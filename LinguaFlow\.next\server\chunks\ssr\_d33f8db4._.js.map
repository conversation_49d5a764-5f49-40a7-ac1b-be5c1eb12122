{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/i18n-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const I18nProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/i18n-context.tsx <module evaluation>\",\n    \"I18nProvider\",\n);\nexport const useI18n = registerClientReference(\n    function() { throw new Error(\"Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/i18n-context.tsx <module evaluation>\",\n    \"useI18n\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/i18n-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const I18nProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/i18n-context.tsx\",\n    \"I18nProvider\",\n);\nexport const useI18n = registerClientReference(\n    function() { throw new Error(\"Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/i18n-context.tsx\",\n    \"useI18n\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/theme-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppearanceProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppearanceProvider() from the server but AppearanceProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx <module evaluation>\",\n    \"AppearanceProvider\",\n);\nexport const useAppearance = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppearance() from the server but useAppearance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx <module evaluation>\",\n    \"useAppearance\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gEACA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/theme-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppearanceProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppearanceProvider() from the server but AppearanceProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx\",\n    \"AppearanceProvider\",\n);\nexport const useAppearance = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppearance() from the server but useAppearance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx\",\n    \"useAppearance\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4CACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/dictionary-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DictionaryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DictionaryProvider() from the server but DictionaryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx <module evaluation>\",\n    \"DictionaryProvider\",\n);\nexport const useDictionary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDictionary() from the server but useDictionary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx <module evaluation>\",\n    \"useDictionary\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/dictionary-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DictionaryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DictionaryProvider() from the server but DictionaryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx\",\n    \"DictionaryProvider\",\n);\nexport const useDictionary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDictionary() from the server but useDictionary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx\",\n    \"useDictionary\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/feature-settings-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureSettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureSettingsProvider() from the server but FeatureSettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"FeatureSettingsProvider\",\n);\nexport const defaultFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultFeatureSettings() from the server but defaultFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"defaultFeatureSettings\",\n);\nexport const useFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureSettings() from the server but useFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"useFeatureSettings\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,2EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,2EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2EACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/feature-settings-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureSettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureSettingsProvider() from the server but FeatureSettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"FeatureSettingsProvider\",\n);\nexport const defaultFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultFeatureSettings() from the server but defaultFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"defaultFeatureSettings\",\n);\nexport const useFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureSettings() from the server but useFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"useFeatureSettings\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,uDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,uDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,uDACA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/layout.tsx"], "sourcesContent": ["\nimport type {Metadata} from 'next';\nimport './globals.css';\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { I18nProvider } from '@/contexts/i18n-context';\nimport { AppearanceProvider } from '@/contexts/theme-context'; // Renamed to AppearanceProvider\nimport { DictionaryProvider } from '@/contexts/dictionary-context';\nimport { FeatureSettingsProvider } from '@/contexts/feature-settings-context';\n\nexport const metadata: Metadata = {\n  title: 'LinguaFlow', \n  description: 'Grammar Correction & Writing Assistant', \n  manifest: '/manifest.json',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    // Class and dir will be set by providers\n    <html lang=\"en\" dir=\"ltr\" suppressHydrationWarning>\n      <head>\n        {/* Favicon and App Icons */}\n        <link rel=\"icon\" href=\"/favicon.ico\" sizes=\"any\" />\n        <link rel=\"icon\" href=\"/favicon.svg\" type=\"image/svg+xml\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n\n        {/* Meta tags for PWA */}\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"LinguaFlow\" />\n\n        {/* Fonts */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap\" rel=\"stylesheet\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;500&display=swap\" rel=\"stylesheet\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Cormorant+SC:wght@500&display=swap\" rel=\"stylesheet\" />\n      </head>\n      <body className=\"font-body antialiased min-h-screen flex flex-col bg-background text-foreground\" suppressHydrationWarning>\n        <AppearanceProvider>\n          <I18nProvider>\n            <DictionaryProvider>\n              <FeatureSettingsProvider>\n                {children}\n                <Toaster />\n              </FeatureSettingsProvider>\n            </DictionaryProvider>\n          </I18nProvider>\n        </AppearanceProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA,uOAA+D,gCAAgC;AAC/F;AACA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,OACE,yCAAyC;kBACzC,8OAAC;QAAK,MAAK;QAAK,KAAI;QAAM,wBAAwB;;0BAChD,8OAAC;;kCAEC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,OAAM;;;;;;kCAC3C,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,MAAK;;;;;;kCAC1C,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAG1B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAGhD,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,MAAK;wBAAmF,KAAI;;;;;;kCAClG,8OAAC;wBAAK,MAAK;wBAAqF,KAAI;;;;;;kCACpG,8OAAC;wBAAK,MAAK;wBAA8E,KAAI;;;;;;;;;;;;0BAE/F,8OAAC;gBAAK,WAAU;gBAAiF,wBAAwB;0BACvH,cAAA,8OAAC,oIAAA,CAAA,qBAAkB;8BACjB,cAAA,8OAAC,mIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,yIAAA,CAAA,qBAAkB;sCACjB,cAAA,8OAAC,kJAAA,CAAA,0BAAuB;;oCACrB;kDACD,8OAAC,mIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}