{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/linguaflow-header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Settings, Moon, Sun, HelpCircle } from 'lucide-react';\nimport { Logo } from '@/components/ui/logo';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\n\ninterface LinguaFlowHeaderProps {\n  onSettingsClick: () => void;\n  onHelpClick: () => void;\n}\n\nexport function LinguaFlowHeader({ onSettingsClick, onHelpClick }: LinguaFlowHeaderProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const isRTL = uiLanguage === 'ar';\n\n  return (\n    <header className=\"sticky top-0 z-50 linguaflow-header shadow-sm\">\n      <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`flex justify-between items-center h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>\n          {/* Logo and Title */}\n          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <Logo className=\"h-8 w-8\" />\n              <h1 \n                className=\"text-xl font-bold\" \n                style={{ color: 'var(--text-primary)' }}\n              >\n                {t('appName')}\n              </h1>\n            </div>\n            \n            {/* Language Selector - Hidden on mobile */}\n            <div className={`hidden md:flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <span \n                className=\"text-sm\" \n                style={{ color: 'var(--text-secondary)' }}\n              >\n                {t('languageLabel')}:\n              </span>\n              <Select value={uiLanguage} onValueChange={setUiLanguage}>\n                <SelectTrigger \n                  className=\"w-auto min-w-[120px] h-8 text-sm linguaflow-input\"\n                  style={{\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-color)',\n                    color: 'var(--text-primary)'\n                  }}\n                >\n                  <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                </SelectTrigger>\n                <SelectContent>\n                  {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                    <SelectItem key={lang.value} value={lang.value}>\n                      {t(lang.labelKey)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className={`flex items-center space-x-2 sm:space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onSettingsClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('settingsTitle')}\n            >\n              <Settings className=\"h-5 w-5\" />\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={effectiveTheme === 'dark' ? t('switchToLightMode') : t('switchToDarkMode')}\n            >\n              {effectiveTheme === 'dark' ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onHelpClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('helpTitle')}\n            >\n              <HelpCircle className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEA;AACA;AACA;AATA;;;;;;;;;AAgBO,SAAS,iBAAiB,EAAE,eAAe,EAAE,WAAW,EAAyB;IACtF,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,QAAQ,eAAe;IAE7B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,qBAAqB,IAAI;;kCAEzF,8OAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;0CAC7E,8OAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;kDAC7E,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAsB;kDAErC,EAAE;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,oBAAoB,IAAI;;kDACvF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAwB;;4CAEvC,EAAE;4CAAiB;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,8OAAC,kIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,OAAO;gDACT;0DAEA,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;kEAC3C,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,8OAAC;wBAAI,WAAW,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,IAAI;;0CAC1F,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,mBAAmB,SAAS,UAAU;gCAC9D,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,mBAAmB,SAAS,EAAE,uBAAuB,EAAE;0CAElE,mBAAmB,uBAClB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/writing-tools-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport {\n  Wrench,\n  FileText,\n  Zap,\n  CheckCircle,\n  Wand2,\n  Lightbulb,\n  Download,\n  Trash2,\n  Bot,\n  Shield,\n  Search,\n  UserCheck\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface WritingToolsSidebarProps {\n  writingMode: string;\n  onWritingModeChange: (mode: string) => void;\n  onFileImport: (file: File) => void;\n  onCheckText: () => void;\n  onAiRewrite: () => void;\n  onBrainstorm: () => void;\n  onExport: () => void;\n  onClear: () => void;\n  onPlagiarismCheck: () => void;\n  onAiWritingDetection: () => void;\n  onHumanizeText: () => void;\n  aiPrompt: string;\n  onAiPromptChange: (prompt: string) => void;\n  onGenerateText: () => void;\n  wordCount: number;\n  charCount: number;\n  errorCount: number;\n  qualityScore: number;\n}\n\nexport function WritingToolsSidebar({\n  writingMode,\n  onWritingModeChange,\n  onFileImport,\n  onCheckText,\n  onAiRewrite,\n  onBrainstorm,\n  onExport,\n  onClear,\n  onPlagiarismCheck,\n  onAiWritingDetection,\n  onHumanizeText,\n  aiPrompt,\n  onAiPromptChange,\n  onGenerateText,\n  wordCount,\n  charCount,\n  errorCount,\n  qualityScore\n}: WritingToolsSidebarProps) {\n  const { t } = useI18n();\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onFileImport(file);\n    }\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Writing Tools Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Wrench className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('writeToolsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Writing Mode */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('writingModeLabel')}\n            </label>\n            <Select value={writingMode} onValueChange={onWritingModeChange}>\n              <SelectTrigger className=\"linguaflow-input\">\n                <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"casual\">📝 {t('casualWritingMode')}</SelectItem>\n                <SelectItem value=\"formal\">👔 {t('formalWritingMode')}</SelectItem>\n                <SelectItem value=\"academic\">🎓 {t('academicWritingMode')}</SelectItem>\n                <SelectItem value=\"creative\">🎨 {t('creativeWritingMode')}</SelectItem>\n                <SelectItem value=\"business\">💼 {t('businessWritingMode')}</SelectItem>\n                <SelectItem value=\"technical\">⚙️ {t('technicalWritingMode')}</SelectItem>\n                <SelectItem value=\"professional\">💼 {t('professionalWritingMode')}</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Import Document */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('importDocumentTitle')}\n            </label>\n            <div \n              className=\"border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400\"\n              style={{ borderColor: 'var(--border-color)' }}\n              onClick={() => document.getElementById('fileInput')?.click()}\n            >\n              <FileText className=\"mx-auto h-8 w-8 mb-2\" style={{ color: 'var(--text-secondary)' }} />\n              <p className=\"text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                {t('dropzoneInstruction')}\n              </p>\n              <input\n                type=\"file\"\n                id=\"fileInput\"\n                className=\"hidden\"\n                accept=\".txt,.md,.docx\"\n                onChange={handleFileChange}\n              />\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('quickActionTitle')}\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onCheckText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <CheckCircle className=\"mr-1 h-4 w-4\" />\n                Check Text\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiRewrite}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Wand2 className=\"mr-1 h-4 w-4\" />\n                AI Rewrite ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onBrainstorm}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Lightbulb className=\"mr-1 h-4 w-4\" />\n                Brainstorm ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onPlagiarismCheck}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Shield className=\"mr-1 h-4 w-4\" />\n                Plagiarism\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiWritingDetection}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Search className=\"mr-1 h-4 w-4\" />\n                AI Detection\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onHumanizeText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <UserCheck className=\"mr-1 h-4 w-4\" />\n                Humanize ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onExport}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Download className=\"mr-1 h-4 w-4\" />\n                Export\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onClear}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Trash2 className=\"mr-1 h-4 w-4\" />\n                {t('clearEditorButton')}\n              </Button>\n            </div>\n          </div>\n\n          {/* AI Text Generation */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              AI Text Generation ✨\n            </label>\n            <Textarea\n              value={aiPrompt}\n              onChange={(e) => onAiPromptChange(e.target.value)}\n              rows={2}\n              className=\"linguaflow-input\"\n              placeholder={t('promptPlaceholder')}\n            />\n            <Button\n              onClick={onGenerateText}\n              className=\"linguaflow-button w-full\"\n              size=\"sm\"\n            >\n              <Bot className=\"mr-1 h-4 w-4\" />\n              Generate with AI ✨\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Writing Statistics Card */}\n      <Card className=\"linguaflow-stats\">\n        <CardContent className=\"p-4\">\n          <h4 className=\"font-semibold text-center mb-3 text-white\">\n            {t('writingStatsTitle')}\n          </h4>\n          <div className=\"space-y-2 text-sm text-white\">\n            <div className=\"flex justify-between items-center\">\n              <span>{t('wordCountLabel')}:</span>\n              <span className=\"font-medium\">{wordCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>{t('charCountLabel')}:</span>\n              <span className=\"font-medium\">{charCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Issues:</span>\n              <span className=\"font-medium\">{errorCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Score:</span>\n              <span className=\"font-medium text-lg\">{qualityScore}%</span>\n            </div>\n          </div>\n          <div className=\"mt-3 linguaflow-progress\">\n            <div \n              className=\"linguaflow-progress-bar\"\n              style={{ width: `${qualityScore}%` }}\n            />\n          </div>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AArBA;;;;;;;;AA4CO,SAAS,oBAAoB,EAClC,WAAW,EACX,mBAAmB,EACnB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,QAAQ,EACR,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACa;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACvE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAa,eAAe;;0DACzC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAY;4DAAI,EAAE;;;;;;;kEACpC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAe;4DAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa;wCAAsB;wCAC5C,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc;;0DAErD,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,OAAO;oDAAE,OAAO;gDAAwB;;;;;;0DACnF,8OAAC;gDAAE,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAwB;0DAC5D,EAAE;;;;;;0DAEL,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,QAAO;gDACP,UAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAAG;;;;;;kDAGlF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,MAAM;wCACN,WAAU;wCACV,aAAa,EAAE;;;;;;kDAEjB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;;0DAEL,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAuB;gDAAa;;;;;;;;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/analysis-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { \n  Lightbulb, \n  MessageCircle, \n  History, \n  Search,\n  Sliders\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\ninterface AnalysisSidebarProps {\n  suggestions: Suggestion[];\n  toneAnalysis: ToneAnalysis;\n  recentPrompts: RecentPrompt[];\n  onApplySuggestion: (suggestionId: string) => void;\n  onAdjustTone: () => void;\n  onUsePrompt: (prompt: string) => void;\n}\n\nexport function AnalysisSidebar({\n  suggestions,\n  toneAnalysis,\n  recentPrompts,\n  onApplySuggestion,\n  onAdjustTone,\n  onUsePrompt\n}: AnalysisSidebarProps) {\n  const { t } = useI18n();\n\n  const getSuggestionTypeColor = (type: string) => {\n    switch (type) {\n      case 'grammar':\n      case 'spelling':\n        return 'var(--error-color)';\n      case 'style':\n        return 'var(--success-color)';\n      case 'clarity':\n        return 'var(--info-color)';\n      case 'tone':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n\n  const getSuggestionTypeIcon = (type: string) => {\n    const color = getSuggestionTypeColor(type);\n    return (\n      <div \n        className=\"w-3 h-3 rounded-full mr-2 flex-shrink-0\"\n        style={{ backgroundColor: color }}\n      />\n    );\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Suggestions Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Lightbulb className=\"mr-2 h-5 w-5\" style={{ color: 'var(--warning-color)' }} />\n            {t('writingSuggestionsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-60 linguaflow-scrollbar\">\n            {suggestions.length > 0 ? (\n              <div className=\"space-y-3\">\n                {suggestions.map((suggestion) => (\n                  <div\n                    key={suggestion.id}\n                    className=\"p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onApplySuggestion(suggestion.id)}\n                  >\n                    <div className=\"flex items-start\">\n                      {getSuggestionTypeIcon(suggestion.type)}\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium\" style={{ color: 'var(--text-primary)' }}>\n                          {suggestion.type.charAt(0).toUpperCase() + suggestion.type.slice(1)}\n                        </p>\n                        <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                          \"{suggestion.text}\" → \"{suggestion.suggestion}\"\n                        </p>\n                        {suggestion.explanation && (\n                          <p className=\"text-xs mt-1 italic\" style={{ color: 'var(--text-secondary)' }}>\n                            {suggestion.explanation}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\" style={{ color: 'var(--text-secondary)' }}>\n                <Search className=\"mx-auto h-8 w-8 mb-2\" />\n                <p className=\"text-sm\">\n                  {t('startTypingForSuggestionsDescription')}\n                </p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n\n      {/* Tone Analysis Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <MessageCircle className=\"mr-2 h-5 w-5\" style={{ color: 'var(--info-color)' }} />\n            {t('aiToneAnalysisTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>Overall Tone:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-accent)' }}>\n                {toneAnalysis.overall}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('formalityLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.formality}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('confidenceLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.confidence}\n              </span>\n            </div>\n          </div>\n          <Button\n            onClick={onAdjustTone}\n            variant=\"secondary\"\n            size=\"sm\"\n            className=\"w-full linguaflow-button-secondary\"\n          >\n            <Sliders className=\"mr-1 h-4 w-4\" />\n            Adjust Tone ✨\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* Recent AI Prompts Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <History className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('generationHistoryTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-40 linguaflow-scrollbar\">\n            {recentPrompts.length > 0 ? (\n              <div className=\"space-y-2\">\n                {recentPrompts.map((prompt) => (\n                  <div\n                    key={prompt.id}\n                    className=\"p-2 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onUsePrompt(prompt.prompt)}\n                  >\n                    <p className=\"text-xs font-medium truncate\" style={{ color: 'var(--text-primary)' }}>\n                      {prompt.prompt}\n                    </p>\n                    <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                      {prompt.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-4\" style={{ color: 'var(--text-secondary)' }}>\n                <p className=\"text-sm\">{t('noGenerationsYetPlaceholder')}</p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;AA4CO,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,aAAa,EACb,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACU;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,uBAAuB;QACrC,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,iBAAiB;YAAM;;;;;;IAGtC;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCAC1E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,YAAY,MAAM,GAAG,kBACpB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,kBAAkB,WAAW,EAAE;kDAE9C,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,sBAAsB,WAAW,IAAI;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAsB;sEACtE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC;;;;;;sEAEnE,8OAAC;4DAAE,WAAU;4DAAe,OAAO;gEAAE,OAAO;4DAAwB;;gEAAG;gEACnE,WAAW,IAAI;gEAAC;gEAAM,WAAW,UAAU;gEAAC;;;;;;;wDAE/C,WAAW,WAAW,kBACrB,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAwB;sEACxE,WAAW,WAAW;;;;;;;;;;;;;;;;;;uCAnB1B,WAAW,EAAE;;;;;;;;;qDA4BxB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;;kDACxE,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAoB;;;;;;gCAC3E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;0DACjD,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAqB;0DAChE,aAAa,OAAO;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAkB;;;;;;;0DACtE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,SAAS;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAmB;;;;;;;0DACvE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,UAAU;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACxE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,cAAc,MAAM,GAAG,kBACtB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,YAAY,OAAO,MAAM;;0DAExC,8OAAC;gDAAE,WAAU;gDAA+B,OAAO;oDAAE,OAAO;gDAAsB;0DAC/E,OAAO,MAAM;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;gDAAe,OAAO;oDAAE,OAAO;gDAAwB;0DACjE,OAAO,SAAS,CAAC,kBAAkB;;;;;;;uCAZjC,OAAO,EAAE;;;;;;;;;qDAkBpB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;0CACxE,cAAA,8OAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/linguaflow-editor.tsx"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\n\n'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport {\n  Undo2,\n  Redo2,\n  Wand2,\n  Circle,\n  X,\n  Bold,\n  Italic,\n  Underline,\n  AlignLeft,\n  AlignCenter,\n  AlignRight,\n  Copy,\n  Sparkles\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface LinguaFlowEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n  isAutoCorrectionEnabled: boolean;\n  onToggleAutoCorrection: () => void;\n  suggestions: any[];\n  onApplySuggestion: (suggestionId: string) => void;\n  direction: 'ltr' | 'rtl';\n  currentLanguage: string;\n  lastSaved?: Date;\n  cursorPosition?: { line: number; col: number };\n  selectionInfo?: string;\n}\n\nexport function LinguaFlowEditor({\n  value,\n  onChange,\n  onUndo,\n  onRedo,\n  canUndo,\n  canRedo,\n  isAutoCorrectionEnabled,\n  onToggleAutoCorrection,\n  suggestions,\n  onApplySuggestion,\n  direction,\n  currentLanguage,\n  lastSaved,\n  cursorPosition = { line: 1, col: 1 },\n  selectionInfo = ''\n}: LinguaFlowEditorProps) {\n  const { t } = useI18n();\n  const editorRef = useRef<HTMLDivElement>(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAiActions, setShowAiActions] = useState(false);\n  const [aiActionPosition, setAiActionPosition] = useState({ x: 0, y: 0 });\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    if (selection && selection.toString().trim()) {\n      const selectedText = selection.toString().trim();\n      setSelectedText(selectedText);\n      \n      // Get selection position for AI actions panel\n      const range = selection.getRangeAt(0);\n      const rect = range.getBoundingClientRect();\n      setAiActionPosition({ x: rect.left, y: rect.bottom + 10 });\n      setShowAiActions(true);\n    } else {\n      setShowAiActions(false);\n      setSelectedText('');\n    }\n  }, []);\n\n  const handleEditorChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {\n    const newValue = e.currentTarget.textContent || '';\n    onChange(newValue);\n  }, [onChange]);\n\n  const aiActions = [\n    { id: 'rewrite-formal', label: 'Formalize', action: () => console.log('Formalize') },\n    { id: 'rewrite-casual', label: 'Casualize', action: () => console.log('Casualize') },\n    { id: 'rewrite-shorter', label: 'Shorten', action: () => console.log('Shorten') },\n    { id: 'rewrite-longer', label: 'Lengthen', action: () => console.log('Lengthen') },\n    { id: 'summarize', label: 'Summarize', action: () => console.log('Summarize') },\n    { id: 'explain', label: 'Explain', action: () => console.log('Explain') },\n  ];\n\n  return (\n    <section className=\"space-y-4\">\n      {/* Main Editor Card */}\n      <Card className=\"linguaflow-card shadow-lg\">\n        <CardHeader className=\"border-b px-4 py-3\" style={{ borderColor: 'var(--border-color)' }}>\n          {/* Top Row - Title and Controls */}\n          <div className=\"flex flex-row items-center justify-between mb-3\">\n            <h3 className=\"font-semibold\" style={{ color: 'var(--text-primary)' }}>\n              Document Editor\n            </h3>\n            <div className=\"flex items-center space-x-2\">\n              {/* Status Indicator */}\n              <div className=\"flex items-center space-x-1 text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                <Circle\n                  className=\"h-3 w-3 fill-current\"\n                  style={{ color: 'var(--success-color)' }}\n                />\n                <span>Ready</span>\n              </div>\n\n              {/* Auto-correction Toggle */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onToggleAutoCorrection}\n                className=\"p-1 rounded\"\n                style={{\n                  color: isAutoCorrectionEnabled ? 'var(--primary-color)' : 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title={isAutoCorrectionEnabled ? 'Auto-correction enabled' : 'Auto-correction disabled'}\n              >\n                <Wand2 className=\"h-4 w-4\" />\n              </Button>\n\n              {/* Undo/Redo */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onUndo}\n                disabled={!canUndo}\n                className=\"p-1 rounded\"\n                style={{\n                  color: 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title=\"Undo (Ctrl+Z)\"\n              >\n                <Undo2 className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onRedo}\n                disabled={!canRedo}\n                className=\"p-1 rounded\"\n                style={{\n                  color: 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title=\"Redo (Ctrl+Y)\"\n              >\n                <Redo2 className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n\n          {/* Formatting Toolbar */}\n          <div className=\"flex items-center space-x-1 border-t pt-3\" style={{ borderColor: 'var(--border-color)' }}>\n            {/* Font Formatting */}\n            <div className=\"flex items-center space-x-1 border-r pr-2\" style={{ borderColor: 'var(--border-color)' }}>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 font-bold\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Bold (Ctrl+B)\"\n              >\n                <Bold className=\"h-4 w-4 mr-1\" />\n                B\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 italic\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Italic (Ctrl+I)\"\n              >\n                <Italic className=\"h-4 w-4 mr-1\" />\n                I\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 underline\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Underline (Ctrl+U)\"\n              >\n                <Underline className=\"h-4 w-4 mr-1\" />\n                U\n              </Button>\n            </div>\n\n            {/* Text Alignment */}\n            <div className=\"flex items-center space-x-1 border-r pr-2\" style={{ borderColor: 'var(--border-color)' }}>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Left\"\n              >\n                <AlignLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Center\"\n              >\n                <AlignCenter className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Right\"\n              >\n                <AlignRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {/* Font Selection */}\n            <select\n              className=\"h-8 px-2 text-sm border rounded\"\n              style={{\n                backgroundColor: 'var(--bg-primary)',\n                borderColor: 'var(--border-color)',\n                color: 'var(--text-primary)'\n              }}\n              title=\"Font Family\"\n            >\n              <option value=\"inter\">Inter</option>\n              <option value=\"arial\">Arial</option>\n              <option value=\"helvetica\">Helvetica</option>\n              <option value=\"times\">Times New Roman</option>\n              <option value=\"georgia\">Georgia</option>\n              <option value=\"courier\">Courier New</option>\n            </select>\n\n            <select\n              className=\"h-8 px-2 text-sm border rounded\"\n              style={{\n                backgroundColor: 'var(--bg-primary)',\n                borderColor: 'var(--border-color)',\n                color: 'var(--text-primary)'\n              }}\n              title=\"Font Size\"\n            >\n              <option value=\"12\">12px</option>\n              <option value=\"14\" selected>14px</option>\n              <option value=\"16\">16px</option>\n              <option value=\"18\">18px</option>\n              <option value=\"20\">20px</option>\n              <option value=\"24\">24px</option>\n            </select>\n          </div>\n        </CardHeader>\n\n        {/* Editor Content */}\n        <div className=\"linguaflow-editor linguaflow-scrollbar\">\n          <div\n            ref={editorRef}\n            contentEditable\n            suppressContentEditableWarning\n            onInput={handleEditorChange}\n            onMouseUp={handleTextSelection}\n            onKeyUp={handleTextSelection}\n            className=\"p-6 min-h-[380px] outline-none\"\n            style={{\n              color: 'var(--text-primary)',\n              lineHeight: '1.75',\n              direction: direction,\n              textAlign: direction === 'rtl' ? 'right' : 'left'\n            }}\n            placeholder={t('editorPlaceholder')}\n            dangerouslySetInnerHTML={{ __html: value }}\n          />\n        </div>\n\n        {/* Editor Footer */}\n        <div\n          className=\"border-t px-4 py-2 flex items-center justify-between text-xs\"\n          style={{\n            borderColor: 'var(--border-color)',\n            backgroundColor: 'var(--bg-alt)',\n            color: 'var(--text-secondary)'\n          }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <span>\n              Last saved: <span className=\"font-medium\">\n                {isClient && lastSaved ? lastSaved.toLocaleTimeString() : 'Never'}\n              </span>\n            </span>\n            <span>\n              Language: <span className=\"font-medium\">{currentLanguage}</span>\n            </span>\n\n            {/* Smart Synonyms and Copy Text Buttons */}\n            <div className=\"flex items-center space-x-2 ml-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 px-2 text-xs\"\n                style={{ color: 'var(--primary-color)' }}\n                title=\"Smart Synonyms\"\n              >\n                <Sparkles className=\"h-3 w-3 mr-1\" />\n                Smart Synonyms\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 px-2 text-xs\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Copy Text\"\n              >\n                <Copy className=\"h-3 w-3 mr-1\" />\n                Copy Text\n              </Button>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {selectionInfo && <span>{selectionInfo}</span>}\n            <span>Line {cursorPosition.line}, Col {cursorPosition.col}</span>\n          </div>\n        </div>\n      </Card>\n\n      {/* AI Context Panel */}\n      {showAiActions && selectedText && (\n        <Card \n          className=\"linguaflow-card animate-in fade-in-0 zoom-in-95\"\n          style={{\n            position: 'fixed',\n            top: aiActionPosition.y,\n            left: aiActionPosition.x,\n            zIndex: 1000,\n            maxWidth: '400px'\n          }}\n        >\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-semibold flex items-center text-sm\" style={{ color: 'var(--primary-color)' }}>\n                <Wand2 className=\"mr-2 h-4 w-4\" />\n                AI Actions for Selection ✨\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setShowAiActions(false)}\n                className=\"h-6 w-6 p-1\"\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <p \n              className=\"text-sm italic p-2 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n            >\n              \"{selectedText}\"\n            </p>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {aiActions.map((action) => (\n                <Button\n                  key={action.id}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={action.action}\n                  className=\"linguaflow-button-secondary text-xs\"\n                >\n                  {action.label}\n                </Button>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAKA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AApBA;;;;;;AAwCO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,uBAAuB,EACvB,sBAAsB,EACtB,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,eAAe,EACf,SAAS,EACT,iBAAiB;IAAE,MAAM;IAAG,KAAK;AAAE,CAAC,EACpC,gBAAgB,EAAE,EACI;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,YAAY,OAAO,YAAY;QACrC,IAAI,aAAa,UAAU,QAAQ,GAAG,IAAI,IAAI;YAC5C,MAAM,eAAe,UAAU,QAAQ,GAAG,IAAI;YAC9C,gBAAgB;YAEhB,8CAA8C;YAC9C,MAAM,QAAQ,UAAU,UAAU,CAAC;YACnC,MAAM,OAAO,MAAM,qBAAqB;YACxC,oBAAoB;gBAAE,GAAG,KAAK,IAAI;gBAAE,GAAG,KAAK,MAAM,GAAG;YAAG;YACxD,iBAAiB;QACnB,OAAO;YACL,iBAAiB;YACjB,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI;QAChD,SAAS;IACX,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAmB,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;QAChF;YAAE,IAAI;YAAkB,OAAO;YAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAY;QACjF;YAAE,IAAI;YAAa,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;KACzE;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAqB,OAAO;4BAAE,aAAa;wBAAsB;;0CAErF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;wCAAgB,OAAO;4CAAE,OAAO;wCAAsB;kDAAG;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;gDAAsC,OAAO;oDAAE,OAAO;gDAAwB;;kEAC3F,8OAAC,sMAAA,CAAA,SAAM;wDACL,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAuB;;;;;;kEAEzC,8OAAC;kEAAK;;;;;;;;;;;;0DAIR,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;gDACV,OAAO;oDACL,OAAO,0BAA0B,yBAAyB;oDAC1D,iBAAiB;gDACnB;gDACA,OAAO,0BAA0B,4BAA4B;0DAE7D,cAAA,8OAAC,+MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAInB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,OAAM;0DAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,OAAM;0DAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC;gCAAI,WAAU;gCAA4C,OAAO;oCAAE,aAAa;gCAAsB;;kDAErG,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,aAAa;wCAAsB;;0DACrG,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAM1C,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,aAAa;wCAAsB;;0DACrG,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,OAAO;wCACT;wCACA,OAAM;;0DAEN,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,OAAO;wCACT;wCACA,OAAM;;0DAEN,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;gDAAK,QAAQ;0DAAC;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,eAAe;4BACf,8BAA8B;4BAC9B,SAAS;4BACT,WAAW;4BACX,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,YAAY;gCACZ,WAAW;gCACX,WAAW,cAAc,QAAQ,UAAU;4BAC7C;4BACA,aAAa,EAAE;4BACf,yBAAyB;gCAAE,QAAQ;4BAAM;;;;;;;;;;;kCAK7C,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,iBAAiB;4BACjB,OAAO;wBACT;;0CAEA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;0DACQ,8OAAC;gDAAK,WAAU;0DACzB,YAAY,YAAY,UAAU,kBAAkB,KAAK;;;;;;;;;;;;kDAG9D,8OAAC;;4CAAK;0DACM,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAI3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAuB;gDACvC,OAAM;;kEAEN,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;oCACZ,+BAAiB,8OAAC;kDAAM;;;;;;kDACzB,8OAAC;;4CAAK;4CAAM,eAAe,IAAI;4CAAC;4CAAO,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAM9D,iBAAiB,8BAChB,8OAAC,gIAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK,iBAAiB,CAAC;oBACvB,MAAM,iBAAiB,CAAC;oBACxB,QAAQ;oBACR,UAAU;gBACZ;;kCAEA,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAA0C,OAAO;wCAAE,OAAO;oCAAuB;;sDAC7F,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;;oCACD;oCACG;oCAAa;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,uBACd,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,OAAO,MAAM;wCACtB,WAAU;kDAET,OAAO,KAAK;uCANR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/new-design.tsx"], "sourcesContent": ["import { useState, useEffect, useMemo, useCallback } from 'react';\n'use client';\n\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\nimport { LinguaFlowHeader } from '@/components/layout/linguaflow-header';\nimport { WritingToolsSidebar } from '@/components/layout/writing-tools-sidebar';\nimport { AnalysisSidebar } from '@/components/layout/analysis-sidebar';\nimport { LinguaFlowEditor } from '@/components/editor/linguaflow-editor';\nimport { SettingsModal } from '@/components/modals/settings-modal';\nimport { HelpModal } from '@/components/modals/help-modal';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\nfunction NewDesignPage() {\n  const { t, uiLanguage, writingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n  const isRTL = uiLanguage === 'ar';\n\n  // Editor state\n  const [editorValue, setEditorValue] = useState('');\n  const [writingMode, setWritingMode] = useState('formal');\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const [isAutoCorrectionEnabled, setIsAutoCorrectionEnabled] = useState(true);\n  const [aiPrompt, setAiPrompt] = useState('');\n\n  // Statistics\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [errorCount, setErrorCount] = useState(0);\n  const [qualityScore, setQualityScore] = useState(100);\n\n  // Analysis data\n  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);\n  const [toneAnalysis, setToneAnalysis] = useState<ToneAnalysis>({\n    overall: 'Neutral',\n    formality: 'Professional (75%)',\n    confidence: 'Confident (80%)'\n  });\n  const [recentPrompts, setRecentPrompts] = useState<RecentPrompt[]>([]);\n\n  // Modal states\n  const [showSettings, setShowSettings] = useState(false);\n  const [showHelp, setShowHelp] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | undefined>(undefined);\n\n  // Set lastSaved on client side only\n  useEffect(() => {\n    setLastSaved(new Date());\n  }, []);\n\n  // Update statistics when editor value changes\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(word => word.length > 0);\n    setWordCount(words.length);\n    setCharCount(editorValue.length);\n    \n    // Mock error count and quality score\n    const errors = Math.max(0, Math.floor(words.length * 0.05) - Math.floor(Math.random() * 3));\n    setErrorCount(errors);\n    setQualityScore(Math.max(60, 100 - errors * 5));\n  }, [editorValue]);\n\n  // Handlers\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n    setLastSaved(new Date());\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    // Implement undo logic\n    console.log('Undo');\n  }, []);\n\n  const handleRedo = useCallback(() => {\n    // Implement redo logic\n    console.log('Redo');\n  }, []);\n\n  const handleFileImport = useCallback((file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      setEditorValue(content);\n      toast({\n        title: t('toastFileImportSuccessTitle'),\n        description: t('toastFileImportSuccessMessage'),\n      });\n    };\n    reader.readAsText(file);\n  }, [toast, t]);\n\n  const handleCheckText = useCallback(() => {\n    // Mock suggestions\n    const mockSuggestions: Suggestion[] = [\n      {\n        id: '1',\n        type: 'grammar',\n        text: 'are',\n        suggestion: 'is',\n        explanation: 'Subject-verb agreement error'\n      },\n      {\n        id: '2',\n        type: 'style',\n        text: 'very good',\n        suggestion: 'excellent',\n        explanation: 'More precise word choice'\n      }\n    ];\n    setSuggestions(mockSuggestions);\n    toast({\n      title: 'Text Analysis Complete',\n      description: `Found ${mockSuggestions.length} suggestions`,\n    });\n  }, [toast]);\n\n  const handleAiRewrite = useCallback(() => {\n    toast({\n      title: 'AI Rewrite',\n      description: 'AI rewrite feature activated',\n    });\n  }, [toast]);\n\n  const handleBrainstorm = useCallback(() => {\n    toast({\n      title: 'Brainstorm',\n      description: 'Brainstorm feature activated',\n    });\n  }, [toast]);\n\n  const handleExport = useCallback(() => {\n    const blob = new Blob([editorValue], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'document.txt';\n    a.click();\n    URL.revokeObjectURL(url);\n    toast({\n      title: 'Export Complete',\n      description: 'Document exported successfully',\n    });\n  }, [editorValue, toast]);\n\n  const handleClear = useCallback(() => {\n    setEditorValue('');\n    setSuggestions([]);\n    toast({\n      title: t('toastEditorClearedSuccess'),\n    });\n  }, [toast, t]);\n\n  const handleGenerateText = useCallback(() => {\n    if (!aiPrompt.trim()) {\n      toast({\n        title: t('toastInputRequiredTitle'),\n        description: t('toastPromptRequiredError'),\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    // Mock text generation\n    const generatedText = `Generated text based on: \"${aiPrompt}\"`;\n    setEditorValue(prev => prev + (prev ? '\\n\\n' : '') + generatedText);\n    \n    // Add to recent prompts\n    const newPrompt: RecentPrompt = {\n      id: Date.now().toString(),\n      prompt: aiPrompt,\n      timestamp: new Date()\n    };\n    setRecentPrompts(prev => [newPrompt, ...prev.slice(0, 4)]);\n    setAiPrompt('');\n    \n    toast({\n      title: t('toastTextGeneratedSuccess'),\n    });\n  }, [aiPrompt, toast, t]);\n\n  const handleApplySuggestion = useCallback((suggestionId: string) => {\n    const suggestion = suggestions.find(s => s.id === suggestionId);\n    if (suggestion) {\n      const newValue = editorValue.replace(suggestion.text, suggestion.suggestion);\n      setEditorValue(newValue);\n      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n      toast({\n        title: t('toastSuggestionAppliedSuccess'),\n      });\n    }\n  }, [suggestions, editorValue, toast, t]);\n\n  const handleAdjustTone = useCallback(() => {\n    toast({\n      title: 'Tone Adjustment',\n      description: 'Tone adjustment feature activated',\n    });\n  }, [toast]);\n\n  const handleUsePrompt = useCallback((prompt: string) => {\n    setAiPrompt(prompt);\n  }, []);\n\n  const handlePlagiarismCheck = useCallback(() => {\n    toast({\n      title: 'Plagiarism Detection',\n      description: 'Plagiarism detection feature activated',\n    });\n  }, [toast]);\n\n  const handleAiWritingDetection = useCallback(() => {\n    toast({\n      title: 'AI Writing Detection',\n      description: 'AI writing detection feature activated',\n    });\n  }, [toast]);\n\n  const handleHumanizeText = useCallback(() => {\n    toast({\n      title: 'Humanize Text',\n      description: 'Text humanization feature activated',\n    });\n  }, [toast]);\n\n  const writingDirection = writingLanguageDialect?.startsWith('ar') ? 'rtl' : 'ltr';\n  const currentLanguage = writingLanguageDialect || 'English';\n\n  return (\n    <div \n      className=\"min-h-screen transition-all duration-300\"\n      style={{ \n        backgroundColor: 'var(--bg-primary)',\n        color: 'var(--text-primary)'\n      }}\n    >\n      {/* Header */}\n      <LinguaFlowHeader\n        onSettingsClick={() => setShowSettings(true)}\n        onHelpClick={() => setShowHelp(true)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className={`grid grid-cols-1 lg:grid-cols-12 gap-6 ${isRTL ? 'direction-rtl' : ''}`}>\n          {/* Left Sidebar - Writing Tools */}\n          <div className=\"lg:col-span-3\">\n            <WritingToolsSidebar\n              writingMode={writingMode}\n              onWritingModeChange={setWritingMode}\n              onFileImport={handleFileImport}\n              onCheckText={handleCheckText}\n              onAiRewrite={handleAiRewrite}\n              onBrainstorm={handleBrainstorm}\n              onExport={handleExport}\n              onClear={handleClear}\n              onPlagiarismCheck={handlePlagiarismCheck}\n              onAiWritingDetection={handleAiWritingDetection}\n              onHumanizeText={handleHumanizeText}\n              aiPrompt={aiPrompt}\n              onAiPromptChange={setAiPrompt}\n              onGenerateText={handleGenerateText}\n              wordCount={wordCount}\n              charCount={charCount}\n              errorCount={errorCount}\n              qualityScore={qualityScore}\n            />\n          </div>\n\n          {/* Center - Editor */}\n          <div className=\"lg:col-span-6\">\n            <LinguaFlowEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n              isAutoCorrectionEnabled={isAutoCorrectionEnabled}\n              onToggleAutoCorrection={() => setIsAutoCorrectionEnabled(!isAutoCorrectionEnabled)}\n              suggestions={suggestions}\n              onApplySuggestion={handleApplySuggestion}\n              direction={writingDirection}\n              currentLanguage={currentLanguage}\n              lastSaved={lastSaved}\n            />\n          </div>\n\n          {/* Right Sidebar - Analysis */}\n          <div className=\"lg:col-span-3\">\n            <AnalysisSidebar\n              suggestions={suggestions}\n              toneAnalysis={toneAnalysis}\n              recentPrompts={recentPrompts}\n              onApplySuggestion={handleApplySuggestion}\n              onAdjustTone={handleAdjustTone}\n              onUsePrompt={handleUsePrompt}\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default NewDesignPage;\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;AACA;AACA;AAGA;AACA;AAEA;;;AAZA;;;;;;;;;AAkCA,SAAS;IACP,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACtC,MAAM,QAAQ,eAAe;IAE7B,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,SAAS;QACT,WAAW;QACX,YAAY;IACd;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAE7D,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,IAAI;IACnB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAC3E,aAAa,MAAM,MAAM;QACzB,aAAa,YAAY,MAAM;QAE/B,qCAAqC;QACrC,MAAM,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxF,cAAc;QACd,gBAAgB,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS;IAC9C,GAAG;QAAC;KAAY;IAEhB,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;QACf,aAAa,IAAI;IACnB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,eAAe;YACf,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;YACjB;QACF;QACA,OAAO,UAAU,CAAC;IACpB,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,mBAAmB;QACnB,MAAM,kBAAgC;YACpC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;SACD;QACD,eAAe;QACf,MAAM;YACJ,OAAO;YACP,aAAa,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;QAC5D;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;QACpB,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,eAAe;QACf,eAAe,EAAE;QACjB,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,uBAAuB;QACvB,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QAC9D,eAAe,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;QAErD,wBAAwB;QACxB,MAAM,YAA0B;YAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ;YACR,WAAW,IAAI;QACjB;QACA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAc,KAAK,KAAK,CAAC,GAAG;aAAG;QACzD,YAAY;QAEZ,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAU;QAAO;KAAE;IAEvB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,YAAY;YACd,MAAM,WAAW,YAAY,OAAO,CAAC,WAAW,IAAI,EAAE,WAAW,UAAU;YAC3E,eAAe;YACf,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACjD,MAAM;gBACJ,OAAO,EAAE;YACX;QACF;IACF,GAAG;QAAC;QAAa;QAAa;QAAO;KAAE;IAEvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,wBAAwB,WAAW,QAAQ,QAAQ;IAC5E,MAAM,kBAAkB,0BAA0B;IAElD,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;0BAGA,8OAAC,oJAAA,CAAA,mBAAgB;gBACf,iBAAiB,IAAM,gBAAgB;gBACvC,aAAa,IAAM,YAAY;;;;;;0BAIjC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,kBAAkB,IAAI;;sCAEtF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,sBAAmB;gCAClB,aAAa;gCACb,qBAAqB;gCACrB,cAAc;gCACd,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,UAAU;gCACV,SAAS;gCACT,mBAAmB;gCACnB,sBAAsB;gCACtB,gBAAgB;gCAChB,UAAU;gCACV,kBAAkB;gCAClB,gBAAgB;gCAChB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,cAAc;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oJAAA,CAAA,mBAAgB;gCACf,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,SAAS;gCACT,SAAS;gCACT,yBAAyB;gCACzB,wBAAwB,IAAM,2BAA2B,CAAC;gCAC1D,aAAa;gCACb,mBAAmB;gCACnB,WAAW;gCACX,iBAAiB;gCACjB,WAAW;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mJAAA,CAAA,kBAAe;gCACd,aAAa;gCACb,cAAc;gCACd,eAAe;gCACf,mBAAmB;gCACnB,cAAc;gCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport NewDesignPage from \"./new-design\";\n\nexport default function LinguaFlowPageContainer() {\n  return <NewDesignPage />;\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,4HAAA,CAAA,UAAa;;;;;AACvB", "debugId": null}}]}