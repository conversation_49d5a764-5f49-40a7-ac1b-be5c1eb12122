'use client';

import type { ReactNode } from 'react';
import React, { createContext, useState, useEffect, useCallback, useContext } from 'react';
import { APP_SUPPORTED_UI_LANGUAGES, APP_WRITING_LANGUAGES, type ProficiencyLevel } from '@/config/languages';


import enTranslationsJson from '@/locales/en.json';
import arTranslationsJson from '@/locales/ar.json';
import trTranslationsJson from '@/locales/tr.json';
import esTranslationsJson from '@/locales/es.json';
import deTranslationsJson from '@/locales/de.json';
import frTranslationsJson from '@/locales/fr.json';
import nlTranslationsJson from '@/locales/nl.json';
import itTranslationsJson from '@/locales/it.json';

type Translations = Record<string, string>;


const getSafeTranslations = (json: any): Translations => {
  if (json && typeof json === 'object' && Object.keys(json).length > 0) {
    return json as Translations;
  }
  return {}; 
};

const allTranslationsData: Record<string, Translations> = {
  'en': getSafeTranslations(enTranslationsJson),
  'ar': getSafeTranslations(arTranslationsJson),
  'tr': getSafeTranslations(trTranslationsJson),
  'es': getSafeTranslations(esTranslationsJson),
  'de': getSafeTranslations(deTranslationsJson),
  'fr': getSafeTranslations(frTranslationsJson),
  'nl': getSafeTranslations(nlTranslationsJson),
  'it': getSafeTranslations(itTranslationsJson),
};

const DEFAULT_UI_LANGUAGE = 'en';
const DEFAULT_WRITING_LANGUAGE_DIALECT = 'en-US'; // Store full dialect
const DEFAULT_PROFICIENCY: ProficiencyLevel = 'native';

export interface I18nContextType {
  uiLanguage: string;
  setUiLanguage: (lang: string) => void;
  writingLanguageDialect: string; // Stores full dialect e.g. "en-US", "es-ES"
  setWritingLanguageDialect: (dialect: string) => void;
  getWritingLanguageBase: () => string; // Helper to get "en" from "en-US"
  languageProficiency: Record<string, ProficiencyLevel>; // Keyed by base language e.g. "en"
  setLanguageProficiency: (baseLang: string, level: ProficiencyLevel) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function I18nProvider({ children }: { children: ReactNode }) {
  const [uiLanguage, setUiLanguageState] = useState<string>(DEFAULT_UI_LANGUAGE);
  const [translations, setTranslations] = useState<Translations>(allTranslationsData[DEFAULT_UI_LANGUAGE]);
  const [writingLanguageDialect, setWritingLanguageDialectState] = useState<string>(DEFAULT_WRITING_LANGUAGE_DIALECT);
  const [languageProficiency, setLanguageProficiencyState] = useState<Record<string, ProficiencyLevel>>({});

  const updateDocumentAttributes = (langCode: string) => {
    const langInfo = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === langCode) || APP_SUPPORTED_UI_LANGUAGES[0];
    document.documentElement.lang = langCode;
    document.documentElement.dir = langInfo?.dir || 'ltr';
  };

  useEffect(() => {
    // Load UI Language from localStorage or browser settings
    const storedUiLanguage = localStorage.getItem('lingua-flow-ui-language');
    let initialLang = storedUiLanguage;

    if (!initialLang) {
        const browserLang = (navigator.languages ? navigator.languages[0] : navigator.language) || DEFAULT_UI_LANGUAGE;
        const matchingSupportedLang = APP_SUPPORTED_UI_LANGUAGES.find(lang => lang.value === browserLang || browserLang.startsWith(lang.value));
        initialLang = matchingSupportedLang ? matchingSupportedLang.value : DEFAULT_UI_LANGUAGE;
    }
    
    // Set initial language state
    setUiLanguage(initialLang);

    // Load Writing Language Dialect
    const storedWritingLangDialect = localStorage.getItem('lingua-flow-writing-language-dialect');
    setWritingLanguageDialectState(storedWritingLangDialect || DEFAULT_WRITING_LANGUAGE_DIALECT);

    // Load Language Proficiency
    const storedProficiency = localStorage.getItem('lingua-flow-language-proficiency');
    if (storedProficiency) {
      try {
        setLanguageProficiencyState(JSON.parse(storedProficiency));
      } catch (e) {
        localStorage.removeItem('lingua-flow-language-proficiency'); // Clear if invalid
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setUiLanguage = useCallback((lang: string) => {
    const validLang = allTranslationsData[lang] ? lang : DEFAULT_UI_LANGUAGE;
    
    setUiLanguageState(validLang);
    setTranslations(allTranslationsData[validLang]);
    localStorage.setItem('lingua-flow-ui-language', validLang);
    updateDocumentAttributes(validLang);
  }, []);

  const setWritingLanguageDialect = useCallback((dialect: string) => {
    setWritingLanguageDialectState(dialect);
    localStorage.setItem('lingua-flow-writing-language-dialect', dialect);
  }, []);

  const getWritingLanguageBase = useCallback((): string => {
    return writingLanguageDialect.split('-')[0];
  }, [writingLanguageDialect]);

  const setLanguageProficiency = useCallback((baseLang: string, level: ProficiencyLevel) => {
    setLanguageProficiencyState(prev => {
      const newProficiency = { ...prev, [baseLang]: level };
      localStorage.setItem('lingua-flow-language-proficiency', JSON.stringify(newProficiency));
      return newProficiency;
    });
  }, []);

  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    let mappedString = translations[key] !== undefined ? translations[key] : key;
    if (params) {
      Object.keys(params).forEach(paramKey => {
        mappedString = mappedString.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(params[paramKey]));
      });
    }
    return mappedString;
  }, [translations]);

  return (
    <I18nContext.Provider value={{ uiLanguage, setUiLanguage, writingLanguageDialect, setWritingLanguageDialect, getWritingLanguageBase, languageProficiency, setLanguageProficiency, t }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n(): I18nContextType {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}
