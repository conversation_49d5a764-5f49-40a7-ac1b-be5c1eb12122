
@tailwind base;
@tailwind components;
@tailwind utilities;

/* LinguaFlow Design System - Blue Theme */
:root {
  /* Primary Colors */
  --primary-color: #2563eb; /* Blue 600 */
  --secondary-color: #1d4ed8; /* Blue 700 */
  --accent-color: #3b82f6; /* Blue 500 */

  /* Status Colors */
  --error-color: #dc2626; /* Red 600 */
  --warning-color: #f59e0b; /* Amber 500 */
  --success-color: #16a34a; /* Green 600 */
  --info-color: #3b82f6; /* Blue 500 */

  /* Text Colors */
  --text-primary: #1f2937; /* Gray 800 */
  --text-secondary: #6b7280; /* Gray 500 */
  --text-accent: var(--primary-color);

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb; /* Gray 50 */
  --bg-alt: #f3f4f6; /* Gray 100 */
  --border-color: #e5e7eb; /* Gray 200 */

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Dark Theme */
[data-theme="dark"] {
  --primary-color: #3b82f6; /* Blue 500 */
  --secondary-color: #2563eb; /* Blue 600 */
  --accent-color: #60a5fa; /* Blue 400 */

  --text-primary: #f9fafb; /* Gray 50 */
  --text-secondary: #9ca3af; /* Gray 400 */
  --text-accent: var(--primary-color);

  --bg-primary: #111827; /* Gray 900 */
  --bg-secondary: #1f2937; /* Gray 800 */
  --bg-alt: #374151; /* Gray 700 */
  --border-color: #374151; /* Gray 700 */
}

/* High Contrast Light Theme */
.high-contrast-mode[data-theme="light"] {
  --primary-color: #0000FF;
  --secondary-color: #0000AA;
  --accent-color: #0000FF;
  --error-color: #FF0000;
  --warning-color: #FFA500;
  --success-color: #008000;
  --info-color: #0000FF;

  --text-primary: #000000;
  --text-secondary: #000000;
  --text-accent: #0000FF;

  --bg-primary: #FFFFFF;
  --bg-secondary: #F0F0F0;
  --bg-alt: #E0E0E0;
  --border-color: #000000;
}

/* High Contrast Dark Theme */
.high-contrast-mode[data-theme="dark"] {
  --primary-color: #FFFF00;
  --secondary-color: #DDDD00;
  --accent-color: #FFFF00;
  --error-color: #FF8888;
  --warning-color: #FFDD88;
  --success-color: #88FF88;
  --info-color: #88FFFF;

  --text-primary: #FFFFFF;
  --text-secondary: #FFFFFF;
  --text-accent: #FFFF00;

  --bg-primary: #000000;
  --bg-secondary: #222222;
  --bg-alt: #333333;
  --border-color: #FFFFFF;
}

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 210 40% 98%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 210 40% 98%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 205 92% 55%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --chart-1: 221 83% 53%;
    --chart-2: 161 74% 40%;
    --chart-3: 39 90% 50%;
    --chart-4: 298 70% 50%;
    --chart-5: 48 96% 62%;
    
    --sidebar-background: 210 40% 96.1%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 205 92% 55%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 90%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 222.2 84% 4.9%;

    --font-size-base: 1rem; 
    --font-size-sm: 0.875rem; 
    --font-size-xs: 0.75rem; 
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem; 
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 205 92% 55%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --chart-1: 221 83% 63%;
    --chart-2: 161 74% 50%;
    --chart-3: 39 90% 60%;
    --chart-4: 298 70% 60%;
    --chart-5: 48 96% 72%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 215 20.2% 65.1%;
  }

  html.high-contrast {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%; 
    --primary-foreground: 0 0% 100%; 
    --secondary: 0 0% 85%; 
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 90%;
    --muted-foreground: 0 0% 20%;
    --accent: 0 0% 85%; 
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 35%; 
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 0%; 
    --input: 0 0% 100%;
    --input-border: 0 0% 0%; 
    --ring: 0 0% 0%; 
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 85%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 0%;
    --sidebar-ring: 0 0% 0%;
  }

  html.high-contrast.dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%; 
    --primary-foreground: 0 0% 0%; 
    --secondary: 0 0% 20%; 
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 85%;
    --accent: 0 0% 20%; 
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 70%; 
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 100%; 
    --input: 0 0% 0%;
    --input-border: 0 0% 100%;
    --ring: 0 0% 100%;
    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 20%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 100%;
    --sidebar-ring: 0 0% 100%;
  }

   html.high-contrast input,
   html.high-contrast textarea,
   html.high-contrast select,
   html.high-contrast button {
      border: 1px solid hsl(var(--border)) !important;
   }
    html.high-contrast [role="button"] {
       border: 1px solid hsl(var(--border)) !important;
    }

}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-size: var(--font-size-base);
    font-family: var(--font-sans);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

   .high-contrast .border-input {
      border-color: hsl(var(--border)) !important;
   }

  /* LinguaFlow Component Styles */
  .linguaflow-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .linguaflow-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(8px);
  }

  .linguaflow-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .linguaflow-button:hover {
    background-color: var(--secondary-color);
  }

  .linguaflow-button-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }

  .linguaflow-button-secondary:hover {
    background-color: var(--bg-alt);
  }

  .linguaflow-input {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    transition: border-color 0.2s ease;
  }

  .linguaflow-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px var(--primary-color);
  }

  .linguaflow-editor {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    min-height: 400px;
    max-height: 70vh;
    overflow-y: auto;
  }

  /* Placeholder for contentEditable elements */
  .linguaflow-editor [contenteditable]:empty:before {
    content: attr(data-placeholder);
    color: var(--text-secondary);
    pointer-events: none;
    opacity: 0.6;
  }

  .linguaflow-stats {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 0.5rem;
    padding: 1rem;
  }

  /* Theme-aware body styling */
  body[data-theme="light"] {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  body[data-theme="dark"] {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .linguaflow-progress {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    height: 0.5rem;
  }

  .linguaflow-progress-bar {
    background: linear-gradient(90deg, var(--accent-color), white);
    height: 100%;
    border-radius: 9999px;
    transition: width 0.3s ease;
  }
}

@layer components {
  .suggestion-highlight {
    @apply cursor-pointer rounded-[2px] pointer-events-auto;
  }

  /* Ensure perfect text alignment between backdrop and textarea */
  .editor-backdrop, .editor-textarea {
    font-variant-ligatures: none;
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: normal;
    font-kerning: none;
  }

  /* RTL Layout Support */
  [dir="rtl"] {
    text-align: right;
  }

  /* RTL Flex containers - only apply to specific components */
  [dir="rtl"] .rtl-flex-reverse {
    flex-direction: row-reverse;
  }

  /* RTL Spacing utilities */
  [dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  [dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  [dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  /* RTL Margin utilities */
  [dir="rtl"] .ml-auto {
    margin-left: auto;
    margin-right: 0;
  }

  [dir="rtl"] .mr-auto {
    margin-right: auto;
    margin-left: 0;
  }

  [dir="rtl"] .ml-2 {
    margin-left: 0;
    margin-right: 0.5rem;
  }

  [dir="rtl"] .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
  }

  [dir="rtl"] .ml-4 {
    margin-left: 0;
    margin-right: 1rem;
  }

  [dir="rtl"] .mr-4 {
    margin-right: 0;
    margin-left: 1rem;
  }

  /* RTL Padding utilities */
  [dir="rtl"] .pl-2 {
    padding-left: 0;
    padding-right: 0.5rem;
  }

  [dir="rtl"] .pr-2 {
    padding-right: 0;
    padding-left: 0.5rem;
  }

  [dir="rtl"] .pl-4 {
    padding-left: 0;
    padding-right: 1rem;
  }

  [dir="rtl"] .pr-4 {
    padding-right: 0;
    padding-left: 1rem;
  }

  /* RTL Text input alignment */
  [dir="rtl"] input[type="text"],
  [dir="rtl"] textarea,
  [dir="rtl"] .text-input {
    text-align: right;
    direction: rtl;
  }

  /* RTL Dropdown and Select alignment */
  [dir="rtl"] select,
  [dir="rtl"] .select-trigger {
    text-align: right;
    direction: rtl;
  }

  /* RTL Button layouts */
  [dir="rtl"] .flex.items-center.gap-2 {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .flex.items-center.gap-3 {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .flex.items-center.justify-between {
    flex-direction: row-reverse;
  }

  /* RTL Tab layouts */
  [dir="rtl"] .grid.grid-cols-2,
  [dir="rtl"] .grid.grid-cols-3 {
    direction: rtl;
  }

  [dir="rtl"] .grid.grid-cols-2 > *,
  [dir="rtl"] .grid.grid-cols-3 > * {
    text-align: right;
  }

  /* RTL Card and Panel layouts */
  [dir="rtl"] .card-header {
    text-align: right;
  }

  [dir="rtl"] .card-content {
    text-align: right;
  }

  /* RTL Sidebar and navigation */
  [dir="rtl"] .sidebar-menu {
    text-align: right;
  }

  [dir="rtl"] .sidebar-menu-button {
    flex-direction: row-reverse;
    text-align: right;
  }

  /* Arabic font optimization */
  [dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* RTL Editor specific styles */
  [dir="rtl"] .editor-backdrop,
  [dir="rtl"] .editor-textarea {
    text-align: right;
    direction: rtl;
  }

  /* RTL Suggestion highlights */
  [dir="rtl"] .suggestion-highlight {
    direction: rtl;
  }

  /* RTL Tooltip and Popover positioning */
  [dir="rtl"] .tooltip {
    left: auto;
    right: 0;
  }

  [dir="rtl"] .popover {
    left: auto;
    right: 0;
  }

  /* RTL Icon positioning */
  [dir="rtl"] .icon-left {
    order: 2;
  }

  [dir="rtl"] .icon-right {
    order: -1;
  }
  .suggestion-spelling, .suggestion-grammar {
    background-color: transparent;
    border-bottom: 2px solid hsl(var(--destructive));
  }
  .suggestion-rewrite {
    background-color: transparent;
    border-bottom: 2px solid hsl(var(--chart-1));
  }
  .suggestion-style {
    background-color: transparent;
    border-bottom: 2px solid hsl(var(--chart-2));
  }
  .suggestion-plagiarism {
    background-color: hsla(var(--destructive), 0.25);
    border-bottom: 2px solid hsl(var(--destructive));
    font-weight: 500;
  }
}
