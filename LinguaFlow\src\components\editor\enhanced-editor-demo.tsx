'use client';

import React, { useState } from 'react';
import { EnhancedTextEditor, createSampleSuggestions } from './enhanced-text-editor';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export function EnhancedEditorDemo() {
  const [editorValue, setEditorValue] = useState(
    `This is a sample text with teh word "very good" repeated multiple times. I are writing to show how the enhanced editor works. The text is very good and the features are very good too. We use the word "use" multiple times and we use it again. We also use "make" to make things and make improvements. The system will make suggestions to make your writing better.`
  );
  
  const [history, setHistory] = useState<string[]>([editorValue]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const { toast } = useToast();

  // Generate sample suggestions based on current text
  const { suggestions, plagiarismSources, repeatedVerbSources } = createSampleSuggestions(editorValue);

  const handleEditorChange = (newValue: string) => {
    setEditorValue(newValue);
    
    // Add to history for undo/redo
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newValue);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setEditorValue(history[newIndex]);
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setEditorValue(history[newIndex]);
    }
  };

  const handleApplySuggestion = (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => {
    if (startIndex !== undefined && endIndex !== undefined) {
      const newValue = editorValue.substring(0, startIndex) + suggestionText + editorValue.substring(endIndex);
      handleEditorChange(newValue);
      toast({
        title: "Suggestion Applied",
        description: `Replaced "${originalSegment}" with "${suggestionText}"`,
      });
    }
  };

  const handleDismissSuggestion = (suggestionId: string) => {
    toast({
      title: "Suggestion Dismissed",
      description: `Suggestion ${suggestionId} has been dismissed`,
    });
  };

  const loadSampleText = () => {
    const sampleTexts = [
      `This is a sample text with teh word "very good" repeated multiple times. I are writing to show how the enhanced editor works. The text is very good and the features are very good too. We use the word "use" multiple times and we use it again. We also use "make" to make things and make improvements. The system will make suggestions to make your writing better.`,
      
      `The quick brown fox jumps over teh lazy dog. This sentence is very good for testing. We make sure to make the text flow well. The system will see repeated words and see them again. We go to the store and go back home. The writing is very good and very good indeed.`,
      
      `I are excited to show you this new editor. The editor is very good at finding errors. We use advanced algorithms and we use them effectively. The system will make corrections and make improvements. You can see the suggestions and see how they work.`
    ];
    
    const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
    handleEditorChange(randomText);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Enhanced Text Editor Demo
            <Button onClick={loadSampleText} variant="outline" size="sm">
              Load Sample Text
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p><strong>Color-coded highlights:</strong></p>
              <ul className="list-disc list-inside space-y-1 mt-2">
                <li><span className="text-red-600">Red underline:</span> Spelling and grammar errors</li>
                <li><span className="text-blue-600">Blue underline:</span> Flow and style suggestions</li>
                <li><span className="text-green-600">Green underline:</span> Repeated verbs to replace</li>
                <li><span className="text-purple-600">Purple underline:</span> Plagiarism detection</li>
              </ul>
              <p className="mt-2">Click on any highlighted text to see suggestions with Correct/Dismiss options.</p>
            </div>
            
            <EnhancedTextEditor
              value={editorValue}
              onChange={handleEditorChange}
              writingMode="formal"
              direction="ltr"
              suggestions={suggestions}
              plagiarismSources={plagiarismSources}
              repeatedVerbSources={repeatedVerbSources}
              onApplySuggestion={handleApplySuggestion}
              onDismissSuggestion={handleDismissSuggestion}
              onUndo={handleUndo}
              onRedo={handleRedo}
              canUndo={historyIndex > 0}
              canRedo={historyIndex < history.length - 1}
            />
            
            <div className="text-sm text-muted-foreground">
              <p><strong>Statistics:</strong></p>
              <ul className="list-disc list-inside space-y-1 mt-2">
                <li>Spelling/Grammar errors: {suggestions.filter(s => s.type === 'spelling' || s.type === 'grammar').length}</li>
                <li>Style suggestions: {suggestions.filter(s => s.type === 'rewrite' || s.type === 'style').length}</li>
                <li>Repeated verbs: {repeatedVerbSources.length}</li>
                <li>Total suggestions: {suggestions.length + repeatedVerbSources.length}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
