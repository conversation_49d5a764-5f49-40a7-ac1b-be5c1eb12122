{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/linguaflow-header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Settings, Moon, Sun, HelpCircle, CheckSquare } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\n\ninterface LinguaFlowHeaderProps {\n  onSettingsClick: () => void;\n  onHelpClick: () => void;\n}\n\nexport function LinguaFlowHeader({ onSettingsClick, onHelpClick }: LinguaFlowHeaderProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const isRTL = uiLanguage === 'ar';\n\n  return (\n    <header className=\"sticky top-0 z-50 linguaflow-header shadow-sm\">\n      <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`flex justify-between items-center h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>\n          {/* Logo and Title */}\n          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <CheckSquare \n                className=\"text-2xl\" \n                style={{ color: 'var(--primary-color)' }}\n                size={32}\n              />\n              <h1 \n                className=\"text-xl font-bold\" \n                style={{ color: 'var(--text-primary)' }}\n              >\n                {t('appName')}\n              </h1>\n            </div>\n            \n            {/* Language Selector - Hidden on mobile */}\n            <div className={`hidden md:flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <span \n                className=\"text-sm\" \n                style={{ color: 'var(--text-secondary)' }}\n              >\n                {t('languageLabel')}:\n              </span>\n              <Select value={uiLanguage} onValueChange={setUiLanguage}>\n                <SelectTrigger \n                  className=\"w-auto min-w-[120px] h-8 text-sm linguaflow-input\"\n                  style={{\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-color)',\n                    color: 'var(--text-primary)'\n                  }}\n                >\n                  <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                </SelectTrigger>\n                <SelectContent>\n                  {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                    <SelectItem key={lang.value} value={lang.value}>\n                      {t(lang.labelKey)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className={`flex items-center space-x-2 sm:space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onSettingsClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('settingsTitle')}\n            >\n              <Settings className=\"h-5 w-5\" />\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={effectiveTheme === 'dark' ? t('switchToLightMode') : t('switchToDarkMode')}\n            >\n              {effectiveTheme === 'dark' ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onHelpClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('helpTitle')}\n            >\n              <HelpCircle className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;AAeO,SAAS,iBAAiB,EAAE,eAAe,EAAE,WAAW,EAAyB;;IACtF,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,QAAQ,eAAe;IAE7B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,qBAAqB,IAAI;;kCAEzF,6LAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;0CAC7E,6LAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;kDAC7E,6LAAC,8NAAA,CAAA,cAAW;wCACV,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;wCACvC,MAAM;;;;;;kDAER,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAsB;kDAErC,EAAE;;;;;;;;;;;;0CAKP,6LAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,oBAAoB,IAAI;;kDACvF,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAwB;;4CAEvC,EAAE;4CAAiB;;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6LAAC,qIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,OAAO;gDACT;0DAEA,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,6HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,6LAAC,qIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;kEAC3C,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,6LAAC;wBAAI,WAAW,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,IAAI;;0CAC1F,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,mBAAmB,SAAS,UAAU;gCAC9D,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,mBAAmB,SAAS,EAAE,uBAAuB,EAAE;0CAElE,mBAAmB,uBAClB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAEf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA3GgB;;QAC2B,sIAAA,CAAA,UAAO;QACJ,uIAAA,CAAA,gBAAa;;;KAF3C", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/writing-tools-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { \n  Wrench, \n  FileText, \n  Zap, \n  CheckCircle, \n  Wand2, \n  Lightbulb, \n  Download, \n  Trash2,\n  Bot\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface WritingToolsSidebarProps {\n  writingMode: string;\n  onWritingModeChange: (mode: string) => void;\n  onFileImport: (file: File) => void;\n  onCheckText: () => void;\n  onAiRewrite: () => void;\n  onBrainstorm: () => void;\n  onExport: () => void;\n  onClear: () => void;\n  aiPrompt: string;\n  onAiPromptChange: (prompt: string) => void;\n  onGenerateText: () => void;\n  wordCount: number;\n  charCount: number;\n  errorCount: number;\n  qualityScore: number;\n}\n\nexport function WritingToolsSidebar({\n  writingMode,\n  onWritingModeChange,\n  onFileImport,\n  onCheckText,\n  onAiRewrite,\n  onBrainstorm,\n  onExport,\n  onClear,\n  aiPrompt,\n  onAiPromptChange,\n  onGenerateText,\n  wordCount,\n  charCount,\n  errorCount,\n  qualityScore\n}: WritingToolsSidebarProps) {\n  const { t } = useI18n();\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onFileImport(file);\n    }\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Writing Tools Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Wrench className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('writeToolsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Writing Mode */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('writingModeLabel')}\n            </label>\n            <Select value={writingMode} onValueChange={onWritingModeChange}>\n              <SelectTrigger className=\"linguaflow-input\">\n                <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"casual\">📝 {t('casualWritingMode')}</SelectItem>\n                <SelectItem value=\"formal\">👔 {t('formalWritingMode')}</SelectItem>\n                <SelectItem value=\"academic\">🎓 {t('academicWritingMode')}</SelectItem>\n                <SelectItem value=\"creative\">🎨 {t('creativeWritingMode')}</SelectItem>\n                <SelectItem value=\"business\">💼 {t('businessWritingMode')}</SelectItem>\n                <SelectItem value=\"technical\">⚙️ {t('technicalWritingMode')}</SelectItem>\n                <SelectItem value=\"professional\">💼 {t('professionalWritingMode')}</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Import Document */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('importDocumentTitle')}\n            </label>\n            <div \n              className=\"border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400\"\n              style={{ borderColor: 'var(--border-color)' }}\n              onClick={() => document.getElementById('fileInput')?.click()}\n            >\n              <FileText className=\"mx-auto h-8 w-8 mb-2\" style={{ color: 'var(--text-secondary)' }} />\n              <p className=\"text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                {t('dropzoneInstruction')}\n              </p>\n              <input\n                type=\"file\"\n                id=\"fileInput\"\n                className=\"hidden\"\n                accept=\".txt,.md,.docx\"\n                onChange={handleFileChange}\n              />\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('quickActionTitle')}\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onCheckText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <CheckCircle className=\"mr-1 h-4 w-4\" />\n                Check Text\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiRewrite}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Wand2 className=\"mr-1 h-4 w-4\" />\n                AI Rewrite ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onBrainstorm}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Lightbulb className=\"mr-1 h-4 w-4\" />\n                Brainstorm ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onExport}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Download className=\"mr-1 h-4 w-4\" />\n                Export\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onClear}\n                className=\"linguaflow-button-secondary col-span-2\"\n              >\n                <Trash2 className=\"mr-1 h-4 w-4\" />\n                {t('clearEditorButton')}\n              </Button>\n            </div>\n          </div>\n\n          {/* AI Text Generation */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              AI Text Generation ✨\n            </label>\n            <Textarea\n              value={aiPrompt}\n              onChange={(e) => onAiPromptChange(e.target.value)}\n              rows={2}\n              className=\"linguaflow-input\"\n              placeholder={t('promptPlaceholder')}\n            />\n            <Button\n              onClick={onGenerateText}\n              className=\"linguaflow-button w-full\"\n              size=\"sm\"\n            >\n              <Bot className=\"mr-1 h-4 w-4\" />\n              Generate with AI ✨\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Writing Statistics Card */}\n      <Card className=\"linguaflow-stats\">\n        <CardContent className=\"p-4\">\n          <h4 className=\"font-semibold text-center mb-3 text-white\">\n            {t('writingStatsTitle')}\n          </h4>\n          <div className=\"space-y-2 text-sm text-white\">\n            <div className=\"flex justify-between items-center\">\n              <span>{t('wordCountLabel')}:</span>\n              <span className=\"font-medium\">{wordCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>{t('charCountLabel')}:</span>\n              <span className=\"font-medium\">{charCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Issues:</span>\n              <span className=\"font-medium\">{errorCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Score:</span>\n              <span className=\"font-medium text-lg\">{qualityScore}%</span>\n            </div>\n          </div>\n          <div className=\"mt-3 linguaflow-progress\">\n            <div \n              className=\"linguaflow-progress-bar\"\n              style={{ width: `${qualityScore}%` }}\n            />\n          </div>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAlBA;;;;;;;AAsCO,SAAS,oBAAoB,EAClC,WAAW,EACX,mBAAmB,EACnB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACa;;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAM,WAAU;;0BAEf,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACvE,EAAE;;;;;;;;;;;;kCAGP,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAa,eAAe;;0DACzC,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,6LAAC,qIAAA,CAAA,gBAAa;;kEACZ,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAY;4DAAI,EAAE;;;;;;;kEACpC,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAe;4DAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa;wCAAsB;wCAC5C,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc;;0DAErD,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,OAAO;oDAAE,OAAO;gDAAwB;;;;;;0DACnF,6LAAC;gDAAE,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAwB;0DAC5D,EAAE;;;;;;0DAEL,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,QAAO;gDACP,UAAU;;;;;;;;;;;;;;;;;;0CAMhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,kNAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAAG;;;;;;kDAGlF,6LAAC,uIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,MAAM;wCACN,WAAU;wCACV,aAAa,EAAE;;;;;;kDAEjB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;;0DAEL,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;;gDAAuB;gDAAa;;;;;;;;;;;;;;;;;;;sCAGxD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GAlMgB;;QAiBA,sIAAA,CAAA,UAAO;;;KAjBP", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/analysis-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { \n  Lightbulb, \n  MessageCircle, \n  History, \n  Search,\n  Sliders\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\ninterface AnalysisSidebarProps {\n  suggestions: Suggestion[];\n  toneAnalysis: ToneAnalysis;\n  recentPrompts: RecentPrompt[];\n  onApplySuggestion: (suggestionId: string) => void;\n  onAdjustTone: () => void;\n  onUsePrompt: (prompt: string) => void;\n}\n\nexport function AnalysisSidebar({\n  suggestions,\n  toneAnalysis,\n  recentPrompts,\n  onApplySuggestion,\n  onAdjustTone,\n  onUsePrompt\n}: AnalysisSidebarProps) {\n  const { t } = useI18n();\n\n  const getSuggestionTypeColor = (type: string) => {\n    switch (type) {\n      case 'grammar':\n      case 'spelling':\n        return 'var(--error-color)';\n      case 'style':\n        return 'var(--success-color)';\n      case 'clarity':\n        return 'var(--info-color)';\n      case 'tone':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n\n  const getSuggestionTypeIcon = (type: string) => {\n    const color = getSuggestionTypeColor(type);\n    return (\n      <div \n        className=\"w-3 h-3 rounded-full mr-2 flex-shrink-0\"\n        style={{ backgroundColor: color }}\n      />\n    );\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Suggestions Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Lightbulb className=\"mr-2 h-5 w-5\" style={{ color: 'var(--warning-color)' }} />\n            {t('writingSuggestionsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-60 linguaflow-scrollbar\">\n            {suggestions.length > 0 ? (\n              <div className=\"space-y-3\">\n                {suggestions.map((suggestion) => (\n                  <div\n                    key={suggestion.id}\n                    className=\"p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onApplySuggestion(suggestion.id)}\n                  >\n                    <div className=\"flex items-start\">\n                      {getSuggestionTypeIcon(suggestion.type)}\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium\" style={{ color: 'var(--text-primary)' }}>\n                          {suggestion.type.charAt(0).toUpperCase() + suggestion.type.slice(1)}\n                        </p>\n                        <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                          \"{suggestion.text}\" → \"{suggestion.suggestion}\"\n                        </p>\n                        {suggestion.explanation && (\n                          <p className=\"text-xs mt-1 italic\" style={{ color: 'var(--text-secondary)' }}>\n                            {suggestion.explanation}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\" style={{ color: 'var(--text-secondary)' }}>\n                <Search className=\"mx-auto h-8 w-8 mb-2\" />\n                <p className=\"text-sm\">\n                  {t('startTypingForSuggestionsDescription')}\n                </p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n\n      {/* Tone Analysis Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <MessageCircle className=\"mr-2 h-5 w-5\" style={{ color: 'var(--info-color)' }} />\n            {t('aiToneAnalysisTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>Overall Tone:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-accent)' }}>\n                {toneAnalysis.overall}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('formalityLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.formality}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('confidenceLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.confidence}\n              </span>\n            </div>\n          </div>\n          <Button\n            onClick={onAdjustTone}\n            variant=\"secondary\"\n            size=\"sm\"\n            className=\"w-full linguaflow-button-secondary\"\n          >\n            <Sliders className=\"mr-1 h-4 w-4\" />\n            Adjust Tone ✨\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* Recent AI Prompts Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <History className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('generationHistoryTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-40 linguaflow-scrollbar\">\n            {recentPrompts.length > 0 ? (\n              <div className=\"space-y-2\">\n                {recentPrompts.map((prompt) => (\n                  <div\n                    key={prompt.id}\n                    className=\"p-2 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onUsePrompt(prompt.prompt)}\n                  >\n                    <p className=\"text-xs font-medium truncate\" style={{ color: 'var(--text-primary)' }}>\n                      {prompt.prompt}\n                    </p>\n                    <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                      {prompt.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-4\" style={{ color: 'var(--text-secondary)' }}>\n                <p className=\"text-sm\">{t('noGenerationsYetPlaceholder')}</p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AAbA;;;;;;AA4CO,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,aAAa,EACb,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACU;;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,uBAAuB;QACrC,qBACE,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,iBAAiB;YAAM;;;;;;IAGtC;IAEA,qBACE,6LAAC;QAAM,WAAU;;0BAEf,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCAC1E,EAAE;;;;;;;;;;;;kCAGP,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,YAAY,MAAM,GAAG,kBACpB,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,kBAAkB,WAAW,EAAE;kDAE9C,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,sBAAsB,WAAW,IAAI;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAsB;sEACtE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC;;;;;;sEAEnE,6LAAC;4DAAE,WAAU;4DAAe,OAAO;gEAAE,OAAO;4DAAwB;;gEAAG;gEACnE,WAAW,IAAI;gEAAC;gEAAM,WAAW,UAAU;gEAAC;;;;;;;wDAE/C,WAAW,WAAW,kBACrB,6LAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAwB;sEACxE,WAAW,WAAW;;;;;;;;;;;;;;;;;;uCAnB1B,WAAW,EAAE;;;;;;;;;qDA4BxB,6LAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;;kDACxE,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAoB;;;;;;gCAC3E,EAAE;;;;;;;;;;;;kCAGP,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;0DACjD,6LAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAqB;0DAChE,aAAa,OAAO;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAkB;;;;;;;0DACtE,6LAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,SAAS;;;;;;;;;;;;kDAG3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAmB;;;;;;;0DACvE,6LAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,UAAU;;;;;;;;;;;;;;;;;;0CAI9B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,6LAAC,uNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACxE,EAAE;;;;;;;;;;;;kCAGP,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,cAAc,MAAM,GAAG,kBACtB,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,YAAY,OAAO,MAAM;;0DAExC,6LAAC;gDAAE,WAAU;gDAA+B,OAAO;oDAAE,OAAO;gDAAsB;0DAC/E,OAAO,MAAM;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;gDAAe,OAAO;oDAAE,OAAO;gDAAwB;0DACjE,OAAO,SAAS,CAAC,kBAAkB;;;;;;;uCAZjC,OAAO,EAAE;;;;;;;;;qDAkBpB,6LAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;0CACxE,cAAA,6LAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GA7KgB;;QAQA,sIAAA,CAAA,UAAO;;;KARP", "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/linguaflow-editor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { \n  Undo2, \n  Redo2, \n  Wand2, \n  Circle,\n  X\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface LinguaFlowEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n  isAutoCorrectionEnabled: boolean;\n  onToggleAutoCorrection: () => void;\n  suggestions: any[];\n  onApplySuggestion: (suggestionId: string) => void;\n  direction: 'ltr' | 'rtl';\n  currentLanguage: string;\n  lastSaved?: Date;\n  cursorPosition?: { line: number; col: number };\n  selectionInfo?: string;\n}\n\nexport function LinguaFlowEditor({\n  value,\n  onChange,\n  onUndo,\n  onRedo,\n  canUndo,\n  canRedo,\n  isAutoCorrectionEnabled,\n  onToggleAutoCorrection,\n  suggestions,\n  onApplySuggestion,\n  direction,\n  currentLanguage,\n  lastSaved,\n  cursorPosition = { line: 1, col: 1 },\n  selectionInfo = ''\n}: LinguaFlowEditorProps) {\n  const { t } = useI18n();\n  const editorRef = useRef<HTMLDivElement>(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAiActions, setShowAiActions] = useState(false);\n  const [aiActionPosition, setAiActionPosition] = useState({ x: 0, y: 0 });\n\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    if (selection && selection.toString().trim()) {\n      const selectedText = selection.toString().trim();\n      setSelectedText(selectedText);\n      \n      // Get selection position for AI actions panel\n      const range = selection.getRangeAt(0);\n      const rect = range.getBoundingClientRect();\n      setAiActionPosition({ x: rect.left, y: rect.bottom + 10 });\n      setShowAiActions(true);\n    } else {\n      setShowAiActions(false);\n      setSelectedText('');\n    }\n  }, []);\n\n  const handleEditorChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {\n    const newValue = e.currentTarget.textContent || '';\n    onChange(newValue);\n  }, [onChange]);\n\n  const aiActions = [\n    { id: 'rewrite-formal', label: 'Formalize', action: () => console.log('Formalize') },\n    { id: 'rewrite-casual', label: 'Casualize', action: () => console.log('Casualize') },\n    { id: 'rewrite-shorter', label: 'Shorten', action: () => console.log('Shorten') },\n    { id: 'rewrite-longer', label: 'Lengthen', action: () => console.log('Lengthen') },\n    { id: 'summarize', label: 'Summarize', action: () => console.log('Summarize') },\n    { id: 'explain', label: 'Explain', action: () => console.log('Explain') },\n  ];\n\n  return (\n    <section className=\"space-y-4\">\n      {/* Main Editor Card */}\n      <Card className=\"linguaflow-card shadow-lg\">\n        <CardHeader className=\"border-b px-4 py-3 flex flex-row items-center justify-between\" style={{ borderColor: 'var(--border-color)' }}>\n          <h3 className=\"font-semibold\" style={{ color: 'var(--text-primary)' }}>\n            Document Editor\n          </h3>\n          <div className=\"flex items-center space-x-2\">\n            {/* Status Indicator */}\n            <div className=\"flex items-center space-x-1 text-sm\" style={{ color: 'var(--text-secondary)' }}>\n              <Circle \n                className=\"h-3 w-3 fill-current\" \n                style={{ color: 'var(--success-color)' }}\n              />\n              <span>Ready</span>\n            </div>\n\n            {/* Auto-correction Toggle */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onToggleAutoCorrection}\n              className=\"p-1 rounded\"\n              style={{ \n                color: isAutoCorrectionEnabled ? 'var(--primary-color)' : 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title={isAutoCorrectionEnabled ? 'Auto-correction enabled' : 'Auto-correction disabled'}\n            >\n              <Wand2 className=\"h-4 w-4\" />\n            </Button>\n\n            {/* Undo/Redo */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onUndo}\n              disabled={!canUndo}\n              className=\"p-1 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title=\"Undo (Ctrl+Z)\"\n            >\n              <Undo2 className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onRedo}\n              disabled={!canRedo}\n              className=\"p-1 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title=\"Redo (Ctrl+Y)\"\n            >\n              <Redo2 className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n\n        {/* Editor Content */}\n        <div className=\"linguaflow-editor linguaflow-scrollbar\">\n          <div\n            ref={editorRef}\n            contentEditable\n            suppressContentEditableWarning\n            onInput={handleEditorChange}\n            onMouseUp={handleTextSelection}\n            onKeyUp={handleTextSelection}\n            className=\"p-6 min-h-[380px] outline-none\"\n            style={{\n              color: 'var(--text-primary)',\n              lineHeight: '1.75',\n              direction: direction,\n              textAlign: direction === 'rtl' ? 'right' : 'left'\n            }}\n            placeholder={t('editorPlaceholder')}\n            dangerouslySetInnerHTML={{ __html: value }}\n          />\n        </div>\n\n        {/* Editor Footer */}\n        <div \n          className=\"border-t px-4 py-2 flex items-center justify-between text-xs\"\n          style={{ \n            borderColor: 'var(--border-color)',\n            backgroundColor: 'var(--bg-alt)',\n            color: 'var(--text-secondary)'\n          }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <span>\n              Last saved: <span className=\"font-medium\">\n                {lastSaved ? lastSaved.toLocaleTimeString() : 'Never'}\n              </span>\n            </span>\n            <span>\n              Language: <span className=\"font-medium\">{currentLanguage}</span>\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {selectionInfo && <span>{selectionInfo}</span>}\n            <span>Line {cursorPosition.line}, Col {cursorPosition.col}</span>\n          </div>\n        </div>\n      </Card>\n\n      {/* AI Context Panel */}\n      {showAiActions && selectedText && (\n        <Card \n          className=\"linguaflow-card animate-in fade-in-0 zoom-in-95\"\n          style={{\n            position: 'fixed',\n            top: aiActionPosition.y,\n            left: aiActionPosition.x,\n            zIndex: 1000,\n            maxWidth: '400px'\n          }}\n        >\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-semibold flex items-center text-sm\" style={{ color: 'var(--primary-color)' }}>\n                <Wand2 className=\"mr-2 h-4 w-4\" />\n                AI Actions for Selection ✨\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setShowAiActions(false)}\n                className=\"h-6 w-6 p-1\"\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <p \n              className=\"text-sm italic p-2 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n            >\n              \"{selectedText}\"\n            </p>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {aiActions.map((action) => (\n                <Button\n                  key={action.id}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={action.action}\n                  className=\"linguaflow-button-secondary text-xs\"\n                >\n                  {action.label}\n                </Button>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AAZA;;;;;;AAgCO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,uBAAuB,EACvB,sBAAsB,EACtB,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,eAAe,EACf,SAAS,EACT,iBAAiB;IAAE,MAAM;IAAG,KAAK;AAAE,CAAC,EACpC,gBAAgB,EAAE,EACI;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtE,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACtC,MAAM,YAAY,OAAO,YAAY;YACrC,IAAI,aAAa,UAAU,QAAQ,GAAG,IAAI,IAAI;gBAC5C,MAAM,eAAe,UAAU,QAAQ,GAAG,IAAI;gBAC9C,gBAAgB;gBAEhB,8CAA8C;gBAC9C,MAAM,QAAQ,UAAU,UAAU,CAAC;gBACnC,MAAM,OAAO,MAAM,qBAAqB;gBACxC,oBAAoB;oBAAE,GAAG,KAAK,IAAI;oBAAE,GAAG,KAAK,MAAM,GAAG;gBAAG;gBACxD,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;gBACjB,gBAAgB;YAClB;QACF;4DAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACtC,MAAM,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI;YAChD,SAAS;QACX;2DAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAmB,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;QAChF;YAAE,IAAI;YAAkB,OAAO;YAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAY;QACjF;YAAE,IAAI;YAAa,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;KACzE;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAgE,OAAO;4BAAE,aAAa;wBAAsB;;0CAChI,6LAAC;gCAAG,WAAU;gCAAgB,OAAO;oCAAE,OAAO;gCAAsB;0CAAG;;;;;;0CAGvE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;wCAAsC,OAAO;4CAAE,OAAO;wCAAwB;;0DAC3F,6LAAC,yMAAA,CAAA,SAAM;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAuB;;;;;;0DAEzC,6LAAC;0DAAK;;;;;;;;;;;;kDAIR,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAO;4CACL,OAAO,0BAA0B,yBAAyB;4CAC1D,iBAAiB;wCACnB;wCACA,OAAO,0BAA0B,4BAA4B;kDAE7D,cAAA,6LAAC,kNAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAInB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,OAAM;kDAEN,cAAA,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,OAAM;kDAEN,cAAA,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,eAAe;4BACf,8BAA8B;4BAC9B,SAAS;4BACT,WAAW;4BACX,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,YAAY;gCACZ,WAAW;gCACX,WAAW,cAAc,QAAQ,UAAU;4BAC7C;4BACA,aAAa,EAAE;4BACf,yBAAyB;gCAAE,QAAQ;4BAAM;;;;;;;;;;;kCAK7C,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,iBAAiB;4BACjB,OAAO;wBACT;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;0DACQ,6LAAC;gDAAK,WAAU;0DACzB,YAAY,UAAU,kBAAkB,KAAK;;;;;;;;;;;;kDAGlD,6LAAC;;4CAAK;0DACM,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;;oCACZ,+BAAiB,6LAAC;kDAAM;;;;;;kDACzB,6LAAC;;4CAAK;4CAAM,eAAe,IAAI;4CAAC;4CAAO,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAM9D,iBAAiB,8BAChB,6LAAC,mIAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK,iBAAiB,CAAC;oBACvB,MAAM,iBAAiB,CAAC;oBACxB,QAAQ;oBACR,UAAU;gBACZ;;kCAEA,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;oCAA0C,OAAO;wCAAE,OAAO;oCAAuB;;sDAC7F,6LAAC,kNAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;;oCACD;oCACG;oCAAa;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,uBACd,6LAAC,qIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,OAAO,MAAM;wCACtB,WAAU;kDAET,OAAO,KAAK;uCANR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC;GA9NgB;;QAiBA,sIAAA,CAAA,UAAO;;;KAjBP", "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/new-design.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\nimport { LinguaFlowHeader } from '@/components/layout/linguaflow-header';\nimport { WritingToolsSidebar } from '@/components/layout/writing-tools-sidebar';\nimport { AnalysisSidebar } from '@/components/layout/analysis-sidebar';\nimport { LinguaFlowEditor } from '@/components/editor/linguaflow-editor';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\nexport default function NewDesignPage() {\n  const { t, uiLanguage, writingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n  const isRTL = uiLanguage === 'ar';\n\n  // Editor state\n  const [editorValue, setEditorValue] = useState('');\n  const [writingMode, setWritingMode] = useState('formal');\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const [isAutoCorrectionEnabled, setIsAutoCorrectionEnabled] = useState(true);\n  const [aiPrompt, setAiPrompt] = useState('');\n\n  // Statistics\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [errorCount, setErrorCount] = useState(0);\n  const [qualityScore, setQualityScore] = useState(100);\n\n  // Analysis data\n  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);\n  const [toneAnalysis, setToneAnalysis] = useState<ToneAnalysis>({\n    overall: 'Neutral',\n    formality: 'Professional (75%)',\n    confidence: 'Confident (80%)'\n  });\n  const [recentPrompts, setRecentPrompts] = useState<RecentPrompt[]>([]);\n\n  // Modal states\n  const [showSettings, setShowSettings] = useState(false);\n  const [showHelp, setShowHelp] = useState(false);\n\n  // Update statistics when editor value changes\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(word => word.length > 0);\n    setWordCount(words.length);\n    setCharCount(editorValue.length);\n    \n    // Mock error count and quality score\n    const errors = Math.max(0, Math.floor(words.length * 0.05) - Math.floor(Math.random() * 3));\n    setErrorCount(errors);\n    setQualityScore(Math.max(60, 100 - errors * 5));\n  }, [editorValue]);\n\n  // Handlers\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    // Implement undo logic\n    console.log('Undo');\n  }, []);\n\n  const handleRedo = useCallback(() => {\n    // Implement redo logic\n    console.log('Redo');\n  }, []);\n\n  const handleFileImport = useCallback((file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      setEditorValue(content);\n      toast({\n        title: t('toastFileImportSuccessTitle'),\n        description: t('toastFileImportSuccessMessage'),\n      });\n    };\n    reader.readAsText(file);\n  }, [toast, t]);\n\n  const handleCheckText = useCallback(() => {\n    // Mock suggestions\n    const mockSuggestions: Suggestion[] = [\n      {\n        id: '1',\n        type: 'grammar',\n        text: 'are',\n        suggestion: 'is',\n        explanation: 'Subject-verb agreement error'\n      },\n      {\n        id: '2',\n        type: 'style',\n        text: 'very good',\n        suggestion: 'excellent',\n        explanation: 'More precise word choice'\n      }\n    ];\n    setSuggestions(mockSuggestions);\n    toast({\n      title: 'Text Analysis Complete',\n      description: `Found ${mockSuggestions.length} suggestions`,\n    });\n  }, [toast]);\n\n  const handleAiRewrite = useCallback(() => {\n    toast({\n      title: 'AI Rewrite',\n      description: 'AI rewrite feature activated',\n    });\n  }, [toast]);\n\n  const handleBrainstorm = useCallback(() => {\n    toast({\n      title: 'Brainstorm',\n      description: 'Brainstorm feature activated',\n    });\n  }, [toast]);\n\n  const handleExport = useCallback(() => {\n    const blob = new Blob([editorValue], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'document.txt';\n    a.click();\n    URL.revokeObjectURL(url);\n    toast({\n      title: 'Export Complete',\n      description: 'Document exported successfully',\n    });\n  }, [editorValue, toast]);\n\n  const handleClear = useCallback(() => {\n    setEditorValue('');\n    setSuggestions([]);\n    toast({\n      title: t('toastEditorClearedSuccess'),\n    });\n  }, [toast, t]);\n\n  const handleGenerateText = useCallback(() => {\n    if (!aiPrompt.trim()) {\n      toast({\n        title: t('toastInputRequiredTitle'),\n        description: t('toastPromptRequiredError'),\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    // Mock text generation\n    const generatedText = `Generated text based on: \"${aiPrompt}\"`;\n    setEditorValue(prev => prev + (prev ? '\\n\\n' : '') + generatedText);\n    \n    // Add to recent prompts\n    const newPrompt: RecentPrompt = {\n      id: Date.now().toString(),\n      prompt: aiPrompt,\n      timestamp: new Date()\n    };\n    setRecentPrompts(prev => [newPrompt, ...prev.slice(0, 4)]);\n    setAiPrompt('');\n    \n    toast({\n      title: t('toastTextGeneratedSuccess'),\n    });\n  }, [aiPrompt, toast, t]);\n\n  const handleApplySuggestion = useCallback((suggestionId: string) => {\n    const suggestion = suggestions.find(s => s.id === suggestionId);\n    if (suggestion) {\n      const newValue = editorValue.replace(suggestion.text, suggestion.suggestion);\n      setEditorValue(newValue);\n      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n      toast({\n        title: t('toastSuggestionAppliedSuccess'),\n      });\n    }\n  }, [suggestions, editorValue, toast, t]);\n\n  const handleAdjustTone = useCallback(() => {\n    toast({\n      title: 'Tone Adjustment',\n      description: 'Tone adjustment feature activated',\n    });\n  }, [toast]);\n\n  const handleUsePrompt = useCallback((prompt: string) => {\n    setAiPrompt(prompt);\n  }, []);\n\n  const writingDirection = writingLanguageDialect?.startsWith('ar') ? 'rtl' : 'ltr';\n  const currentLanguage = writingLanguageDialect || 'English';\n\n  return (\n    <div \n      className=\"min-h-screen transition-all duration-300\"\n      style={{ \n        backgroundColor: 'var(--bg-primary)',\n        color: 'var(--text-primary)'\n      }}\n    >\n      {/* Header */}\n      <LinguaFlowHeader\n        onSettingsClick={() => setShowSettings(true)}\n        onHelpClick={() => setShowHelp(true)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className={`grid grid-cols-1 lg:grid-cols-12 gap-6 ${isRTL ? 'direction-rtl' : ''}`}>\n          {/* Left Sidebar - Writing Tools */}\n          <div className=\"lg:col-span-3\">\n            <WritingToolsSidebar\n              writingMode={writingMode}\n              onWritingModeChange={setWritingMode}\n              onFileImport={handleFileImport}\n              onCheckText={handleCheckText}\n              onAiRewrite={handleAiRewrite}\n              onBrainstorm={handleBrainstorm}\n              onExport={handleExport}\n              onClear={handleClear}\n              aiPrompt={aiPrompt}\n              onAiPromptChange={setAiPrompt}\n              onGenerateText={handleGenerateText}\n              wordCount={wordCount}\n              charCount={charCount}\n              errorCount={errorCount}\n              qualityScore={qualityScore}\n            />\n          </div>\n\n          {/* Center - Editor */}\n          <div className=\"lg:col-span-6\">\n            <LinguaFlowEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n              isAutoCorrectionEnabled={isAutoCorrectionEnabled}\n              onToggleAutoCorrection={() => setIsAutoCorrectionEnabled(!isAutoCorrectionEnabled)}\n              suggestions={suggestions}\n              onApplySuggestion={handleApplySuggestion}\n              direction={writingDirection}\n              currentLanguage={currentLanguage}\n              lastSaved={new Date()}\n            />\n          </div>\n\n          {/* Right Sidebar - Analysis */}\n          <div className=\"lg:col-span-3\">\n            <AnalysisSidebar\n              suggestions={suggestions}\n              toneAnalysis={toneAnalysis}\n              recentPrompts={recentPrompts}\n              onApplySuggestion={handleApplySuggestion}\n              onAdjustTone={handleAdjustTone}\n              onUsePrompt={handleUsePrompt}\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAVA;;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IACtC,MAAM,QAAQ,eAAe;IAE7B,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,SAAS;QACT,WAAW;QACX,YAAY;IACd;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;iDAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;;YAC3E,aAAa,MAAM,MAAM;YACzB,aAAa,YAAY,MAAM;YAE/B,qCAAqC;YACrC,MAAM,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACxF,cAAc;YACd,gBAAgB,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS;QAC9C;kCAAG;QAAC;KAAY;IAEhB,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACtC,eAAe;QACjB;wDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,uBAAuB;YACvB,QAAQ,GAAG,CAAC;QACd;gDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,uBAAuB;YACvB,QAAQ,GAAG,CAAC;QACd;gDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACpC,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM;+DAAG,CAAC;oBACf,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,eAAe;oBACf,MAAM;wBACJ,OAAO,EAAE;wBACT,aAAa,EAAE;oBACjB;gBACF;;YACA,OAAO,UAAU,CAAC;QACpB;sDAAG;QAAC;QAAO;KAAE;IAEb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,mBAAmB;YACnB,MAAM,kBAAgC;gBACpC;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,YAAY;oBACZ,aAAa;gBACf;aACD;YACD,eAAe;YACf,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;YAC5D;QACF;qDAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;qDAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACnC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;sDAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAY,EAAE;gBAAE,MAAM;YAAa;YAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,EAAE,KAAK;YACP,IAAI,eAAe,CAAC;YACpB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;kDAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,eAAe;YACf,eAAe,EAAE;YACjB,MAAM;gBACJ,OAAO,EAAE;YACX;QACF;iDAAG;QAAC;QAAO;KAAE;IAEb,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACrC,IAAI,CAAC,SAAS,IAAI,IAAI;gBACpB,MAAM;oBACJ,OAAO,EAAE;oBACT,aAAa,EAAE;oBACf,SAAS;gBACX;gBACA;YACF;YAEA,uBAAuB;YACvB,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;YAC9D;iEAAe,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;;YAErD,wBAAwB;YACxB,MAAM,YAA0B;gBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA;iEAAiB,CAAA,OAAQ;wBAAC;2BAAc,KAAK,KAAK,CAAC,GAAG;qBAAG;;YACzD,YAAY;YAEZ,MAAM;gBACJ,OAAO,EAAE;YACX;QACF;wDAAG;QAAC;QAAU;QAAO;KAAE;IAEvB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACzC,MAAM,aAAa,YAAY,IAAI;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YAClD,IAAI,YAAY;gBACd,MAAM,WAAW,YAAY,OAAO,CAAC,WAAW,IAAI,EAAE,WAAW,UAAU;gBAC3E,eAAe;gBACf;wEAAe,CAAA,OAAQ,KAAK,MAAM;gFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;gBACjD,MAAM;oBACJ,OAAO,EAAE;gBACX;YACF;QACF;2DAAG;QAAC;QAAa;QAAa;QAAO;KAAE;IAEvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACnC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;sDAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACnC,YAAY;QACd;qDAAG,EAAE;IAEL,MAAM,mBAAmB,wBAAwB,WAAW,QAAQ,QAAQ;IAC5E,MAAM,kBAAkB,0BAA0B;IAElD,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;0BAGA,6LAAC,uJAAA,CAAA,mBAAgB;gBACf,iBAAiB,IAAM,gBAAgB;gBACvC,aAAa,IAAM,YAAY;;;;;;0BAIjC,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,kBAAkB,IAAI;;sCAEtF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8JAAA,CAAA,sBAAmB;gCAClB,aAAa;gCACb,qBAAqB;gCACrB,cAAc;gCACd,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,UAAU;gCACV,SAAS;gCACT,UAAU;gCACV,kBAAkB;gCAClB,gBAAgB;gCAChB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,cAAc;;;;;;;;;;;sCAKlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uJAAA,CAAA,mBAAgB;gCACf,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,SAAS;gCACT,SAAS;gCACT,yBAAyB;gCACzB,wBAAwB,IAAM,2BAA2B,CAAC;gCAC1D,aAAa;gCACb,mBAAmB;gCACnB,WAAW;gCACX,iBAAiB;gCACjB,WAAW,IAAI;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,sJAAA,CAAA,kBAAe;gCACd,aAAa;gCACb,cAAc;gCACd,eAAe;gCACf,mBAAmB;gCACnB,cAAc;gCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GApQwB;;QAC4B,sIAAA,CAAA,UAAO;QACvC,+HAAA,CAAA,WAAQ;QACL,qJAAA,CAAA,qBAAkB;;;KAHjB", "debugId": null}}]}