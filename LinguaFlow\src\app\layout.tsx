
import type {Metadata} from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { I18nProvider } from '@/contexts/i18n-context';
import { AppearanceProvider } from '@/contexts/theme-context'; // Renamed to AppearanceProvider
import { DictionaryProvider } from '@/contexts/dictionary-context';
import { FeatureSettingsProvider } from '@/contexts/feature-settings-context';

export const metadata: Metadata = {
  title: 'LinguaFlow', 
  description: 'Grammar Correction & Writing Assistant', 
  manifest: '/manifest.json',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    // Class and dir will be set by providers
    <html lang="en" dir="ltr" suppressHydrationWarning>
      <head>
        {/* Favicon and App Icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Meta tags for PWA */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="LinguaFlow" />

        {/* Fonts */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;500&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Cormorant+SC:wght@500&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased min-h-screen flex flex-col bg-background text-foreground" suppressHydrationWarning>
        <AppearanceProvider>
          <I18nProvider>
            <DictionaryProvider>
              <FeatureSettingsProvider>
                {children}
                <Toaster />
              </FeatureSettingsProvider>
            </DictionaryProvider>
          </I18nProvider>
        </AppearanceProvider>
      </body>
    </html>
  );
}
