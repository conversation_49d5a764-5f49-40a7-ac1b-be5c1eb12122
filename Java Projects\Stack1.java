public class Stack1 {
    // top of the stack represents beginning of the line, 
    // represented with the value -1 which is characterized as an empty stack
    public static int autostation = -1;
    // array stack size is 3
    public static int stack[] = new int[3];
    
    // method for push operation
    public static void push(int a) {
        // checks if stack is full or not
        if (autostation >= 2) {
            System.out.println("Line heap overflow");
            return;
        }
        // increasing top of the stack by 1
        autostation++;
        // inserting an item to the top of the stack
        stack[autostation] = a;
    }
    
    // method for pop operation
    public static void pop() {
        // checking if the stack is empty
        if (autostation <= -1) {
            System.out.println("Line heap underflow");
            return;
        }
        // displaying the item being popped
        System.out.println("Item popped: " + stack[autostation]);
        // decreasing the top of stack by 1
        autostation--;
    }
    
    public static void main() {
        // this pushes button 3 times as vehicle begins the line
        push(2);
        push(1);
        push(0); // vehicle begins the line
        // Popping three items out of the stack
        pop(); // First Testing Station
        pop(); // Second Testing Station
        pop(); // Third Testing Station
    }
}

