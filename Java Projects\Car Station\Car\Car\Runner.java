import java.util.Stack;

public class Runner {

	/**
	 * @param args
	 */
	public static void main(String[] args) {

    //This code reports the number of seconds that the      computer spent on this task.
    long startTime; // Starting time of program, in milliseconds.
    long endTime; // Time when computations are done, in milliseconds.
    double time; // Time difference, in seconds.
    startTime = System.currentTimeMillis();

		Stack car = new Stack();
		
		//(1)Beginning of the line.
		car.push(0);
		car.push(1);
		car.push(2);
				
		System.out.println("Beginning of the line:");
		System.out.println(car.push(0));
		System.out.println(car.push(1));
		System.out.println(car.push(2)+ "\n");
		
		//(2)Station 2. Poping out car(0).
		System.out.println("#'Station 2': Poping out Car(0). Remains two cars on the line.");
		System.out.print(car.pop());	
		System.out.println("\n- Size of Station 2 after poping out Car(0):");
		System.out.println(car.size() + "\n");
		
		//(3)Station 3. Poping out car(1).
		System.out.println("#'Station 3': Poping out Car(1). Remains one car on the line.");
		System.out.print(car.pop());	
		System.out.println("\n- Size of Station 3 after poping out Car(1):");
		System.out.println(car.size() + "\n");
		
		//(4)Station 4. Poping out car(2).
		System.out.println("#'Station 4' Poping out Car(2). Now, there are no cars on the line.");
		System.out.print(car.pop());	
		System.out.println("\n- Size of Station 4 after poping out Car(2):");
		System.out.println(car.size() + "\n");
		
		
    //(5)End of the line.
		
    System.out.println("#'Station 5' (End of the line). Now, there are no cars on the line.");
		System.out.println(car.pop()+ "\n");	
		System.out.println("End of the line.");
	
    //This code reports the number of seconds that the computer spent on this task.
    endTime = System.currentTimeMillis();
    time = (endTime - startTime) / 1000.0;
    //This output display the Runtime of the my algorithm.
    System.out.println("This is the runtime of my algorithm:");
    System.out.print("\nRuntime in seconds was: ");
    System.out.println(time);
			
	}

}