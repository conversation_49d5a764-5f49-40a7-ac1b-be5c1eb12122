import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
"use client";

import React, { useState, useRef, useCallback, useEffect, forwardRef, useMemo, type ChangeEvent, type UIEvent } from 'react';
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Loader2, Eraser, Copy, Check, Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, Undo2, Redo2, Wand2, List, ListOrdered, ShieldCheck } from "lucide-react";
import { rephraseText, type RephraseTextInput } from '@/ai/flows/contextual-ai-rephraser';
import { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';
import { useToast } from '@/hooks/use-toast';
import { Label } from '../ui/label';
import { useI18n } from '@/contexts/i18n-context';
import { cn } from '@/lib/utils';
import { Separator } from '../ui/separator';
import { WordToolkitPopover } from './word-toolkit-popover';
import { useDebounce } from '@/hooks/use-debounce';

export interface SelectionDetail {
  text: string;
  start: number;
  end: number;
}

export type PlagiarismSource = Omit<AnalysisSuggestion, 'type'> & { type: 'plagiarism' };
export type RephraseSource = Omit<AnalysisSuggestion, 'type'> & { type: 'rephrase' };
export type RepeatedVerbSource = Omit<AnalysisSuggestion, 'type'> & { type: 'repeated-verb' };

type CombinedSuggestion = AnalysisSuggestion | PlagiarismSource | RepeatedVerbSource;

interface EnhancedTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  writingMode: string;
  direction: 'ltr' | 'rtl';
  suggestions: AnalysisSuggestion[];
  plagiarismSources: PlagiarismSource[];
  repeatedVerbSources?: RepeatedVerbSource[];
  onApplySuggestion: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;
  onDismissSuggestion: (suggestionId: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

interface SuggestionPopoverProps {
    suggestion: AnalysisSuggestion, 
    onApply: (s: AnalysisSuggestion) => void,
    onDismiss: (id: string) => void,
    onClose: () => void;
}

interface PlagiarismPopoverProps {
    source: PlagiarismSource, 
    onClose: () => void;
}

const fontOptions = [
    { value: "'Inter', sans-serif", label: 'Inter' },
    { value: "'Source Code Pro', monospace", label: 'Source Code Pro' },
    { value: "'Georgia', serif", label: 'Georgia' },
    { value: "'Times New Roman', Times, serif", label: 'Times New Roman' },
];

// Common verbs that when repeated should be replaced with more engaging alternatives
const COMMON_VERBS = [
    'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
    'can', 'could', 'should', 'may', 'might', 'must',
    'go', 'goes', 'went', 'come', 'comes', 'came',
    'get', 'gets', 'got', 'take', 'takes', 'took',
    'make', 'makes', 'made', 'give', 'gives', 'gave',
    'say', 'says', 'said', 'tell', 'tells', 'told',
    'see', 'sees', 'saw', 'look', 'looks', 'looked',
    'know', 'knows', 'knew', 'think', 'thinks', 'thought',
    'want', 'wants', 'wanted', 'need', 'needs', 'needed',
    'use', 'uses', 'used', 'work', 'works', 'worked',
    'find', 'finds', 'found', 'put', 'puts', 'show', 'shows', 'showed'
];

// Enhanced verb alternatives for more engaging writing
const VERB_ALTERNATIVES: Record<string, string[]> = {
    'is': ['becomes', 'remains', 'appears', 'seems', 'stands as', 'represents'],
    'are': ['become', 'remain', 'appear', 'seem', 'stand as', 'represent'],
    'was': ['became', 'remained', 'appeared', 'seemed', 'stood as', 'represented'],
    'were': ['became', 'remained', 'appeared', 'seemed', 'stood as', 'represented'],
    'go': ['venture', 'journey', 'travel', 'proceed', 'advance', 'navigate'],
    'goes': ['ventures', 'journeys', 'travels', 'proceeds', 'advances', 'navigates'],
    'went': ['ventured', 'journeyed', 'traveled', 'proceeded', 'advanced', 'navigated'],
    'get': ['obtain', 'acquire', 'secure', 'attain', 'receive', 'gain'],
    'gets': ['obtains', 'acquires', 'secures', 'attains', 'receives', 'gains'],
    'got': ['obtained', 'acquired', 'secured', 'attained', 'received', 'gained'],
    'make': ['create', 'craft', 'construct', 'build', 'forge', 'develop'],
    'makes': ['creates', 'crafts', 'constructs', 'builds', 'forges', 'develops'],
    'made': ['created', 'crafted', 'constructed', 'built', 'forged', 'developed'],
    'say': ['express', 'declare', 'state', 'articulate', 'convey', 'communicate'],
    'says': ['expresses', 'declares', 'states', 'articulates', 'conveys', 'communicates'],
    'said': ['expressed', 'declared', 'stated', 'articulated', 'conveyed', 'communicated'],
    'see': ['observe', 'witness', 'notice', 'perceive', 'spot', 'glimpse'],
    'sees': ['observes', 'witnesses', 'notices', 'perceives', 'spots', 'glimpses'],
    'saw': ['observed', 'witnessed', 'noticed', 'perceived', 'spotted', 'glimpsed'],
    'look': ['gaze', 'glance', 'peer', 'examine', 'inspect', 'scrutinize'],
    'looks': ['gazes', 'glances', 'peers', 'examines', 'inspects', 'scrutinizes'],
    'looked': ['gazed', 'glanced', 'peered', 'examined', 'inspected', 'scrutinized'],
    'think': ['believe', 'consider', 'contemplate', 'ponder', 'reflect', 'reason'],
    'thinks': ['believes', 'considers', 'contemplates', 'ponders', 'reflects', 'reasons'],
    'thought': ['believed', 'considered', 'contemplated', 'pondered', 'reflected', 'reasoned'],
    'use': ['utilize', 'employ', 'apply', 'implement', 'leverage', 'harness'],
    'uses': ['utilizes', 'employs', 'applies', 'implements', 'leverages', 'harnesses'],
    'used': ['utilized', 'employed', 'applied', 'implemented', 'leveraged', 'harnessed'],
    'work': ['function', 'operate', 'perform', 'execute', 'accomplish', 'achieve'],
    'works': ['functions', 'operates', 'performs', 'executes', 'accomplishes', 'achieves'],
    'worked': ['functioned', 'operated', 'performed', 'executed', 'accomplished', 'achieved']
};

// Function to detect repeated verbs in text
function detectRepeatedVerbs(text: string): RepeatedVerbSource[] {
    if (!text || text.length < 10) return [];
    
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const verbCounts: Record<string, { count: number; positions: number[] }> = {};
    const suggestions: RepeatedVerbSource[] = [];
    
    // Count verb occurrences and track positions
    words.forEach((word, index) => {
        if (COMMON_VERBS.includes(word)) {
            if (!verbCounts[word]) {
                verbCounts[word] = { count: 0, positions: [] };
            }
            verbCounts[word].count++;
            verbCounts[word].positions.push(index);
        }
    });
    
    // Find repeated verbs (appearing 3+ times in text)
    Object.entries(verbCounts).forEach(([verb, data]) => {
        if (data.count >= 3 && VERB_ALTERNATIVES[verb]) {
            // Create suggestions for each occurrence after the first two
            data.positions.slice(2).forEach((position, idx) => {
                const startIndex = text.toLowerCase().indexOf(verb, 
                    idx > 0 ? suggestions[suggestions.length - 1]?.endIndex || 0 : 0);
                
                if (startIndex !== -1) {
                    const endIndex = startIndex + verb.length;
                    const alternatives = VERB_ALTERNATIVES[verb];
                    const randomAlternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                    
                    suggestions.push({
                        id: `repeated-verb-${verb}-${position}`,
                        type: 'repeated-verb',
                        message: `Consider replacing this repeated verb "${verb}" with a more engaging alternative to improve your writing's rhythm and variety.`,
                        originalSegment: text.substring(startIndex, endIndex),
                        suggestion: randomAlternative,
                        startIndex,
                        endIndex
                    });
                }
            });
        }
    });
    
    return suggestions;
}

// Enhanced popover for inline suggestions with improved UX - Memoized for performance
const SuggestionPopover = React.memo(({
    suggestion,
    onApply,
    onDismiss,
    onClose,
}: SuggestionPopoverProps) => {
    const { t } = useI18n();

    const handleApply = () => {
        onApply(suggestion);
        onClose();
    };

    const handleDismiss = () => {
        onDismiss(suggestion.id);
        onClose();
    };

    // Get suggestion type styling and icon
    const getSuggestionTypeInfo = (type: string) => {
        switch (type) {
            case 'spelling':
                return {
                    color: '#ef4444',
                    bgColor: '#fef2f2',
                    borderColor: '#fecaca',
                    icon: '🔤',
                    title: 'Spelling Error'
                };
            case 'grammar':
                return {
                    color: '#ef4444',
                    bgColor: '#fef2f2',
                    borderColor: '#fecaca',
                    icon: '📝',
                    title: 'Grammar Issue'
                };
            case 'rewrite':
                return {
                    color: '#3b82f6',
                    bgColor: '#eff6ff',
                    borderColor: '#bfdbfe',
                    icon: '🌊',
                    title: 'Flow Enhancement'
                };
            case 'style':
                return {
                    color: '#3b82f6',
                    bgColor: '#eff6ff',
                    borderColor: '#bfdbfe',
                    icon: '✨',
                    title: 'Style Improvement'
                };
            case 'repeated-verb':
                return {
                    color: '#10b981',
                    bgColor: '#f0fdf4',
                    borderColor: '#bbf7d0',
                    icon: '🎯',
                    title: 'Vocabulary Enhancement'
                };
            default:
                return {
                    color: '#6b7280',
                    bgColor: '#f9fafb',
                    borderColor: '#e5e7eb',
                    icon: '💡',
                    title: 'Suggestion'
                };
        }
    };

    const typeInfo = getSuggestionTypeInfo(suggestion.type);

    return (
        <PopoverContent className="w-auto max-w-sm p-0 shadow-xl border-0" side="top" align="start" onEscapeKeyDown={onClose}>
            <div className="rounded-lg overflow-hidden">
                {/* Header with type indicator */}
                <div
                    className="px-3 py-2 text-sm font-medium flex items-center gap-2"
                    style={{
                        backgroundColor: typeInfo.bgColor,
                        borderBottom: `1px solid ${typeInfo.borderColor}`,
                        color: typeInfo.color
                    }}
                >
                    <span>{typeInfo.icon}</span>
                    <span>{typeInfo.title}</span>
                </div>

                {/* Content */}
                <div className="p-3 space-y-3">
                    <p className="text-sm text-muted-foreground">{suggestion.message}</p>

                    {/* Before/After comparison */}
                    <div className="space-y-2">
                        <div className="p-2 rounded-md bg-red-50 border border-red-200">
                            <p className="text-xs text-red-600 font-medium mb-1">Original:</p>
                            <p className="text-sm text-red-700 line-through">{suggestion.originalSegment}</p>
                        </div>
                        <div className="p-2 rounded-md bg-green-50 border border-green-200">
                            <p className="text-xs text-green-600 font-medium mb-1">Suggested:</p>
                            <p className="text-sm text-green-700 font-semibold">{suggestion.suggestion}</p>
                        </div>
                    </div>

                    {/* Action buttons */}
                    <div className="flex justify-end gap-2 pt-2">
                        <Button size="sm" variant="ghost" onClick={handleDismiss} className="text-gray-600 hover:text-gray-800">
                            {t('dismissButton')}
                        </Button>
                        <Button
                            size="sm"
                            onClick={handleApply}
                            className="bg-green-600 hover:bg-green-700 text-white"
                        >
                            <Check className="mr-2 h-4 w-4" />
                            {t('correctButton')}
                        </Button>
                    </div>
                </div>
            </div>
        </PopoverContent>
    );
});

const PlagiarismPopover = React.memo(({ source, onClose }: PlagiarismPopoverProps) => {
    const { t } = useI18n();

    return (
        <PopoverContent className="w-auto max-w-sm p-3 shadow-xl" side="top" align="start" onEscapeKeyDown={onClose}>
            <div className="space-y-3">
                <div className="flex items-center gap-2 font-semibold text-destructive">
                    <ShieldCheck className="h-5 w-5" />
                    {t('plagiarismReportLabel')}
                </div>
                <p className="text-sm text-muted-foreground">{source.message}</p>
                <div className="p-2 rounded-md bg-destructive/10 border border-destructive/20">
                    <p className="text-sm text-destructive">
                        <span className="font-semibold">{t('originalSourceLabel')}:</span>{' '}
                        <a href={source.suggestion} target="_blank" rel="noopener noreferrer" className="underline break-all">
                            {source.suggestion}
                        </a>
                    </p>
                </div>
                <div className="flex justify-end">
                    <Button size="sm" variant="ghost" onClick={onClose}>
                        {t('dismissButton')}
                    </Button>
                </div>
            </div>
        </PopoverContent>
    );
});
