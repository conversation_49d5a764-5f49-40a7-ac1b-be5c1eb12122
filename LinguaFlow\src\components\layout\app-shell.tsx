
"use client";

import React, { type ReactNode } from 'react';
import { Sidebar<PERSON>rovider, Sidebar, SidebarHeader, SidebarContent, SidebarFooter, SidebarTrigger, SidebarInset, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarCollapse } from '@/components/ui/sidebar';
import { Logo } from '@/components/icons/logo';
import { Button } from '@/components/ui/button';
import { useI18n } from '@/contexts/i18n-context';
import { useAppearance } from '@/contexts/theme-context';
import { Moon, Sun, Home, Settings, HelpCircle, User, Globe } from 'lucide-react';
import { useState } from 'react';
import { SettingsPanel } from '@/components/settings/settings-panel';
import { HelpPanel } from '@/components/help/help-panel';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';
import { Label } from '@/components/ui/label';
import { Separator } from '../ui/separator';
import { WritingModeSelector } from '../ai/writing-mode-selector';

interface AppShellProps {
  children: (props: { writingMode: string }) => ReactNode;
}

export function AppShell({ children }: AppShellProps) {
  const { t, uiLanguage, setUiLanguage } = useI18n();
  const { theme, setTheme, effectiveTheme } = useAppearance();
  const [activeView, setActiveView] = useState('editor');
  const [writingMode, setWritingMode] = useState("Formal");

  const mainContent = () => {
    switch (activeView) {
      case 'settings':
        return <SettingsPanel />;
      case 'help':
        return <HelpPanel />;
      default:
        return children({ writingMode });
    }
  };
  
  const sidebarDirection = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === uiLanguage)?.dir === 'rtl' ? 'right' : 'left';
  const isRTL = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === uiLanguage)?.dir === 'rtl';

  return (
    <SidebarProvider>
      <Sidebar side={sidebarDirection}>
        <SidebarHeader>
          <div className={`flex items-center gap-2 ${isRTL ? 'rtl-flex-reverse' : ''}`}>
            <Logo className="h-10 w-10" />
            <div className={`flex flex-col group-data-[collapsible=icon]:hidden ${isRTL ? 'text-right' : 'text-left'}`}>
                <h2 className="text-lg font-semibold text-sidebar-primary">{t('appName')}</h2>
                <p className="text-xs text-muted-foreground">{t('appDescription')}</p>
            </div>
          </div>
          <div className={`${isRTL ? 'mr-auto' : 'ml-auto'} flex items-center gap-2`}>
            <SidebarCollapse className="group-data-[collapsible=icon]:hidden"/>
          </div>
        </SidebarHeader>
        <SidebarContent>
            <SidebarMenu className={isRTL ? 'sidebar-menu' : ''}>
                <SidebarMenuItem>
                    <SidebarMenuButton
                        onClick={(e) => { e.preventDefault(); setActiveView('editor'); }}
                        isActive={activeView === 'editor'}
                        tooltip={{ children: t('editorTitle') }}
                        className={isRTL ? 'sidebar-menu-button' : ''}
                    >
                        <Home className={isRTL ? 'icon-left' : ''}/>
                        <span>{t('editorTitle')}</span>
                    </SidebarMenuButton>
                </SidebarMenuItem>
                 <SidebarMenuItem>
                    <SidebarMenuButton
                        onClick={(e) => { e.preventDefault(); setActiveView('settings'); }}
                        isActive={activeView === 'settings'}
                        tooltip={{ children: t('settingsTitle') }}
                        className={isRTL ? 'sidebar-menu-button' : ''}
                    >
                        <Settings className={isRTL ? 'icon-left' : ''} />
                        <span>{t('settingsTitle')}</span>
                    </SidebarMenuButton>
                </SidebarMenuItem>
                 <SidebarMenuItem>
                    <SidebarMenuButton
                        onClick={(e) => { e.preventDefault(); setActiveView('help'); }}
                        isActive={activeView === 'help'}
                        tooltip={{ children: t('helpTitle') }}
                        className={isRTL ? 'sidebar-menu-button' : ''}
                    >
                        <HelpCircle className={isRTL ? 'icon-left' : ''} />
                        <span>{t('helpTitle')}</span>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarContent>
        <SidebarFooter className="border-t border-sidebar-border">
          <Separator className="my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden" />
          <div className={`flex items-center justify-between gap-2 group-data-[collapsible=icon]:justify-center p-2 ${isRTL ? 'rtl-flex-reverse' : ''}`}>
            <Button 
                variant="ghost" 
                size="icon" 
                className="text-sidebar-foreground hover:bg-sidebar-accent"
                onClick={() => setTheme(effectiveTheme === 'light' ? 'dark' : 'light')} 
                aria-label={effectiveTheme === 'light' ? t('switchToDarkMode') : t('switchToLightMode')}
            >
              {effectiveTheme === 'dark' ? <Sun/> : <Moon />}
            </Button>
            <Select value={uiLanguage} onValueChange={setUiLanguage}>
              <SelectTrigger 
                className="h-9 text-xs bg-sidebar-background border-sidebar-border text-sidebar-foreground focus:ring-sidebar-ring group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:justify-center"
                aria-label={t('selectLanguagePlaceholder')}
              >
                  <Globe className="h-4 w-4" />
                  <span className="group-data-[collapsible=icon]:hidden">
                    <SelectValue placeholder={t('selectLanguagePlaceholder')} />
                  </span>
              </SelectTrigger>
              <SelectContent>
                {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value} className="text-xs">
                    {t(lang.labelKey)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Separator className="my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden" />
          <div className="hidden items-center justify-center group-data-[collapsible=icon]:flex">
             <SidebarCollapse />
          </div>
        </SidebarFooter>
      </Sidebar>
      
      <SidebarInset className="flex flex-col">
        <header className="sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4 sm:px-6">
            <SidebarTrigger className="md:hidden"/>
            <div className="flex-1">
                <WritingModeSelector value={writingMode} onChange={setWritingMode} />
            </div>
        </header>
        <main className="flex-1 overflow-auto">
            {mainContent()}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
