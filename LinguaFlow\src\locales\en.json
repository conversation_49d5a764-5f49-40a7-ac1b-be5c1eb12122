{"appName": "LinguaFlow", "appDescription": "Grammar Correction & Writing Assistant", "editorTitle": "Editor", "rephraseSelectionButton": "Rephrase Selection", "writingStatsTitle": "Writing Statistics", "wordCountLabel": "Word Count", "charCountLabel": "Character Count", "writingScoreLabel": "Writing Score", "writingScoreUnit": "/ 100", "writingModeLabel": "Writing Mode", "selectWritingModePlaceholder": "Select writing mode", "formalWritingMode": "Formal", "casualWritingMode": "Casual", "professionalWritingMode": "Professional", "creativeWritingMode": "Creative", "technicalWritingMode": "Technical", "academicWritingMode": "Academic", "businessWritingMode": "Business", "aiToneAnalysisAccordionTitle": "Tone Analysis", "aiToneAnalysisTitle": "Tone Analysis", "aiToneAnalysisDescription": "Get feedback on your writing's formality and confidence.", "analyzeToneButton": "Analyze Text Tone", "formalityLabel": "Formality", "confidenceLabel": "Confidence", "feedbackLabel": "<PERSON><PERSON><PERSON>", "writeSomeTextToAnalyzePlaceholder": "Write some text in the editor to analyze its tone.", "aiTextGenerationAccordionTitle": "AI Content Generation", "aiTextGenerationTitle": "AI Content Generator", "aiTextGenerationDescription": "Generate content based on your prompt.", "yourPromptLabel": "Your Prompt", "promptPlaceholder": "e.g., Write a short story about a robot who discovers music", "generatedTextLabel": "Generated Text", "generateTextButton": "Generate Text", "settingsAccordionTitle": "Settings", "settingsTitle": "Settings", "settingsDescription": "Customize your LinguaFlow experience.", "themeLabel": "Theme", "switchToLightMode": "Switch to Light Mode", "switchToDarkMode": "Switch to Dark Mode", "languageLabel": "Language", "selectLanguagePlaceholder": "Select language", "englishLanguage": "English", "englishUSDialect": "American English", "englishUKDialect": "British English", "englishCanadianDialect": "Canadian English", "arabicLanguage": "العربية (Arabic)", "turkishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Turkish)", "spanishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Spanish)", "germanLanguage": "<PERSON><PERSON><PERSON> (German)", "frenchLanguage": "<PERSON><PERSON><PERSON> (French)", "dutchLanguage": "<PERSON><PERSON><PERSON> (Dutch)", "italianLanguage": "<PERSON><PERSON> (Italian)", "startWritingPlaceholder": "Start writing here...", "rephrasePopoverTitle": "Rephrase Text", "rephrasePopoverDescription": "Review the suggestion for the selected text.", "originalTextLabel": "Original", "suggestionTextLabel": "Suggestion", "rephraseWaitMessage": "Click \"Rephrase\" or wait for suggestion.", "applyButton": "Apply", "cancelButton": "Cancel", "siteHeaderTitle": "LinguaFlow", "footerText": "Created by Engineer <PERSON> 2025©. All rights reserved®. We appreciate your support and thank you", "toastInputRequiredTitle": "Input Required", "toastEditorEmptyError": "Editor is empty. Please write some text to analyze.", "toastPromptRequiredError": "Please enter a prompt to generate text.", "toastSuccessTitle": "Success", "toastErrorTitle": "Error", "toastInfoTitle": "Info", "toastTextGeneratedSuccess": "Text generated successfully.", "toastTextGenerationError": "Failed to generate text. Please try again.", "toastToneAnalysisSuccess": "Tone analysis completed.", "toastToneAnalysisError": "Failed to analyze tone. Please try again.", "toastNothingToRephraseError": "Nothing to rephrase", "toastSelectTextToRephraseError": "Please select some text in the editor.", "toastSuggestionReady": "Suggestion ready", "toastRephraseError": "Failed to rephrase text. Please try again.", "toastFileUploadedSuccess": "File content uploaded to editor.", "toastFileTypeNotSupportedError": "File type not supported. Please upload a {{fileType}} file.", "plagiarismDetectionAccordionTitle": "Plagiarism Detection", "plagiarismDetectionTitle": "Plagiarism Detection", "plagiarismDetectionSettingsTitle": "Plagiarism Detection", "plagiarismDetectionDescription": "Protect your integrity with our plagiarism detection tool, designed to meticulously scan your content for unintended similarities with existing works, helping you maintain originality in your writing.", "detectPlagiarismButton": "Detect Plagiarism", "originalityScoreLabel": "Originality Score", "plagiarismReportLabel": "Analysis Report", "potentialSourcesFoundLabel": "Potential sources found", "originalSourceLabel": "Original Source", "similarityScoreLabel": "Similarity Score", "toastPlagiarismDetectionSuccess": "Plagiarism detection completed.", "toastPlagiarismDetectionError": "Failed to detect plagiarism. Please try again.", "writeSomeTextToDetectPlagiarismPlaceholder": "Write some text in the editor to check for plagiarism.", "aiWritingDetectionAccordionTitle": "AI Writing Detection", "aiWritingDetectionTitle": "AI Writing Detection", "aiWritingDetectionDescription": "Estimate the likelihood that your text was generated by artificial intelligence.", "detectAiWritingButton": "Detect AI Writing", "probabilityAIWrittenLabel": "Probability AI-written", "aiWritingDetectionSummaryLabel": "Analysis Summary", "toastAiWritingDetectionSuccess": "AI writing detection completed.", "toastAiWritingDetectionError": "Failed to detect AI writing. Please try again.", "writeSomeTextToDetectAiWritingPlaceholder": "Write some text in the editor to check for AI authorship.", "writingSuggestionsTitle": "Writing Suggestions", "analyzingTextDescription": "AI is analyzing your text for suggestions...", "suggestionsFoundDescription": "Found {{count}} suggestion(s). Review them below.", "noSuggestionsFoundDescription": "No immediate suggestions found. Keep writing or try rephrasing.", "startTypingForSuggestionsDescription": "Start typing for better writing suggestions.", "suggestionTypeSpelling": "Spelling", "suggestionTypeGrammar": "Grammar", "suggestionTypeRewrite": "Rewrite", "suggestionTypeStyle": "Style", "suggestionTypeUnknown": "Suggestion", "suggestionLabel": "Suggests", "applySuggestionButton": "Apply Suggestion", "suggestionExplanationTooltip": "Show explanation", "toastTextAnalysisError": "Failed to get writing suggestions. Please try again.", "toastSuggestionAppliedSuccess": "Suggestion applied.", "toastSuggestionApplyError": "Could not apply suggestion. The original text may have changed.", "humanizeAiTextAccordionTitle": "Humanize AI Text", "humanizeAiTextTitle": "Humanize AI Text", "humanizeAiTextDescription": "Rewrite AI-generated text to sound more human-like.", "humanizeTextButton": "Humanize Text", "humanizedTextLabel": "Humanized Text", "toastHumanizeTextSuccess": "Text humanized successfully.", "toastHumanizeTextError": "Failed to humanize text. Please try again.", "writeSomeTextToHumanizePlaceholder": "Write or paste AI-generated text in the editor to humanize it.", "clearEditorButton": "Clear", "clearEditorButtonAriaLabel": "Clear all text from the editor", "toastEditorClearedSuccess": "Editor content cleared.", "generationHistoryTitle": "Generation History", "noGenerationsYetPlaceholder": "No generations yet. Generate some text to see them here.", "promptLabel": "Prompt", "outputLabel": "Output", "useThisPromptButton": "Use This Prompt", "copyOutputButton": "Copy Output", "toastPromptRestoredSuccess": "Prompt restored to input field.", "toastTextCopiedSuccess": "Text copied to clipboard.", "toastTextCopyError": "Failed to copy text to clipboard.", "insertIntoEditorButton": "Insert into Editor", "insertIntoEditorButtonTooltip": "Append generated text to the editor", "toastTextInsertedSuccess": "Generated text inserted into editor.", "copyEditorButton": "Copy Text", "copyEditorButtonAriaLabel": "Copy all text from the editor", "toastEditorContentCopiedSuccess": "Editor content copied to clipboard.", "toastEditorContentCopyError": "Failed to copy editor content to clipboard.", "toastEditorEmptyForCopyError": "Editor is empty. Nothing to copy.", "recordVoiceButtonStart": "Record Voice", "recordVoiceButtonStop": "Stop Recording", "recordVoiceButtonAriaLabelStart": "Start voice recording to convert to text", "recordVoiceButtonAriaLabelStop": "Stop voice recording", "toastRecordingStarted": "Recording started. Speak into the microphone.", "toastRecordingStoppedNoTranscript": "Recording stopped. No speech was converted.", "toastSpeechTranscribedAndAppended": "Speech transcribed and appended to editor.", "toastSpeechRecognitionNotSupported": "Your browser does not support speech recognition.", "toastMicrophonePermissionDenied": "Microphone permission denied. Please enable it in browser settings and refresh the page.", "toastSpeechNoSpeechDetected": "No speech detected. Please try again.", "toastSpeechAudioCaptureError": "Audio capture error. Please check your microphone.", "toastSpeechNetworkError": "Network error during speech recognition. Check your connection.", "toastSpeechRecognitionError": "Speech recognition error: {{error}}", "toastSpeechServiceNotAllowed": "Speech recognition service not allowed or unavailable. Please try again later.", "toastSpeechLanguageNotSupportedError": "The selected language is not supported for speech recognition by your browser.", "helpTitle": "Help", "helpPanelTitle": "How to Use LinguaFlow", "helpPanelDescription": "Get started with <PERSON>ua<PERSON><PERSON>'s features.", "helpPanelIntro": "Welcome to LinguaFlow! This guide will help you navigate and use the app's powerful writing assistance tools.", "helpEditorTitle": "Editor", "helpAiToolsTitle": "AI Tools Panel", "helpLanguageSettingsTitle": "Language Settings", "helpAppearanceSettingsTitle": "Appearance Settings", "helpDictionarySettingsTitle": "Dictionary Settings", "helpFeatureSettingsTitle": "Feature Settings", "helpWritingAidSettingsTitle": "Writing Aid Settings", "helpAdvancedSettingsTitle": "Advanced Settings", "helpEditorDescription": "The editor is your main workspace. <br/><br/> <b>- Real-time suggestions:</b> As you type, the app automatically scans your text and underlines potential issues. Click on a highlighted segment to see a correction popup. <br/> <b>- AI tools on selection:</b> Select any part of your text to reveal the <b>Smart Synonyms</b> button. If you select a single word, you'll get synonyms and pronunciation guide. If you select a longer phrase, you'll get an AI-powered rephrasing suggestion. <br/> <b>- Formatting toolbar:</b> At the top of the editor, you'll find tools for undo/redo, changing fonts, and applying formatting like bullet points, numbered lists, and text alignment.", "helpAiToolsDescription": "The panels on the left and right provide powerful AI capabilities. <br/><br/> <b>- Writing tools (left):</b> Here you can change the <b>writing mode</b> to influence the AI's style, <b>import a document</b>, or use the <b>AI rewriter</b> to rephrase your entire text. You can also use tools to <b>humanize AI text</b>, check for <b>AI writing</b>, and detect <b>plagiarism</b>. <br/> <b>- Analysis tools (right):</b> This column shows real-time <b>writing statistics</b>, provides a <b>tone analyzer</b> for your text, and includes an <b>AI content generator</b> to create new text from a prompt. Your generation history is saved here for easy reuse.", "helpLanguageSettingsDescription": "Configure the <b>UI language</b> for the app interface and the <b>primary writing language</b> for analysis. For some languages, you can also select a <b>regional dialect</b>. Enable <b>automatic language detection</b> to have the app switch writing languages as you type.", "helpAppearanceSettingsDescription": "Customize the app's look and feel. Choose a <b>theme</b> (light, dark, or system), adjust the overall <b>font size</b>, and toggle <b>high contrast mode</b> for improved readability.", "helpDictionarySettingsDescription": "Manage your personal dictionaries for different languages. <b>Add</b> words you use frequently but might be flagged as misspellings (like names or technical terms). You can also <b>import</b> or <b>export</b> your dictionary list as a JSON file, or <b>clear</b> the dictionary for a specific language.", "helpFeatureSettingsDescription": "Fine-tune the behavior of specific AI and auto-correction features. Here you can turn on/off various generative AI tools, auto-correction behaviors, and core real-time checking functions to suit your workflow.", "helpWritingAidSettingsDescription": "Customize how the AI assists you. Set your <b>language proficiency</b> to get suggestions tailored to your skill level. You can also enable or disable features like <b>tone detection</b>, <b>plagiarism detection</b>, and specialized support for <b>non-native speakers</b>.", "helpAdvancedSettingsDescription": "Control core operational behaviors. Enable <b>offline functionality</b> to use basic features without an internet connection. If you want to start fresh, you can <b>reset all settings</b> to restore the app to its original default configuration (this cannot be undone).", "helpPanelTip": "Try different tools and settings to find what works best for your writing style and needs!", "writeToolsTitle": "Writing Tools", "importDocumentTitle": "Import Document", "quickActionTitle": "Quick Action", "toneAnalyzerTitle": "<PERSON><PERSON>", "aiRewriteAccordionTitle": "AI Rewrite", "aiRewriteTitle": "AI Rewriter", "aiRewriteDescription": "Rewrite the entire editor content to enhance clarity and style.", "rewriteEditorContentButton": "Rewrite Editor Content", "rewrittenTextLabel": "Rewritten Text", "applyToEditorButton": "Apply to Editor", "toastRewriteSuccess": "Editor content rewritten successfully.", "toastRewriteError": "Failed to rewrite editor content. Please try again.", "writeSomeTextToRewritePlaceholder": "Write some text in the editor to rewrite it.", "dropzoneInstruction": "Drop files here or browse", "toastFileImportSuccessTitle": "File Imported", "toastFileImportSuccessMessage": "Document content loaded.", "toastFileImportErrorTitle": "Import Error", "toastFileImportErrorMessage": "Could not read file content. Please ensure it's a valid .txt file.", "toastInvalidFileTypeMessage": "Invalid file type. Only .txt files are accepted.", "dropzoneAriaLabel": "Document import dropzone: Click or drag and drop a .txt file to upload.", "featuresLabel": "Features", "featureSettingsDescription": "Customize specific writing assistance feature functionality.", "appearanceLabel": "Appearance", "writingAidLabel": "Writing Aid", "dictionaryLabel": "Dictionary", "dictionarySettingsDescription": "Add custom words or manage personal dictionaries. (Placeholder)", "advancedSettingsLabel": "Advanced", "advancedSettingsDescription": "Access advanced configuration options. Use with caution. (Placeholder)", "uiLanguageLabel": "UI Language", "selectUiLanguagePlaceholder": "Select UI language...", "uiLanguageDescription": "Changes the app interface language.", "writingLanguageLabel": "Primary Writing Language", "selectWritingLanguagePlaceholder": "Select writing language...", "writingLanguageDescription": "Sets the primary language for AI analysis and generation.", "regionalDialectLabel": "Regional Dialect", "selectRegionalDialectPlaceholder": "Select dialect...", "regionalDialectDescription": "Specifies the regional variant of the selected writing language.", "languageProficiencyLabel": "Language Proficiency Level", "selectProficiencyPlaceholder": "Select proficiency level...", "languageProficiencyDescription": "Helps the AI tailor suggestions to your language skill level.", "proficiencyNative": "Native", "proficiencyAdvanced": "Advanced (C1/C2)", "proficiencyIntermediate": "Intermediate (B1/B2)", "proficiencyBeginner": "<PERSON><PERSON><PERSON> (A1/A2)", "languageEnglishGeneral": "English", "languageSpanishGeneral": "Spanish", "languageFrenchGeneral": "French", "languageGermanGeneral": "German", "languageItalianGeneral": "Italian", "languageDutchGeneral": "Dutch", "languageArabicGeneral": "Arabic", "arabicSyriaLanguage": "Arabic (Syria)", "arabicSaudiArabiaLanguage": "Arabic (Saudi Arabia)", "arabicEgyptLanguage": "Arabic (Egypt)", "languageTurkishGeneral": "Turkish", "spanishSpainLanguage": "Spanish (Spain)", "spanishMexicoLanguage": "Spanish (Mexico)", "themeLight": "Light", "themeDark": "Dark", "themeSystem": "System Default", "selectThemePlaceholder": "Select theme...", "themeDescription": "Choose the visual theme for the app.", "fontSizeLabel": "Font Size", "selectFontSizePlaceholder": "Select font size...", "fontSizeSmall": "Small", "fontSizeMedium": "Medium", "fontSizeLarge": "Large", "fontSizeDescription": "Adjust text size throughout the app.", "highContrastModeLabel": "High Contrast Mode", "highContrastModeDescription": "Increases text/background contrast for improved readability.", "enabledLabel": "Enabled", "disabledLabel": "Disabled", "personalDictionaryLabel": "Personal Dictionary", "personalDictionaryDescription": "Add words you use frequently that might be flagged as errors.", "addWordPlaceholder": "Enter word...", "addWordButton": "Add Word", "deleteWordButtonAria": "Delete word {{word}}", "dictionaryEmptyPlaceholder": "Your dictionary is empty. Add some words!", "dictionaryImportExportLabel": "Import / Export Dictionary", "importDictionaryButton": "Import", "exportDictionaryButton": "Export", "dictionaryImportExportDescription": "Backup or share your personal dictionary as a JSON file.", "clearDictionaryForLanguageButton": "Clear {{language}} Dictionary", "clearDictionaryConfirmTitle": "Are you sure?", "clearDictionaryForLanguageConfirmDescription": "This will permanently delete all words from your personal dictionary for {{language}}. This action cannot be undone.", "confirmClearButton": "Yes, Clear Dictionary", "clearDictionaryWarning": "This action cannot be undone.", "toastDictionaryWordAdded": "Added word '{{word}}' to dictionary.", "toastDictionaryWordExists": "Word '{{word}}' already exists in dictionary.", "toastDictionaryWordEmpty": "Cannot add empty word.", "toastDictionaryWordDeleted": "Deleted word '{{word}}' from dictionary.", "toastDictionaryImportOverwriteSuccess": "Imported {{count}} words, overwriting dictionary.", "toastDictionaryImportMergeSuccess": "Imported {{count}} new words and merged.", "toastDictionaryImportInvalidFormat": "Invalid dictionary file format. Must be a JSON array of strings.", "toastDictionaryImportError": "Error importing dictionary file.", "toastDictionaryExportSuccess": "Dictionary exported successfully.", "toastDictionaryCleared": "Personal dictionary cleared.", "generativeAiFeaturesLabel": "Generative AI Features", "showAiSuggestionsOnTextSelectionLabel": "Show AI suggestions on text selection", "showAiSuggestionsOnTextSelectionDescription": "Enable AI suggestions when selecting text", "quickAiActionsForSelectedTextLabel": "Quick AI actions for selected text", "quickAiActionsForSelectedTextDescription": "Access quick AI actions for selected text", "aiQuickReplySuggestionsLabel": "AI-powered quick reply suggestions", "aiQuickReplySuggestionsDescription": "Receive ideas for responses. (<PERSON><PERSON> feature)", "viewAndReuseRecentPromptHistoryLabel": "View and reuse recent prompt history", "viewAndReuseRecentPromptHistoryDescription": "Easily access your latest AI prompts", "autoCorrectionFeaturesLabel": "Auto-correction Features", "automaticTextCorrectionLabel": "Automatic text correction", "automaticTextCorrectionDescription": "Automatically fix common spelling and grammar errors", "realTimeCorrectionLabel": "Real-time correction", "realTimeCorrectionDescription": "Apply corrections as you type", "sentenceEnhancementLabel": "Sentence enhancement", "sentenceEnhancementDescription": "Get suggestions to improve clarity and style", "showCorrectionFeedbackLabel": "Show correction feedback", "showCorrectionFeedbackDescription": "Receive notifications when corrections are applied", "coreCheckingFeaturesLabel": "Core Checking Features", "realTimeGrammarCheckingLabel": "Real-time grammar checking", "realTimeGrammarCheckingDescription": "Get instant feedback on grammar while typing", "realTimeSpellCheckingLabel": "Real-time spell checking", "realTimeSpellCheckingDescription": "Check for spelling errors in real-time", "styleSuggestionsLabel": "Style suggestions", "styleSuggestionsDescription": "Get suggestions to improve your writing style.", "toastLanguageSwitched": "Writing language automatically switched to {{language}}.", "writingAssistanceTitle": "Writing Assistance", "writingAssistanceDescription": "Enhance your LinguaFlow writing experience by customizing how we assist you.", "yourLanguageProficiencyTitle": "Your Language Proficiency (for Primary Language)", "yourLanguageProficiencyDescription": "Get precisely tailored suggestions that align with your evolving understanding of the language, ensuring a more nuanced approach to your writing.", "toneDetectionTitle": "Tone Detection", "toneDetectionDescription": "Delve into the emotional undertones of your text with our tone detection feature, which analyzes your writing and provides insightful suggestions to enhance and elevate your tone, making it resonate more effectively with your audience.", "nonNativeSupportTitle": "Non-Native Speaker Support", "nonNativeSupportDescription": "Benefit from specialized assistance tailored for non-native speakers, offering thoughtful guidance and practical tips to enhance your fluency and confidence in writing.", "advancedSettingsTitle": "Advanced Settings", "enableOfflineFunctionalityLabel": "Enable offline functionality", "enableOfflineFunctionalityDescription": "Core features, including settings and dictionary, are available offline. Key components will be included to ensure the program works smoothly.", "enableAutomaticLanguageDetectionLabel": "Enable automatic language detection", "enableAutomaticLanguageDetectionDescription": "Automatically detect and switch writing language as you type.", "dataManagementLabel": "Data Management", "resetAllSettingsLabel": "Reset all settings", "resetAllSettingsDescription": "This will reset all customizations including theme, language, and feature settings to their default values. This action cannot be undone.", "resetButtonLabel": "Reset", "resetAllSettingsConfirmTitle": "Are you sure you want to reset all settings?", "resetAllSettingsConfirmDescription": "All your personal settings, dictionary words, and preferences will be permanently deleted and returned to the app's default settings. This action cannot be undone.", "confirmResetButton": "Yes, Reset Everything", "toastResetSuccess": "All settings have been reset to default. The app will now reload.", "dictionaryLanguageLabel": "Dictionary Language", "selectDictionaryLanguagePlaceholder": "Select language to view dictionary...", "dictionaryLanguageDescription": "View and manage dictionary for a specific language.", "toastSuggestionDismissed": "Suggestion dismissed.", "dismissButton": "<PERSON><PERSON><PERSON>", "correctButton": "Correct", "undoButton": "Undo", "redoButton": "Redo", "aiToolsButton": "Smart Synonyms", "wordToolkitTitle": "Synonym Suggestions", "wordToolkitDescription": "Select a single word in the editor to get synonyms and hear its pronunciation.", "wordToolkitPlaceholder": "To get smart synonyms, select a single word in the main editor.", "selectedWordLabel": "Selected Word", "synonymsLabel": "Synonyms", "noSynonymsFound": "No synonyms found.", "applySynonymTooltip": "Replace word with '{{synonym}}'", "toastWordToolkitError": "Failed to get suggestions for the selected word.", "toastWordReplacedSuccess": "Word replaced with '{{word}}'.", "wordToolkitPopoverDescription": "Get synonyms or listen to the word's pronunciation.", "spellingAndPronunciationLabel": "Pronunciation", "pronounceButton": "Pronounce Word", "toastPronunciationError": "Failed to generate audio pronunciation.", "localVerbRepetitionDetectionLabel": "Detect Repeated Verbs Locally", "localVerbRepetitionDetectionDescription": "Highlight repeated verbs and suggest dynamic alternatives (English only).", "aiToolsTitle": "AI Tools", "editorPlaceholder": "Start writing here...", "englishUSLanguage": "English (US)", "englishUKLanguage": "English (UK)"}