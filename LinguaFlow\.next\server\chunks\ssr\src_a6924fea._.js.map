{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/contextual-ai-rephraser.ts"], "sourcesContent": ["'use server';\n/**\n * @fileOverview An AI agent that rephrases text based on context.\n *\n * - rephraseText - A function that handles the text rephrasing process.\n * - RephraseTextInput - The input type for the rephraseText function.\n * - RephraseTextOutput - The return type for the rephraseText function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst RephraseTextInputSchema = z.object({\n  selectedText: z.string().describe('The text selected by the user to rephrase.'),\n  contextText: z.string().describe('The surrounding context of the selected text.'),\n  tone: z.string().optional().describe('The desired tone of the rephrased text.'),\n  style: z.string().optional().describe('The desired style of the rephrased text.'),\n});\nexport type RephraseTextInput = z.infer<typeof RephraseTextInputSchema>;\n\nconst RephraseTextOutputSchema = z.object({\n  rephrasedText: z.string().describe('The rephrased text based on the context.'),\n});\nexport type RephraseTextOutput = z.infer<typeof RephraseTextOutputSchema>;\n\nexport async function rephraseText(input: RephraseTextInput): Promise<RephraseTextOutput | null> {\n  return rephraseTextFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'rephraseTextPrompt',\n  input: {schema: RephraseTextInputSchema},\n  output: {schema: RephraseTextOutputSchema},\n  prompt: `You are an AI assistant that helps users rephrase text to improve clarity and flow.\n\n  Selected Text: {{{selectedText}}}\n  Context: {{{contextText}}}\n  Tone: {{{tone}}}\n  Style: {{{style}}}\n\n  Rephrased Text:`,\n});\n\nconst rephraseTextFlow = ai.defineFlow(\n  {\n    name: 'rephraseTextFlow',\n    inputSchema: RephraseTextInputSchema,\n    outputSchema: RephraseTextOutputSchema.nullable(),\n  },\n  async (input: RephraseTextInput): Promise<RephraseTextOutput | null> => {\n    try {\n      const {output} = await prompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[rephraseTextFlow] - Error: ${errorMessage}`, {input});\n      // Return null on error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAyBsB", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/word-toolkit-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to provide synonyms and spelling for a word.\n *\n * - getWordSuggestions - A function that handles the word analysis process.\n * - WordToolkitInput - The input type for the getWordSuggestions function.\n * - WordToolkitOutput - The return type for the getWordSuggestions function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst WordToolkitInputSchema = z.object({\n  word: z.string().describe('The single word to be analyzed.'),\n  context: z\n    .string()\n    .describe('The surrounding sentence or text to provide context.'),\n  language: z\n    .string()\n    .describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\").'),\n});\nexport type WordToolkitInput = z.infer<typeof WordToolkitInputSchema>;\n\nconst WordToolkitOutputSchema = z.object({\n  synonyms: z\n    .array(z.string())\n    .describe('An array of relevant synonyms for the word, based on its context. Should be 5 or less.'),\n  correctSpelling: z\n    .string()\n    .describe(\n      'The correct spelling of the word. If the word is already spelled correctly, returns the original word.'\n    ),\n});\nexport type WordToolkitOutput = z.infer<typeof WordToolkitOutputSchema>;\n\nexport async function getWordSuggestions(\n  input: WordToolkitInput\n): Promise<WordToolkitOutput> {\n  return wordToolkitFlow(input);\n}\n\nconst wordToolkitPrompt = ai.definePrompt({\n  name: 'wordToolkitPrompt',\n  input: {schema: WordToolkitInputSchema},\n  output: {schema: WordToolkitOutputSchema},\n  prompt: `You are a linguistic expert providing quick tools for writers. You will be given a specific word, its surrounding context, and its language.\n\nYour task is to provide a list of synonyms and the correct spelling for the given word in the specified language ({{language}}).\n\n- Synonyms should be relevant to the word's usage in the provided context. Provide up to 5 synonyms. If no relevant synonyms are found, return an empty array.\n- For spelling, if the word is already spelled correctly, return the word itself. If it is misspelled, return the correct spelling.\n\nAnalyze the following:\nWord: {{{word}}}\nContext: {{{context}}}\nLanguage: {{language}}\n`,\n});\n\nconst wordToolkitFlow = ai.defineFlow(\n  {\n    name: 'wordToolkitFlow',\n    inputSchema: WordToolkitInputSchema,\n    outputSchema: WordToolkitOutputSchema,\n  },\n  async (input: WordToolkitInput): Promise<WordToolkitOutput> => {\n    try {\n      const {output} = await wordToolkitPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : String(error);\n      console.error(`[wordToolkitFlow] - Error: ${errorMessage}`, {input});\n      // Return a default/empty state to prevent the client from crashing\n      // on transient errors like API quota limits.\n      return {\n        synonyms: [],\n        correctSpelling: input.word,\n      };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAoCsB", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-to-speech-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to convert a word into spoken audio of its spelling.\n *\n * - getSpelledOutAudio - A function that takes a word and generates a WAV audio data URI of it being spelled out.\n * - SpelledOutAudioInput - The input type for the getSpelledOutAudio function.\n * - SpelledOutAudioOutput - The return type for the getSpelledOutAudio function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {googleAI} from '@genkit-ai/googleai';\nimport {z} from 'genkit';\nimport wav from 'wav';\n\nconst SpelledOutAudioInputSchema = z.object({\n  word: z.string().describe('The word to be spelled out.'),\n  lang: z.string().describe('The BCP-47 language code for the pronunciation voice.'),\n});\nexport type SpelledOutAudioInput = z.infer<typeof SpelledOutAudioInputSchema>;\n\nconst SpelledOutAudioOutputSchema = z.object({\n  audioDataUri: z.string().nullable().describe(\"A data URI of the WAV audio file. Expected format: 'data:audio/wav;base64,<encoded_data>'. Is null on failure.\"),\n});\nexport type SpelledOutAudioOutput = z.infer<typeof SpelledOutAudioOutputSchema>;\n\nexport async function getSpelledOutAudio(input: SpelledOutAudioInput): Promise<SpelledOutAudioOutput> {\n  return spellWordToAudioFlow(input);\n}\n\nasync function toWav(\n  pcmData: Buffer,\n  channels = 1,\n  rate = 24000,\n  sampleWidth = 2\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const writer = new wav.Writer({\n      channels,\n      sampleRate: rate,\n      bitDepth: sampleWidth * 8,\n    });\n\n    const bufs: Buffer[] = [];\n    writer.on('error', reject);\n    writer.on('data', (d) => {\n      bufs.push(d);\n    });\n    writer.on('end', () => {\n      resolve(Buffer.concat(bufs).toString('base64'));\n    });\n\n    writer.write(pcmData);\n    writer.end();\n  });\n}\n\nconst spellWordToAudioFlow = ai.defineFlow(\n  {\n    name: 'spellWordToAudioFlow',\n    inputSchema: SpelledOutAudioInputSchema,\n    outputSchema: SpelledOutAudioOutputSchema,\n  },\n  async ({ word, lang }) => {\n    try {\n      // Format the word to be spelled out letter by letter.\n      const spelledOutWord = word.split('').join(' ');\n\n      const { media } = await ai.generate({\n        model: googleAI.model('gemini-2.5-flash-preview-tts'),\n        config: {\n          responseModalities: ['AUDIO'],\n          speechConfig: {\n            voiceConfig: {\n              prebuiltVoiceConfig: { voiceName: 'Algenib' }, // A standard voice\n            },\n          },\n        },\n        prompt: spelledOutWord,\n      });\n\n      if (!media) {\n        throw new Error('No audio media was generated by the model.');\n      }\n\n      const audioBuffer = Buffer.from(\n        media.url.substring(media.url.indexOf(',') + 1),\n        'base64'\n      );\n      \n      const wavBase64 = await toWav(audioBuffer);\n\n      return {\n        audioDataUri: 'data:audio/wav;base64,' + wavBase64,\n      };\n    } catch (error) {\n       const errorMessage = error instanceof Error ? error.message : String(error);\n       console.error(`[spellWordToAudioFlow] - Error: ${errorMessage}`, { word, lang });\n       // Return null instead of throwing an error to prevent server crashes.\n       return { audioDataUri: null };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IA0BsB", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/word-toolkit-popover.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { PopoverContent } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from \"@/components/ui/button\";\nimport { Loader2, Volume2 } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useToast } from '@/hooks/use-toast';\nimport { getWordSuggestions, type WordToolkitOutput, type WordToolkitInput } from '@/ai/flows/word-toolkit-flow';\nimport { getSpelledOutAudio, type SpelledOutAudioInput } from '@/ai/flows/text-to-speech-flow';\n\ninterface WordToolkitPopoverProps {\n  selectedWord: string;\n  contextText: string;\n  language: string;\n  onSynonymSelect: (synonym: string) => void;\n}\n\nexport function WordToolkitPopover({ selectedWord, contextText, language, onSynonymSelect }: WordToolkitPopoverProps) {\n  const { t } = useI18n();\n  const { toast } = useToast();\n  const [isLoading, setIsLoading] = useState(true);\n  const [analysisResult, setAnalysisResult] = useState<WordToolkitOutput | null>(null);\n  const [isPronouncing, setIsPronouncing] = useState(false);\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  const handleWordAnalysis = useCallback(async () => {\n    setIsLoading(true);\n    setAnalysisResult(null);\n    try {\n      const input: WordToolkitInput = {\n        word: selectedWord,\n        context: contextText,\n        language,\n      };\n      const result = await getWordSuggestions(input);\n      setAnalysisResult(result);\n    } catch (error) {\n      console.error(\"Error in word toolkit analysis:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastWordToolkitError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  }, [selectedWord, contextText, language, toast]);\n\n  useEffect(() => {\n    if (selectedWord) {\n        handleWordAnalysis();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedWord, contextText, language]);\n\n  const handlePronounce = async () => {\n    if (!analysisResult?.correctSpelling) return;\n    setIsPronouncing(true);\n    try {\n      const input: SpelledOutAudioInput = {\n        word: analysisResult.correctSpelling,\n        lang: language,\n      };\n      const { audioDataUri } = await getSpelledOutAudio(input);\n      \n      if (audioDataUri && audioRef.current) {\n        audioRef.current.src = audioDataUri;\n        audioRef.current.play();\n      } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n      }\n    } catch (error) {\n      console.error(\"Error generating or playing pronunciation:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n    } finally {\n      setIsPronouncing(false);\n    }\n  };\n\n  return (\n    <PopoverContent className=\"w-80\" side=\"top\" align=\"start\">\n      <div className=\"grid gap-4\">\n        <div className=\"space-y-2\">\n          <h4 className=\"font-medium leading-none\">{t('wordToolkitTitle')}</h4>\n          <p className=\"text-sm text-muted-foreground\">{t('wordToolkitPopoverDescription')}</p>\n        </div>\n\n        {isLoading && (\n          <div className=\"flex items-center justify-center h-24\">\n            <Loader2 className=\"h-5 w-5 animate-spin text-primary\" />\n          </div>\n        )}\n\n        {!isLoading && analysisResult && (\n          <div className=\"space-y-4\">\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('synonymsLabel')}</h5>\n              <div className=\"flex flex-wrap gap-2\">\n                {analysisResult.synonyms.length > 0 ? (\n                  analysisResult.synonyms.map((synonym) => (\n                    <Badge\n                      key={synonym}\n                      variant=\"secondary\"\n                      className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground\"\n                      onClick={() => onSynonymSelect(synonym)}\n                      title={t('applySynonymTooltip', { synonym })}\n                    >\n                      {synonym}\n                    </Badge>\n                  ))\n                ) : (\n                  <p className=\"text-xs text-muted-foreground\">{t('noSynonymsFound')}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('pronunciationLabel')}</h5>\n              <div className=\"flex items-center justify-between gap-2 p-2 bg-muted rounded-md\">\n                <span className=\"text-sm font-semibold\">{analysisResult.correctSpelling}</span>\n                <Button size=\"icon\" variant=\"ghost\" className=\"h-7 w-7\" onClick={handlePronounce} disabled={isPronouncing} title={t('pronounceButton')}>\n                  {isPronouncing ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Volume2 className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n      <audio ref={audioRef} className=\"hidden\" onEnded={() => setIsPronouncing(false)} onError={() => setIsPronouncing(false)}/>\n    </PopoverContent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBO,SAAS,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAA2B;IAClH,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAEjD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,aAAa;QACb,kBAAkB;QAClB,IAAI;YACF,MAAM,QAA0B;gBAC9B,MAAM;gBACN,SAAS;gBACT;YACF;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;YACxC,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAyB,SAAS;YAAc;QACvG,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAc;QAAa;QAAU;KAAM;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YACd;QACJ;IACF,uDAAuD;IACvD,GAAG;QAAC;QAAc;QAAa;KAAS;IAExC,MAAM,kBAAkB;QACtB,IAAI,CAAC,gBAAgB,iBAAiB;QACtC,iBAAiB;QACjB,IAAI;YACF,MAAM,QAA8B;gBAClC,MAAM,eAAe,eAAe;gBACpC,MAAM;YACR;YACA,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE;YAElD,IAAI,gBAAgB,SAAS,OAAO,EAAE;gBACpC,SAAS,OAAO,CAAC,GAAG,GAAG;gBACvB,SAAS,OAAO,CAAC,IAAI;YACvB,OAAO;gBACL,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAA2B,SAAS;gBAAc;YACzG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA2B,SAAS;YAAc;QACzG,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,iBAAc;QAAC,WAAU;QAAO,MAAK;QAAM,OAAM;;0BAChD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B,EAAE;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;0CAAiC,EAAE;;;;;;;;;;;;oBAGjD,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAItB,CAAC,aAAa,gCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,eAAe,QAAQ,CAAC,MAAM,GAAG,IAChC,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC3B,8OAAC,iIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,gBAAgB;gDAC/B,OAAO,EAAE,uBAAuB;oDAAE;gDAAQ;0DAEzC;+CANI;;;;sEAUT,8OAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;;;;;;0CAKtD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,eAAe,eAAe;;;;;;0DACvE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,SAAQ;gDAAQ,WAAU;gDAAU,SAAS;gDAAiB,UAAU;gDAAe,OAAO,EAAE;0DACjH,8BAAgB,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAA4B,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/F,8OAAC;gBAAM,KAAK;gBAAU,WAAU;gBAAS,SAAS,IAAM,iBAAiB;gBAAQ,SAAS,IAAM,iBAAiB;;;;;;;;;;;;AAGvH", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-debounce.ts"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    // Set debouncedValue to value (passed in) after the specified delay\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    // Return a cleanup function that will be called every time ...\n    // ... useEffect is re-called. useEffect will only be re-called ...\n    // ... if value or delay changes (see the inputs array below). \n    // This is how we prevent debouncedValue from changing if value is ...\n    // ... changing within the delay period. Timeout gets cleared and restarted.\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]); // Only re-call effect if value or delay changes\n\n  return debouncedValue;\n}\n"], "names": [], "mappings": ";;;AAGA;AAFA;;AAIO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,+DAA+D;QAC/D,mEAAmE;QACnE,+DAA+D;QAC/D,sEAAsE;QACtE,4EAA4E;QAC5E,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM,GAAG,gDAAgD;IAEpE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/enhanced-text-editor.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useCallback, useEffect, forwardRef, useMemo, type ChangeEvent, type UIEvent } from 'react';\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { FileText, IterationCw, Loader2, Eraser, Copy, Check, Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, Undo2, Redo2, Wand2, List, ListOrdered, ShieldCheck } from \"lucide-react\";\nimport { rephraseText, type RephraseTextInput } from '@/ai/flows/contextual-ai-rephraser';\nimport { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { useToast } from '@/hooks/use-toast';\nimport { Label } from '../ui/label';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { cn } from '@/lib/utils';\nimport { Separator } from '../ui/separator';\nimport { WordToolkitPopover } from './word-toolkit-popover';\nimport { useDebounce } from '@/hooks/use-debounce';\n\nexport interface SelectionDetail {\n  text: string;\n  start: number;\n  end: number;\n}\n\nexport type PlagiarismSource = Omit<AnalysisSuggestion, 'type'> & { type: 'plagiarism' };\nexport type RephraseSource = Omit<AnalysisSuggestion, 'type'> & { type: 'rephrase' };\n\ntype CombinedSuggestion = AnalysisSuggestion | PlagiarismSource;\n\ninterface EnhancedTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  writingMode: string;\n  direction: 'ltr' | 'rtl';\n  suggestions: AnalysisSuggestion[];\n  plagiarismSources: PlagiarismSource[];\n  onApplySuggestion: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;\n  onDismissSuggestion: (suggestionId: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n}\n\ninterface SuggestionPopoverProps {\n    suggestion: AnalysisSuggestion, \n    onApply: (s: AnalysisSuggestion) => void,\n    onDismiss: (id: string) => void,\n    onClose: () => void;\n}\n\ninterface PlagiarismPopoverProps {\n    source: PlagiarismSource, \n    onClose: () => void;\n}\n\nconst fontOptions = [\n    { value: \"'Inter', sans-serif\", label: 'Inter' },\n    { value: \"'Source Code Pro', monospace\", label: 'Source Code Pro' },\n    { value: \"'Georgia', serif\", label: 'Georgia' },\n    { value: \"'Times New Roman', Times, serif\", label: 'Times New Roman' },\n];\n\n\n// A single popover for inline suggestions\nfunction SuggestionPopover({ \n    suggestion, \n    onApply,\n    onDismiss,\n    onClose,\n}: SuggestionPopoverProps) {\n    const { t } = useI18n();\n\n    const handleApply = () => {\n        onApply(suggestion);\n        onClose();\n    };\n\n    const handleDismiss = () => {\n        onDismiss(suggestion.id);\n        onClose();\n    };\n\n    return (\n        <PopoverContent className=\"w-auto max-w-sm p-3 shadow-xl\" side=\"top\" align=\"start\" onEscapeKeyDown={onClose}>\n            <div className=\"space-y-3\">\n                <p className=\"text-sm text-muted-foreground\">{suggestion.message}</p>\n                <div className=\"p-2 rounded-md bg-muted border\">\n                    <p className=\"text-sm text-destructive line-through\">{suggestion.originalSegment}</p>\n                    <p className=\"text-sm text-primary font-semibold\">{suggestion.suggestion}</p>\n                </div>\n                <div className=\"flex justify-end gap-2\">\n                    <Button size=\"sm\" variant=\"ghost\" onClick={handleDismiss}>\n                        {t('dismissButton')}\n                    </Button>\n                    <Button size=\"sm\" onClick={handleApply}>\n                        <Check className=\"mr-2 h-4 w-4\" />\n                        {t('correctButton')}\n                    </Button>\n                </div>\n            </div>\n        </PopoverContent>\n    );\n}\n\nfunction PlagiarismPopover({ source, onClose }: PlagiarismPopoverProps) {\n    const { t } = useI18n();\n\n    return (\n        <PopoverContent className=\"w-auto max-w-sm p-3 shadow-xl\" side=\"top\" align=\"start\" onEscapeKeyDown={onClose}>\n            <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-2 font-semibold text-destructive\">\n                    <ShieldCheck className=\"h-5 w-5\" />\n                    {t('plagiarismReportLabel')}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">{source.message}</p>\n                <div className=\"p-2 rounded-md bg-destructive/10 border border-destructive/20\">\n                    <p className=\"text-sm text-destructive\">\n                        <span className=\"font-semibold\">{t('originalSourceLabel')}:</span>{' '}\n                        <a href={source.suggestion} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline break-all\">\n                            {source.suggestion}\n                        </a>\n                    </p>\n                </div>\n                <div className=\"flex justify-end\">\n                    <Button size=\"sm\" variant=\"ghost\" onClick={onClose}>\n                        {t('dismissButton')}\n                    </Button>\n                </div>\n            </div>\n        </PopoverContent>\n    );\n}\n\n\nexport const EnhancedTextEditor = forwardRef<HTMLDivElement, EnhancedTextEditorProps>(\n  ({ value, onChange, writingMode, direction, suggestions, plagiarismSources, onApplySuggestion, onDismissSuggestion, onUndo, onRedo, canUndo, canRedo }, ref) => {\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const backdropRef = useRef<HTMLDivElement>(null);\n  const { t, getWritingLanguageBase } = useI18n();\n  const { toast } = useToast();\n  \n  const [selectedTextDetail, setSelectedTextDetail] = useState<SelectionDetail | null>(null);\n  const debouncedSelection = useDebounce(selectedTextDetail, 300);\n\n  const [isAiPopoverOpen, setIsAiPopoverOpen] = useState(false);\n  const [rephrasedText, setRephrasedText] = useState<string>(\"\");\n  const [isRephrasing, setIsRephrasing] = useState(false);\n  const [activeSuggestion, setActiveSuggestion] = useState<CombinedSuggestion | null>(null);\n  const [fontFamily, setFontFamily] = useState(fontOptions[0].value);\n  \n  const isSingleWordSelection = useMemo(() => \n      debouncedSelection ? !debouncedSelection.text.trim().includes(' ') && debouncedSelection.text.trim().length > 0 : false\n  , [debouncedSelection]);\n\n\n  const placeholder = t('startWritingPlaceholder');\n  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(event.target.value);\n  };\n  \n  const handleScroll = (event: UIEvent<HTMLTextAreaElement>) => {\n    if (backdropRef.current) {\n        const target = event.currentTarget;\n        backdropRef.current.scrollTop = target.scrollTop;\n        backdropRef.current.scrollLeft = target.scrollLeft;\n\n        // Force synchronization to prevent drift\n        requestAnimationFrame(() => {\n          if (backdropRef.current && target) {\n            backdropRef.current.scrollTop = target.scrollTop;\n            backdropRef.current.scrollLeft = target.scrollLeft;\n          }\n        });\n    }\n  };\n\n  const handleSelect = () => {\n    if (textareaRef.current) {\n      const { selectionStart, selectionEnd, value: editorValue } = textareaRef.current;\n      const selectedText = editorValue.substring(selectionStart, selectionEnd);\n\n      if (selectionStart !== selectionEnd && selectedText.trim().length > 0) {\n        setSelectedTextDetail({ text: selectedText, start: selectionStart, end: selectionEnd });\n      } else {\n        setSelectedTextDetail(null);\n        setIsAiPopoverOpen(false);\n      }\n    }\n  };\n\n  const handleRephrase = useCallback(async () => {\n    if (!debouncedSelection || !debouncedSelection.text.trim() || isSingleWordSelection) {\n      return;\n    }\n    setIsRephrasing(true);\n    setRephrasedText(\"\");\n    try {\n      const input: RephraseTextInput = {\n        selectedText: debouncedSelection.text,\n        contextText: value,\n        tone: writingMode,\n        style: writingMode,\n      };\n      const result = await rephraseText(input);\n      if (!result || typeof result.rephrasedText === 'undefined') {\n        throw new Error('AI model did not return the expected output format.');\n      }\n      setRephrasedText(result.rephrasedText);\n      toast({ titleKey: \"toastSuggestionReady\", descriptionKey: \"toastTextGeneratedSuccess\" });\n    } catch (error) {\n      console.error(\"Error rephrasing text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastRephraseError\", variant: \"destructive\" });\n      setIsAiPopoverOpen(false);\n    } finally {\n      setIsRephrasing(false);\n    }\n  }, [debouncedSelection, value, writingMode, toast, isSingleWordSelection]);\n  \n  useEffect(() => {\n    if (isAiPopoverOpen && debouncedSelection && !isSingleWordSelection && !rephrasedText && !isRephrasing) {\n        handleRephrase();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isAiPopoverOpen, debouncedSelection, rephrasedText, isRephrasing, handleRephrase, isSingleWordSelection]);\n\n\n  const applyRephrasedText = () => {\n    if (debouncedSelection && rephrasedText) {\n      const newValue =\n        value.substring(0, debouncedSelection.start) +\n        rephrasedText +\n        value.substring(debouncedSelection.end);\n      onChange(newValue); \n      setIsAiPopoverOpen(false);\n      setSelectedTextDetail(null);\n      setRephrasedText(\"\");\n      if (textareaRef.current) {\n        textareaRef.current.focus();\n        const newCursorPosition = debouncedSelection.start + rephrasedText.length;\n        setTimeout(() => {\n          if (textareaRef.current) {\n            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);\n          }\n        }, 0);\n      }\n    }\n  };\n  \n  const handleApplyInlineSuggestion = (suggestion: AnalysisSuggestion) => {\n    onApplySuggestion(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);\n  };\n  \n  const handleApplyWordReplacement = (replacement: string) => {\n    if (debouncedSelection) {\n      const newValue =\n        value.substring(0, debouncedSelection.start) +\n        replacement +\n        value.substring(debouncedSelection.end);\n      onChange(newValue);\n      \n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastWordReplacedSuccess\", descriptionParams: { word: replacement } });\n\n      setIsAiPopoverOpen(false);\n      setSelectedTextDetail(null);\n      \n      if (textareaRef.current) {\n        textareaRef.current.focus();\n        const newCursorPosition = debouncedSelection.start + replacement.length;\n        setTimeout(() => {\n          if(textareaRef.current) {\n            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);\n          }\n        }, 0);\n      }\n    }\n  };\n\n  const handleFormat = (formatType: 'bold' | 'italic' | 'underline' | 'ordered-list' | 'unordered-list') => {\n    const textarea = textareaRef.current;\n    if (!textarea) return;\n\n    let { selectionStart, selectionEnd } = textarea;\n\n    // For lists, find the start and end of the line(s)\n    if (formatType === 'ordered-list' || formatType === 'unordered-list') {\n      let lineStartIndex = value.lastIndexOf('\\n', selectionStart - 1) + 1;\n      \n      // If no text is selected, operate on the current line\n      if (selectionStart === selectionEnd) {\n          selectionEnd = value.indexOf('\\n', selectionStart);\n          if (selectionEnd === -1) {\n              selectionEnd = value.length;\n          }\n      }\n\n      let lineEndIndex = value.indexOf('\\n', selectionEnd - 1);\n      if (lineEndIndex === -1 || lineEndIndex < selectionStart) {\n        lineEndIndex = value.length;\n      }\n      \n      const textToFormat = value.substring(lineStartIndex, lineEndIndex);\n      const lines = textToFormat.split('\\n');\n      let formattedLines;\n      let isList = false;\n\n      if (formatType === 'ordered-list') {\n          isList = lines.every(line => /^\\s*\\d+\\.\\s/.test(line.trim()) || line.trim() === '');\n          if (isList) {\n              formattedLines = lines.map(line => line.replace(/^\\s*\\d+\\.\\s*/, ''));\n          } else {\n              let counter = 1;\n              formattedLines = lines.map(line => {\n                  const trimmedLine = line.trim().replace(/^•\\s/, '');\n                  return trimmedLine ? `${counter++}. ${trimmedLine}` : '';\n              });\n          }\n      } else { // unordered-list\n          isList = lines.every(line => /^\\s*•\\s/.test(line.trim()) || line.trim() === '');\n          if (isList) {\n              formattedLines = lines.map(line => line.replace(/^\\s*•\\s*/, ''));\n          } else {\n              formattedLines = lines.map(line => {\n                  const trimmedLine = line.trim().replace(/^\\d+\\.\\s/, '');\n                  return trimmedLine ? `• ${trimmedLine}` : '';\n              });\n          }\n      }\n\n      const formattedBlock = formattedLines.join('\\n');\n      const newValue = value.substring(0, lineStartIndex) + formattedBlock + value.substring(lineEndIndex);\n      onChange(newValue);\n\n      setTimeout(() => {\n          textarea.focus();\n          textarea.setSelectionRange(lineStartIndex, lineStartIndex + formattedBlock.length);\n      }, 0);\n\n    } else { // Bold, Italic, Underline\n      const selectedText = value.substring(selectionStart, selectionEnd);\n      if (!selectedText) return;\n      \n      let formattedText;\n      switch (formatType) {\n        case 'bold': formattedText = `**${selectedText}**`; break;\n        case 'italic': formattedText = `*${selectedText}*`; break;\n        case 'underline': formattedText = `<u>${selectedText}</u>`; break;\n        default: return;\n      }\n\n      const newValue = value.substring(0, selectionStart) + formattedText + value.substring(selectionEnd);\n      onChange(newValue);\n\n      setTimeout(() => {\n          textarea.focus();\n          const newCursorPos = selectionStart + formattedText.length;\n          textarea.setSelectionRange(newCursorPos, newCursorPos);\n      }, 0);\n    }\n  };\n  \n  const renderFormattedText = (text: string): (string | JSX.Element)[] => {\n    if (!text) return [];\n    \n    let parts: (string | JSX.Element)[] = [text];\n\n    const processRegex = (regex: RegExp, tag: 'strong' | 'em' | 'u', keyPrefix: string) => {\n        parts = parts.flatMap((part, partIndex) => {\n            if (typeof part !== 'string') return part;\n\n            const splitParts = part.split(regex);\n            const processed: (string | JSX.Element)[] = [];\n\n            for (let i = 0; i < splitParts.length; i++) {\n                if (i % 2 === 0) {\n                    if(splitParts[i]) processed.push(splitParts[i]);\n                } else {\n                    const Tag = tag;\n                    processed.push(<Tag key={`${keyPrefix}-${partIndex}-${i}`}>{splitParts[i]}</Tag>);\n                }\n            }\n            return processed;\n        });\n    };\n    \n    processRegex(/\\*\\*([\\s\\S]*?)\\*\\*/g, 'strong', 'bold');\n    processRegex(/\\*([\\s\\S]*?)\\*/g, 'em', 'italic');\n    processRegex(/<u>([\\s\\S]*?)<\\/u>/g, 'u', 'underline');\n\n    return parts;\n};\n\n  const combinedSuggestions = useMemo(() => {\n    const allSuggestions = [...suggestions, ...plagiarismSources] as CombinedSuggestion[];\n    return allSuggestions.sort((a, b) => (a.startIndex || 0) - (b.startIndex || 0));\n  }, [suggestions, plagiarismSources]);\n  \n  const handleHighlightClick = (suggestion: CombinedSuggestion) => {\n      setActiveSuggestion(suggestion);\n  };\n  const closeSuggestionPopover = () => {\n      setActiveSuggestion(null);\n  }\n\n  const highlightedContent = useMemo(() => {\n    if (!value) return <p>&nbsp;</p>; // Render a non-breaking space to maintain height\n    \n    const sortedSuggestions = combinedSuggestions;\n\n    let lastIndex = 0;\n    const elements = [];\n\n    sortedSuggestions.forEach((suggestion) => {\n        const { startIndex, endIndex, originalSegment, id, type } = suggestion;\n\n        if (startIndex === undefined || endIndex === undefined || startIndex < lastIndex) {\n            return; \n        }\n\n        // Add the text before the current suggestion\n        if (startIndex > lastIndex) {\n            elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex, startIndex))}</React.Fragment>);\n        }\n\n        const highlightClass = cn('suggestion-highlight', {\n            'suggestion-spelling': type === 'spelling',\n            'suggestion-grammar': type === 'grammar',\n            'suggestion-rewrite': type === 'rewrite',\n            'suggestion-style': type === 'style',\n            'suggestion-plagiarism': type === 'plagiarism'\n        });\n\n        elements.push(\n            <Popover key={id} open={activeSuggestion?.id === id} onOpenChange={(open) => {if (!open) closeSuggestionPopover()}}>\n                <PopoverTrigger asChild>\n                    <span className={highlightClass} onClick={() => handleHighlightClick(suggestion)}>\n                        {renderFormattedText(originalSegment)}\n                    </span>\n                </PopoverTrigger>\n                {activeSuggestion?.id === id && (\n                    type === 'plagiarism' ? (\n                        <PlagiarismPopover \n                            source={suggestion as PlagiarismSource}\n                            onClose={closeSuggestionPopover}\n                        />\n                    ) : (\n                        <SuggestionPopover \n                            suggestion={suggestion as AnalysisSuggestion}\n                            onApply={handleApplyInlineSuggestion} \n                            onDismiss={onDismissSuggestion}\n                            onClose={closeSuggestionPopover}\n                        />\n                    )\n                )}\n            </Popover>\n        );\n\n        lastIndex = endIndex;\n    });\n\n    // Add any remaining text after the last suggestion\n    if (lastIndex < value.length) {\n        elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex))}</React.Fragment>);\n    }\n\n    // Split the entire content by newlines to create <p> tags for each line\n    const finalElements = [];\n    let lineBuffer: (string|JSX.Element)[] = [];\n\n    elements.forEach((el) => {\n        if (typeof el.props.children === 'string') {\n            const text = el.props.children;\n            const lines = text.split('\\n');\n            lines.forEach((line: string, lineIndex: number) => {\n                if(line) lineBuffer.push(line);\n                if (lineIndex < lines.length - 1) {\n                    finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer.length > 0 ? lineBuffer : <>&nbsp;</>}</p>);\n                    lineBuffer = [];\n                }\n            });\n        } else {\n            lineBuffer.push(el);\n        }\n    });\n\n    if (lineBuffer.length > 0) {\n        finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer}</p>);\n    }\n    \n    if (finalElements.length === 0 && value === \"\") {\n      return <p>&nbsp;</p>;\n    }\n\n    return <>{finalElements}</>;\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [value, combinedSuggestions, onDismissSuggestion, activeSuggestion]);\n\n  const handleClearText = () => {\n    onChange(\"\");\n    toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastEditorClearedSuccess\" });\n  };\n\n  const handleCopyText = async () => {\n    if (!value) {\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastEditorEmptyForCopyError\", variant: \"destructive\" });\n      return;\n    }\n    try {\n      await navigator.clipboard.writeText(value);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastEditorContentCopiedSuccess\" });\n    } catch (error) {\n      console.error(\"Failed to copy text from editor:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastEditorContentCopyError\", variant: \"destructive\" });\n    }\n  };\n  \n  const editorStyles: React.CSSProperties = {\n    fontFamily: direction === 'rtl' ? 'Segoe UI, Tahoma, Arial, Helvetica Neue, sans-serif' : fontFamily,\n    fontSize: 'inherit',\n    lineHeight: 'inherit',\n    letterSpacing: 'inherit',\n    padding: '1rem',\n    boxSizing: 'border-box',\n    width: '100%',\n    height: '100%',\n    resize: 'none',\n    border: 'none',\n    outline: 'none',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    wordBreak: 'break-word',\n    overflowWrap: 'break-word',\n    textAlign: direction === 'rtl' ? 'right' : 'left',\n    direction: direction,\n  };\n\n  return (\n    <Card className=\"h-full flex flex-col\" ref={ref}>\n      <CardHeader className=\"p-4 border-b flex flex-row items-center justify-between gap-4 flex-wrap\">\n        <CardTitle className=\"flex items-center text-lg shrink-0\">\n          <FileText className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('editorTitle')}\n        </CardTitle>\n        <div className=\"flex items-center flex-nowrap gap-x-2 justify-end grow\">\n            <div className=\"flex items-center gap-1\">\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={onUndo} disabled={!canUndo} title={t('undoButton')}>\n                    <Undo2 className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={onRedo} disabled={!canRedo} title={t('redoButton')}>\n                    <Redo2 className=\"h-4 w-4\" />\n                </Button>\n            </div>\n\n            <Separator orientation=\"vertical\" className=\"h-6\" />\n\n            <Select value={fontFamily} onValueChange={setFontFamily}>\n                <SelectTrigger className=\"w-[140px] h-8\">\n                    <SelectValue placeholder=\"Select font\" />\n                </SelectTrigger>\n                <SelectContent>\n                    {fontOptions.map(font => (\n                        <SelectItem key={font.value} value={font.value} style={{fontFamily: font.value}}>\n                            {font.label}\n                        </SelectItem>\n                    ))}\n                </SelectContent>\n            </Select>\n\n            <Separator orientation=\"vertical\" className=\"h-6\" />\n\n            <div className=\"flex items-center gap-0.5\">\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('bold')} title=\"Bold\">\n                    <Bold className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('italic')} title=\"Italic\">\n                    <Italic className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('underline')} title=\"Underline\">\n                    <Underline className=\"h-4 w-4\" />\n                </Button>\n                 <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('ordered-list')} title=\"Numbered List\">\n                    <ListOrdered className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('unordered-list')} title=\"Bulleted List\">\n                    <List className=\"h-4 w-4\" />\n                </Button>\n            </div>\n            \n            <div className=\"flex items-center gap-0.5\">\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Left\">\n                  <AlignLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Center\">\n                  <AlignCenter className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Right\">\n                  <AlignRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"flex-grow p-0 relative\">\n          <div className=\"relative h-full\">\n            <div\n              ref={backdropRef}\n              className=\"editor-backdrop absolute inset-0 z-[1] overflow-auto pointer-events-none\"\n              style={{\n                ...editorStyles,\n                margin: 0,\n                border: 0,\n                background: 'transparent'\n              }}\n              dir={direction}\n            >\n              {highlightedContent}\n            </div>\n            <Textarea\n              ref={textareaRef}\n              value={value}\n              onChange={handleChange}\n              onSelect={handleSelect}\n              onScroll={handleScroll}\n              placeholder={placeholder}\n              className=\"editor-textarea absolute inset-0 z-0 rounded-none focus-visible:ring-0 focus-visible:ring-offset-0 min-h-[400px] max-h-[80vh] bg-transparent resize-none\"\n              style={{\n                ...editorStyles,\n                color: 'transparent',\n                caretColor: 'hsl(var(--foreground))',\n                margin: 0,\n                border: 0,\n                background: 'transparent'\n              }}\n              aria-label={t('editorTitle')}\n              dir={direction}\n            />\n          </div>\n      </CardContent>\n      <CardFooter className=\"p-4 border-t flex items-center justify-start gap-2 flex-wrap\">\n        <Popover open={isAiPopoverOpen} onOpenChange={setIsAiPopoverOpen}>\n          <PopoverTrigger asChild>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              disabled={!debouncedSelection || isRephrasing}\n            >\n              <Wand2 className=\"mr-2 h-4 w-4\" />\n              {t('aiToolsButton')}\n            </Button>\n          </PopoverTrigger>\n          {debouncedSelection && (\n            isSingleWordSelection ? (\n                <WordToolkitPopover \n                    selectedWord={debouncedSelection.text}\n                    contextText={value}\n                    language={getWritingLanguageBase()}\n                    onSynonymSelect={handleApplyWordReplacement}\n                />\n            ) : (\n                <PopoverContent className=\"w-80\" side=\"top\" align=\"start\">\n                <div className=\"grid gap-4\">\n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-medium leading-none\">{t('rephrasePopoverTitle')}</h4>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {t('rephrasePopoverDescription')}\n                    </p>\n                  </div>\n                  \n                    <div className=\"space-y-1\">\n                      <Label htmlFor=\"original-text-popover\">{t('originalTextLabel')}</Label>\n                      <Textarea\n                        id=\"original-text-popover\"\n                        value={debouncedSelection.text}\n                        readOnly\n                        className=\"h-20 text-xs bg-muted/50\"\n                      />\n                    </div>\n                  \n                  {isRephrasing && (\n                    <div className=\"flex items-center justify-center h-20\">\n                      <Loader2 className=\"h-5 w-5 animate-spin text-primary\" />\n                    </div>\n                  )}\n                  {!isRephrasing && rephrasedText && (\n                     <div className=\"space-y-1\">\n                      <Label htmlFor=\"rephrased-text-popover\">{t('suggestionTextLabel')}</Label>\n                      <Textarea\n                        id=\"rephrased-text-popover\"\n                        value={rephrasedText}\n                        readOnly\n                        className=\"h-20 text-xs bg-muted\"\n                      />\n                    </div>\n                  )}\n                  <div className=\"flex justify-end gap-2\">\n                    <Button variant=\"ghost\" size=\"sm\" onClick={() => setIsAiPopoverOpen(false)}>{t('cancelButton')}</Button>\n                    <Button\n                      size=\"sm\"\n                      onClick={applyRephrasedText}\n                      disabled={isRephrasing || !rephrasedText}\n                    >\n                      {t('applyButton')}\n                    </Button>\n                  </div>\n                </div>\n              </PopoverContent>\n            )\n          )}\n        </Popover>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleClearText}\n          disabled={value.length === 0}\n          aria-label={t('clearEditorButtonAriaLabel')}\n          title={t('clearEditorButtonAriaLabel')}\n        >\n          <Eraser className=\"mr-2 h-4 w-4\" />\n          {t('clearEditorButton')}\n        </Button>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleCopyText}\n          disabled={value.length === 0}\n          aria-label={t('copyEditorButtonAriaLabel')}\n          title={t('copyEditorButtonAriaLabel')}\n        >\n          <Copy className=\"mr-2 h-4 w-4\" />\n          {t('copyEditorButton')}\n        </Button>\n      </CardFooter>\n    </Card>\n  );\n});\n\nEnhancedTextEditor.displayName = \"EnhancedTextEditor\";\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;AAyDA,MAAM,cAAc;IAChB;QAAE,OAAO;QAAuB,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAAgC,OAAO;IAAkB;IAClE;QAAE,OAAO;QAAoB,OAAO;IAAU;IAC9C;QAAE,OAAO;QAAmC,OAAO;IAAkB;CACxE;AAGD,0CAA0C;AAC1C,SAAS,kBAAkB,EACvB,UAAU,EACV,OAAO,EACP,SAAS,EACT,OAAO,EACc;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,cAAc;QAChB,QAAQ;QACR;IACJ;IAEA,MAAM,gBAAgB;QAClB,UAAU,WAAW,EAAE;QACvB;IACJ;IAEA,qBACI,8OAAC,mIAAA,CAAA,iBAAc;QAAC,WAAU;QAAgC,MAAK;QAAM,OAAM;QAAQ,iBAAiB;kBAChG,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAE,WAAU;8BAAiC,WAAW,OAAO;;;;;;8BAChE,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAE,WAAU;sCAAyC,WAAW,eAAe;;;;;;sCAChF,8OAAC;4BAAE,WAAU;sCAAsC,WAAW,UAAU;;;;;;;;;;;;8BAE5E,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAQ;4BAAQ,SAAS;sCACtC,EAAE;;;;;;sCAEP,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAS;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;AAEA,SAAS,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAA0B;IAClE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,qBACI,8OAAC,mIAAA,CAAA,iBAAc;QAAC,WAAU;QAAgC,MAAK;QAAM,OAAM;QAAQ,iBAAiB;kBAChG,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB,EAAE;;;;;;;8BAEP,8OAAC;oBAAE,WAAU;8BAAiC,OAAO,OAAO;;;;;;8BAC5D,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAE,WAAU;;0CACT,8OAAC;gCAAK,WAAU;;oCAAiB,EAAE;oCAAuB;;;;;;;4BAAS;0CACnE,8OAAC;gCAAE,MAAM,OAAO,UAAU;gCAAE,QAAO;gCAAS,KAAI;gCAAsB,WAAU;0CAC3E,OAAO,UAAU;;;;;;;;;;;;;;;;;8BAI9B,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAQ,SAAS;kCACtC,EAAE;;;;;;;;;;;;;;;;;;;;;;AAM3B;AAGO,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;IACxJ,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACrF,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB;IAE3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK;IAEjE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAClC,qBAAqB,CAAC,mBAAmB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,QAAQ,mBAAmB,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI,OACpH;QAAC;KAAmB;IAGtB,MAAM,cAAc,EAAE;IACtB,MAAM,eAAe,CAAC;QACpB,SAAS,MAAM,MAAM,CAAC,KAAK;IAC7B;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,YAAY,OAAO,EAAE;YACrB,MAAM,SAAS,MAAM,aAAa;YAClC,YAAY,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;YAChD,YAAY,OAAO,CAAC,UAAU,GAAG,OAAO,UAAU;YAElD,yCAAyC;YACzC,sBAAsB;gBACpB,IAAI,YAAY,OAAO,IAAI,QAAQ;oBACjC,YAAY,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;oBAChD,YAAY,OAAO,CAAC,UAAU,GAAG,OAAO,UAAU;gBACpD;YACF;QACJ;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,YAAY,OAAO;YAChF,MAAM,eAAe,YAAY,SAAS,CAAC,gBAAgB;YAE3D,IAAI,mBAAmB,gBAAgB,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;gBACrE,sBAAsB;oBAAE,MAAM;oBAAc,OAAO;oBAAgB,KAAK;gBAAa;YACvF,OAAO;gBACL,sBAAsB;gBACtB,mBAAmB;YACrB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,IAAI,CAAC,IAAI,MAAM,uBAAuB;YACnF;QACF;QACA,gBAAgB;QAChB,iBAAiB;QACjB,IAAI;YACF,MAAM,QAA2B;gBAC/B,cAAc,mBAAmB,IAAI;gBACrC,aAAa;gBACb,MAAM;gBACN,OAAO;YACT;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE;YAClC,IAAI,CAAC,UAAU,OAAO,OAAO,aAAa,KAAK,aAAa;gBAC1D,MAAM,IAAI,MAAM;YAClB;YACA,iBAAiB,OAAO,aAAa;YACrC,MAAM;gBAAE,UAAU;gBAAwB,gBAAgB;YAA4B;QACxF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAsB,SAAS;YAAc;YAClG,mBAAmB;QACrB,SAAU;YACR,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAoB;QAAO;QAAa;QAAO;KAAsB;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,sBAAsB,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,cAAc;YACpG;QACJ;IACF,uDAAuD;IACvD,GAAG;QAAC;QAAiB;QAAoB;QAAe;QAAc;QAAgB;KAAsB;IAG5G,MAAM,qBAAqB;QACzB,IAAI,sBAAsB,eAAe;YACvC,MAAM,WACJ,MAAM,SAAS,CAAC,GAAG,mBAAmB,KAAK,IAC3C,gBACA,MAAM,SAAS,CAAC,mBAAmB,GAAG;YACxC,SAAS;YACT,mBAAmB;YACnB,sBAAsB;YACtB,iBAAiB;YACjB,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,MAAM,oBAAoB,mBAAmB,KAAK,GAAG,cAAc,MAAM;gBACzE,WAAW;oBACT,IAAI,YAAY,OAAO,EAAE;wBACvB,YAAY,OAAO,CAAC,iBAAiB,CAAC,mBAAmB;oBAC3D;gBACF,GAAG;YACL;QACF;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,kBAAkB,WAAW,UAAU,EAAE,WAAW,eAAe,EAAE,WAAW,UAAU,EAAE,WAAW,QAAQ;IACjH;IAEA,MAAM,6BAA6B,CAAC;QAClC,IAAI,oBAAoB;YACtB,MAAM,WACJ,MAAM,SAAS,CAAC,GAAG,mBAAmB,KAAK,IAC3C,cACA,MAAM,SAAS,CAAC,mBAAmB,GAAG;YACxC,SAAS;YAET,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;gBAA4B,mBAAmB;oBAAE,MAAM;gBAAY;YAAE;YAE5H,mBAAmB;YACnB,sBAAsB;YAEtB,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,MAAM,oBAAoB,mBAAmB,KAAK,GAAG,YAAY,MAAM;gBACvE,WAAW;oBACT,IAAG,YAAY,OAAO,EAAE;wBACtB,YAAY,OAAO,CAAC,iBAAiB,CAAC,mBAAmB;oBAC3D;gBACF,GAAG;YACL;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,YAAY,OAAO;QACpC,IAAI,CAAC,UAAU;QAEf,IAAI,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG;QAEvC,mDAAmD;QACnD,IAAI,eAAe,kBAAkB,eAAe,kBAAkB;YACpE,IAAI,iBAAiB,MAAM,WAAW,CAAC,MAAM,iBAAiB,KAAK;YAEnE,sDAAsD;YACtD,IAAI,mBAAmB,cAAc;gBACjC,eAAe,MAAM,OAAO,CAAC,MAAM;gBACnC,IAAI,iBAAiB,CAAC,GAAG;oBACrB,eAAe,MAAM,MAAM;gBAC/B;YACJ;YAEA,IAAI,eAAe,MAAM,OAAO,CAAC,MAAM,eAAe;YACtD,IAAI,iBAAiB,CAAC,KAAK,eAAe,gBAAgB;gBACxD,eAAe,MAAM,MAAM;YAC7B;YAEA,MAAM,eAAe,MAAM,SAAS,CAAC,gBAAgB;YACrD,MAAM,QAAQ,aAAa,KAAK,CAAC;YACjC,IAAI;YACJ,IAAI,SAAS;YAEb,IAAI,eAAe,gBAAgB;gBAC/B,SAAS,MAAM,KAAK,CAAC,CAAA,OAAQ,cAAc,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;gBAChF,IAAI,QAAQ;oBACR,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,gBAAgB;gBACpE,OAAO;oBACH,IAAI,UAAU;oBACd,iBAAiB,MAAM,GAAG,CAAC,CAAA;wBACvB,MAAM,cAAc,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ;wBAChD,OAAO,cAAc,GAAG,UAAU,EAAE,EAAE,aAAa,GAAG;oBAC1D;gBACJ;YACJ,OAAO;gBACH,SAAS,MAAM,KAAK,CAAC,CAAA,OAAQ,UAAU,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;gBAC5E,IAAI,QAAQ;oBACR,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,YAAY;gBAChE,OAAO;oBACH,iBAAiB,MAAM,GAAG,CAAC,CAAA;wBACvB,MAAM,cAAc,KAAK,IAAI,GAAG,OAAO,CAAC,YAAY;wBACpD,OAAO,cAAc,CAAC,EAAE,EAAE,aAAa,GAAG;oBAC9C;gBACJ;YACJ;YAEA,MAAM,iBAAiB,eAAe,IAAI,CAAC;YAC3C,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,kBAAkB,iBAAiB,MAAM,SAAS,CAAC;YACvF,SAAS;YAET,WAAW;gBACP,SAAS,KAAK;gBACd,SAAS,iBAAiB,CAAC,gBAAgB,iBAAiB,eAAe,MAAM;YACrF,GAAG;QAEL,OAAO;YACL,MAAM,eAAe,MAAM,SAAS,CAAC,gBAAgB;YACrD,IAAI,CAAC,cAAc;YAEnB,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAQ,gBAAgB,CAAC,EAAE,EAAE,aAAa,EAAE,CAAC;oBAAE;gBACpD,KAAK;oBAAU,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;oBAAE;gBACpD,KAAK;oBAAa,gBAAgB,CAAC,GAAG,EAAE,aAAa,IAAI,CAAC;oBAAE;gBAC5D;oBAAS;YACX;YAEA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,kBAAkB,gBAAgB,MAAM,SAAS,CAAC;YACtF,SAAS;YAET,WAAW;gBACP,SAAS,KAAK;gBACd,MAAM,eAAe,iBAAiB,cAAc,MAAM;gBAC1D,SAAS,iBAAiB,CAAC,cAAc;YAC7C,GAAG;QACL;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,QAAkC;YAAC;SAAK;QAE5C,MAAM,eAAe,CAAC,OAAe,KAA4B;YAC7D,QAAQ,MAAM,OAAO,CAAC,CAAC,MAAM;gBACzB,IAAI,OAAO,SAAS,UAAU,OAAO;gBAErC,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,YAAsC,EAAE;gBAE9C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBACxC,IAAI,IAAI,MAAM,GAAG;wBACb,IAAG,UAAU,CAAC,EAAE,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC,EAAE;oBAClD,OAAO;wBACH,MAAM,MAAM;wBACZ,UAAU,IAAI,eAAC,8OAAC;sCAA4C,UAAU,CAAC,EAAE;2BAAhD,GAAG,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG;;;;;oBAC7D;gBACJ;gBACA,OAAO;YACX;QACJ;QAEA,aAAa,uBAAuB,UAAU;QAC9C,aAAa,mBAAmB,MAAM;QACtC,aAAa,uBAAuB,KAAK;QAEzC,OAAO;IACX;IAEE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAClC,MAAM,iBAAiB;eAAI;eAAgB;SAAkB;QAC7D,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;IAC/E,GAAG;QAAC;QAAa;KAAkB;IAEnC,MAAM,uBAAuB,CAAC;QAC1B,oBAAoB;IACxB;IACA,MAAM,yBAAyB;QAC3B,oBAAoB;IACxB;IAEA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,IAAI,CAAC,OAAO,qBAAO,8OAAC;sBAAE;;;;;kBAAY,iDAAiD;QAEnF,MAAM,oBAAoB;QAE1B,IAAI,YAAY;QAChB,MAAM,WAAW,EAAE;QAEnB,kBAAkB,OAAO,CAAC,CAAC;YACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG;YAE5D,IAAI,eAAe,aAAa,aAAa,aAAa,aAAa,WAAW;gBAC9E;YACJ;YAEA,6CAA6C;YAC7C,IAAI,aAAa,WAAW;gBACxB,SAAS,IAAI,eAAC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;8BAA4B,oBAAoB,MAAM,KAAK,CAAC,WAAW;mBAAjE,CAAC,KAAK,EAAE,WAAW;;;;;YAC1D;YAEA,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;gBAC9C,uBAAuB,SAAS;gBAChC,sBAAsB,SAAS;gBAC/B,sBAAsB,SAAS;gBAC/B,oBAAoB,SAAS;gBAC7B,yBAAyB,SAAS;YACtC;YAEA,SAAS,IAAI,eACT,8OAAC,mIAAA,CAAA,UAAO;gBAAU,MAAM,kBAAkB,OAAO;gBAAI,cAAc,CAAC;oBAAU,IAAI,CAAC,MAAM;gBAAwB;;kCAC7G,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACnB,cAAA,8OAAC;4BAAK,WAAW;4BAAgB,SAAS,IAAM,qBAAqB;sCAChE,oBAAoB;;;;;;;;;;;oBAG5B,kBAAkB,OAAO,MAAM,CAC5B,SAAS,6BACL,8OAAC;wBACG,QAAQ;wBACR,SAAS;;;;;6CAGb,8OAAC;wBACG,YAAY;wBACZ,SAAS;wBACT,WAAW;wBACX,SAAS;;;;;4BAGrB;;eApBU;;;;;YAwBlB,YAAY;QAChB;QAEA,mDAAmD;QACnD,IAAI,YAAY,MAAM,MAAM,EAAE;YAC1B,SAAS,IAAI,eAAC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;0BAA4B,oBAAoB,MAAM,KAAK,CAAC;eAAtD,CAAC,KAAK,EAAE,WAAW;;;;;QAC1D;QAEA,wEAAwE;QACxE,MAAM,gBAAgB,EAAE;QACxB,IAAI,aAAqC,EAAE;QAE3C,SAAS,OAAO,CAAC,CAAC;YACd,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,KAAK,UAAU;gBACvC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ;gBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,MAAM,OAAO,CAAC,CAAC,MAAc;oBACzB,IAAG,MAAM,WAAW,IAAI,CAAC;oBACzB,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;wBAC9B,cAAc,IAAI,eAAC,8OAAC;sCAAwC,WAAW,MAAM,GAAG,IAAI,2BAAa;0CAAE;;2BAAxE,CAAC,KAAK,EAAE,cAAc,MAAM,EAAE;;;;;wBACzD,aAAa,EAAE;oBACnB;gBACJ;YACJ,OAAO;gBACH,WAAW,IAAI,CAAC;YACpB;QACJ;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACvB,cAAc,IAAI,eAAC,8OAAC;0BAAwC;eAAjC,CAAC,KAAK,EAAE,cAAc,MAAM,EAAE;;;;;QAC7D;QAEA,IAAI,cAAc,MAAM,KAAK,KAAK,UAAU,IAAI;YAC9C,qBAAO,8OAAC;0BAAE;;;;;;QACZ;QAEA,qBAAO;sBAAG;;IACZ,uDAAuD;IACvD,GAAG;QAAC;QAAO;QAAqB;QAAqB;KAAiB;IAEtE,MAAM,kBAAkB;QACtB,SAAS;QACT,MAAM;YAAE,UAAU;YAAqB,gBAAgB;QAA4B;IACrF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO;YACV,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAgC,SAAS;YAAc;YAC5G;QACF;QACA,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAkC;QAC3F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA+B,SAAS;YAAc;QAC7G;IACF;IAEA,MAAM,eAAoC;QACxC,YAAY,cAAc,QAAQ,wDAAwD;QAC1F,UAAU;QACV,YAAY;QACZ,eAAe;QACf,SAAS;QACT,WAAW;QACX,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW,cAAc,QAAQ,UAAU;QAC3C,WAAW;IACb;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;QAAuB,KAAK;;0BAC1C,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEL,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS;wCAAQ,UAAU,CAAC;wCAAS,OAAO,EAAE;kDACpG,cAAA,8OAAC,wMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS;wCAAQ,UAAU,CAAC;wCAAS,OAAO,EAAE;kDACpG,cAAA,8OAAC,wMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAE5C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe;;kDACtC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACrB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE7B,8OAAC,kIAAA,CAAA,gBAAa;kDACT,YAAY,GAAG,CAAC,CAAA,qBACb,8OAAC,kIAAA,CAAA,aAAU;gDAAkB,OAAO,KAAK,KAAK;gDAAE,OAAO;oDAAC,YAAY,KAAK,KAAK;gDAAA;0DACzE,KAAK,KAAK;+CADE,KAAK,KAAK;;;;;;;;;;;;;;;;0CAOvC,8OAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAE5C,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAS,OAAM;kDACjG,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAW,OAAM;kDACnG,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAc,OAAM;kDACtG,cAAA,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAiB,OAAM;kDAC1G,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAmB,OAAM;kDAC3G,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACnB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCACL,GAAG,YAAY;gCACf,QAAQ;gCACR,QAAQ;gCACR,YAAY;4BACd;4BACA,KAAK;sCAEJ;;;;;;sCAEH,8OAAC,oIAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO;4BACP,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,aAAa;4BACb,WAAU;4BACV,OAAO;gCACL,GAAG,YAAY;gCACf,OAAO;gCACP,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,YAAY;4BACd;4BACA,cAAY,EAAE;4BACd,KAAK;;;;;;;;;;;;;;;;;0BAIb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAM;wBAAiB,cAAc;;0CAC5C,8OAAC,mIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU,CAAC,sBAAsB;;sDAEjC,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,EAAE;;;;;;;;;;;;4BAGN,sBAAsB,CACrB,sCACI,8OAAC,0JAAA,CAAA,qBAAkB;gCACf,cAAc,mBAAmB,IAAI;gCACrC,aAAa;gCACb,UAAU;gCACV,iBAAiB;;;;;qDAGrB,8OAAC,mIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAO,MAAK;gCAAM,OAAM;0CAClD,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4B,EAAE;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DACV,EAAE;;;;;;;;;;;;sDAIL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB,EAAE;;;;;;8DAC1C,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,mBAAmB,IAAI;oDAC9B,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAIf,8BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;wCAGtB,CAAC,gBAAgB,+BACf,8OAAC;4CAAI,WAAU;;8DACd,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA0B,EAAE;;;;;;8DAC3C,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS,IAAM,mBAAmB;8DAAS,EAAE;;;;;;8DAC/E,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,UAAU,gBAAgB,CAAC;8DAE1B,EAAE;;;;;;;;;;;;;;;;;;;;;;oCAMf;;;;;;;kCAGF,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,MAAM,MAAM,KAAK;wBAC3B,cAAY,EAAE;wBACd,OAAO,EAAE;;0CAET,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,EAAE;;;;;;;kCAGL,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,MAAM,MAAM,KAAK;wBAC3B,cAAY,EAAE;wBACd,OAAO,EAAE;;0CAET,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;;;;;;;;;;;;;AAKb;AAEA,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/suggestion-item.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport type { AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { Button } from '@/components/ui/button';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Lightbulb, PenTool, SpellCheck, Info, Check, X } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface SuggestionItemProps {\n  suggestion: AnalysisSuggestion;\n  onApply: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;\n  onDismiss: (suggestionId: string) => void;\n}\n\nconst getSuggestionTypeAppearance = (type: AnalysisSuggestion['type']): { color: string; icon: JSX.Element; labelKey: string } => {\n  switch (type) {\n    case 'spelling':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <SpellCheck className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeSpelling' };\n    case 'grammar':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeGrammar' };\n    case 'rewrite':\n      return { color: 'bg-blue-500 hover:bg-blue-600', icon: <Lightbulb className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeRewrite' };\n    case 'style':\n      return { color: 'bg-green-500 hover:bg-green-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeStyle' };\n    default:\n      return { color: 'bg-gray-500 hover:bg-gray-600', icon: <Info className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeUnknown' };\n  }\n};\n\n// This function renders a suggestion item with a given suggestion, onApply and onDismiss functions\nexport function SuggestionItem({ suggestion, onApply, onDismiss }: SuggestionItemProps) {\n  // Get the i18n instance\n  const { t } = useI18n();\n  // Get the appearance of the suggestion type\n  const { color, icon, labelKey } = getSuggestionTypeAppearance(suggestion.type);\n\n  // This function is called when the suggestion is applied\n  const handleApply = () => {\n    onApply(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);\n  };\n  \n  // This function is called when a suggestion is dismissed\n  const handleDismiss = () => {\n      // Call the onDismiss function passed as a prop, passing the suggestion id as an argument\n      onDismiss(suggestion.id);\n  };\n\n  return (\n    <div className=\"p-3 border-b last:border-b-0 bg-card hover:bg-muted/50 transition-colors\">\n      <div className=\"flex items-start justify-between gap-2\">\n        <div>\n          <div className=\"flex items-center mb-1\">\n            <Badge variant=\"default\" className={`text-xs text-white ${color} mr-2`}>\n              {icon}\n              {t(labelKey)}\n            </Badge>\n             <Popover>\n                <PopoverTrigger asChild>\n                  <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6 text-muted-foreground hover:text-foreground\">\n                    <Info className=\"h-3.5 w-3.5\" />\n                    <span className=\"sr-only\">{t('suggestionExplanationTooltip')}</span>\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-72 text-sm p-3\" side=\"top\" align=\"start\">\n                  {suggestion.message}\n                </PopoverContent>\n              </Popover>\n          </div>\n          <p className=\"text-sm text-muted-foreground mb-1\">\n            <span className=\"italic\">\"{suggestion.originalSegment}\"</span>\n          </p>\n          <p className=\"text-sm font-semibold text-primary\">\n            {t('suggestionLabel')}: <span className=\"font-normal text-foreground\">{suggestion.suggestion}</span>\n          </p>\n        </div>\n        <div className=\"flex items-center gap-1 mt-1 shrink-0\">\n            <Button onClick={handleApply} size=\"sm\" variant=\"outline\" title={t('correctButton')}>\n                <Check className=\"mr-1.5 h-3.5 w-3.5\" />\n                {t('correctButton')}\n            </Button>\n            <Button onClick={handleDismiss} size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" title={t('dismissButton')}>\n                <X className=\"h-4 w-4\" />\n            </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n    "], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;AAeA,MAAM,8BAA8B,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAyB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACrI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAmC,oBAAM,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAsB;QACvI;YACE,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;IACtI;AACF;AAGO,SAAS,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAuB;IACpF,wBAAwB;IACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,4CAA4C;IAC5C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,4BAA4B,WAAW,IAAI;IAE7E,yDAAyD;IACzD,MAAM,cAAc;QAClB,QAAQ,WAAW,UAAU,EAAE,WAAW,eAAe,EAAE,WAAW,UAAU,EAAE,WAAW,QAAQ;IACvG;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QAClB,yFAAyF;QACzF,UAAU,WAAW,EAAE;IAC3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAW,CAAC,mBAAmB,EAAE,MAAM,KAAK,CAAC;;wCACnE;wCACA,EAAE;;;;;;;8CAEJ,8OAAC,mIAAA,CAAA,UAAO;;sDACL,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;;kEAC5C,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAW,EAAE;;;;;;;;;;;;;;;;;sDAGjC,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAmB,MAAK;4CAAM,OAAM;sDAC3D,WAAW,OAAO;;;;;;;;;;;;;;;;;;sCAI3B,8OAAC;4BAAE,WAAU;sCACX,cAAA,8OAAC;gCAAK,WAAU;;oCAAS;oCAAE,WAAW,eAAe;oCAAC;;;;;;;;;;;;sCAExD,8OAAC;4BAAE,WAAU;;gCACV,EAAE;gCAAmB;8CAAE,8OAAC;oCAAK,WAAU;8CAA+B,WAAW,UAAU;;;;;;;;;;;;;;;;;;8BAGhG,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,MAAK;4BAAK,SAAQ;4BAAU,OAAO,EAAE;;8CAC/D,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,EAAE;;;;;;;sCAEP,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAe,MAAK;4BAAO,SAAQ;4BAAQ,WAAU;4BAAU,OAAO,EAAE;sCACrF,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2575, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/writing-suggestions-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from \"@/components/ui/card\";\nimport { Loader2, <PERSON><PERSON>ircle, AlertTriangle } from \"lucide-react\";\nimport { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { SuggestionItem } from './suggestion-item';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport React from 'react';\n\ninterface WritingSuggestionsPanelProps {\n  suggestions: AnalysisSuggestion[];\n  isAnalyzing: boolean;\n  onApplySuggestion: (\n    suggestionText: string,\n    originalSegment: string,\n    startIndex?: number,\n    endIndex?: number\n  ) => void;\n  onDismissSuggestion: (suggestionId: string) => void;\n}\n\nexport function WritingSuggestionsPanel({ \n  suggestions, \n  isAnalyzing, \n  onApplySuggestion, \n  onDismissSuggestion\n}: WritingSuggestionsPanelProps) {\n  const { t } = useI18n();\n\n  const hasSuggestions = suggestions.length > 0;\n  \n  const descriptionKey = isAnalyzing \n    ? 'analyzingTextDescription' \n    : hasSuggestions \n    ? 'suggestionsFoundDescription' \n    : 'startTypingForSuggestionsDescription';\n\n  return (\n    <Card className=\"h-full flex flex-col\">\n      <CardHeader className=\"p-3 border-b sticky top-0 bg-card z-10\">\n        <CardTitle className=\"text-base flex items-center\">\n            {isAnalyzing && <Loader2 className=\"h-3.5 w-3.5 animate-spin mr-2\" />}\n            {!isAnalyzing && hasSuggestions && <CheckCircle className=\"h-3.5 w-3.5 text-primary mr-2\" />}\n            {!isAnalyzing && !hasSuggestions && <AlertTriangle className=\"h-3.5 w-3.5 text-muted-foreground mr-2\" />}\n            {t('writingSuggestionsTitle')}\n        </CardTitle>\n        <CardDescription className=\"text-xs\">\n            {t(descriptionKey, { count: suggestions.length.toString() })}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"flex-grow p-0 relative\">\n        <div className=\"absolute inset-0\">\n          <ScrollArea className=\"h-full w-full\">\n            <div className=\"p-1\">\n              {hasSuggestions ? (\n                suggestions.map((suggestion) => (\n                  <SuggestionItem\n                    key={suggestion.id}\n                    suggestion={suggestion}\n                    onApply={onApplySuggestion}\n                    onDismiss={onDismissSuggestion}\n                  />\n                ))\n              ) : !isAnalyzing ? (\n                <div className=\"p-4 text-sm text-muted-foreground text-center\">{t('startTypingForSuggestionsDescription')}</div>\n              ) : null}\n            </div>\n          </ScrollArea>\n        </div>\n      </CardContent>\n\n      {hasSuggestions && (\n        <CardFooter className=\"p-2 border-t text-xs text-muted-foreground justify-center sticky bottom-0 bg-card z-10\">\n          {t('suggestionsFoundDescription', { count: suggestions.length.toString() })}\n        </CardFooter>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;;AAsBO,SAAS,wBAAwB,EACtC,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACU;IAC7B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,iBAAiB,YAAY,MAAM,GAAG;IAE5C,MAAM,iBAAiB,cACnB,6BACA,iBACA,gCACA;IAEJ,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;4BAChB,6BAAe,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClC,CAAC,eAAe,gCAAkB,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACzD,CAAC,eAAe,CAAC,gCAAkB,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAC5D,EAAE;;;;;;;kCAEP,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCACtB,EAAE,gBAAgB;4BAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;wBAAG;;;;;;;;;;;;0BAIhE,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,iBACC,YAAY,GAAG,CAAC,CAAC,2BACf,8OAAC,kJAAA,CAAA,iBAAc;oCAEb,YAAY;oCACZ,SAAS;oCACT,WAAW;mCAHN,WAAW,EAAE;;;;4CAMpB,CAAC,4BACH,8OAAC;gCAAI,WAAU;0CAAiD,EAAE;;;;;uCAChE;;;;;;;;;;;;;;;;;;;;;YAMX,gCACC,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,EAAE,+BAA+B;oBAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;gBAAG;;;;;;;;;;;;AAKnF", "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/stats/writing-statistics.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from \"@/components/ui/card\";\nimport { Activity, ListOrdered, BarChartHorizontalBig } from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\ninterface WritingStatisticsProps {\n  wordCount: number;\n  charCount: number;\n  writingScore: number;\n}\n\nexport function WritingStatistics({ wordCount, charCount, writingScore }: WritingStatisticsProps) {\n  const { t } = useI18n();\n  return (\n    <Card>\n      <CardHeader className=\"p-3 border-b\">\n          <CardTitle className=\"flex items-center text-base\">\n          <Activity className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('writingStatsTitle')}\n          </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-2 p-3\">\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <ListOrdered className=\"mr-2 h-3.5 w-3.5\" /> {t('wordCountLabel')}\n          </span>\n          <span className=\"font-medium\">{wordCount}</span>\n          </div>\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <BarChartHorizontalBig className=\"mr-2 h-3.5 w-3.5\" /> {t('charCountLabel')}\n          </span>\n          <span className=\"font-medium\">{charCount}</span>\n          </div>\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2 h-3.5 w-3.5 lucide lucide-award\"><circle cx=\"12\" cy=\"8\" r=\"6\"/><path d=\"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\"/></svg>\n              {t('writingScoreLabel')}\n          </span>\n          <span className=\"font-medium\">{writingScore} {t('writingScoreUnit')}</span>\n          </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAYO,SAAS,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAA0B;IAC9F,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BAClB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACrB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,EAAE;;;;;;;;;;;;0BAGP,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACnB,8OAAC;wBAAI,WAAU;;0CACf,8OAAC;gCAAK,WAAU;;kDACZ,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAqB;oCAAE,EAAE;;;;;;;0CAEpD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACf,8OAAC;gCAAK,WAAU;;kDACZ,8OAAC,kOAAA,CAAA,wBAAqB;wCAAC,WAAU;;;;;;oCAAqB;oCAAE,EAAE;;;;;;;0CAE9D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;;0CACf,8OAAC;gCAAK,WAAU;;kDACZ,8OAAC;wCAAI,OAAM;wCAA6B,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAO,QAAO;wCAAe,aAAY;wCAAI,eAAc;wCAAQ,gBAAe;wCAAQ,WAAU;;0DAAuC,8OAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAI,GAAE;;;;;;0DAAK,8OAAC;gDAAK,GAAE;;;;;;;;;;;;oCACxQ,EAAE;;;;;;;0CAEP,8OAAC;gCAAK,WAAU;;oCAAe;oCAAa;oCAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAK1D", "debugId": null}}, {"offset": {"line": 2916, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/shared/document-dropzone.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport type { DragEvent, ChangeEvent } from 'react';\nimport { useState, useRef, useCallback } from 'react';\nimport { UploadCloud } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface DocumentDropzoneProps {\n  onTextExtracted: (textContent: string) => void;\n  className?: string;\n}\n\nexport function DocumentDropzone({ onTextExtracted, className }: DocumentDropzoneProps) {\n  const [isDragActive, setIsDragActive] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const processFile = useCallback((file: File | null) => {\n    if (!file) return;\n\n    if (file.type !== \"text/plain\" && !file.name.endsWith(\".txt\")) {\n      toast({\n        titleKey: \"toastErrorTitle\",\n        descriptionKey: \"toastInvalidFileTypeMessage\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const textContent = e.target?.result;\n      if (typeof textContent === 'string') {\n        onTextExtracted(textContent);\n        toast({\n          titleKey: \"toastFileImportSuccessTitle\",\n          descriptionKey: \"toastFileImportSuccessMessage\",\n        });\n      } else {\n        toast({\n          titleKey: \"toastFileImportErrorTitle\",\n          descriptionKey: \"toastFileImportErrorMessage\",\n          variant: \"destructive\",\n        });\n      }\n    };\n    reader.onerror = () => {\n      toast({\n        titleKey: \"toastFileImportErrorTitle\",\n        descriptionKey: \"toastFileImportErrorMessage\",\n        variant: \"destructive\",\n      });\n    };\n    reader.readAsText(file);\n  }, [onTextExtracted, toast, t]);\n\n  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(true);\n  };\n\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(false);\n  };\n\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!isDragActive) setIsDragActive(true); // Ensure active state on drag over\n  };\n\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      processFile(e.dataTransfer.files[0]);\n    }\n  };\n\n  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      processFile(e.target.files[0]);\n      e.target.value = \"\"; // Reset file input\n    }\n  };\n\n  const openFileDialog = () => {\n    inputRef.current?.click();\n  };\n\n  return (\n    <div\n      onClick={openFileDialog}\n      onDrop={handleDrop}\n      onDragOver={handleDragOver}\n      onDragEnter={handleDragEnter}\n      onDragLeave={handleDragLeave}\n      className={cn(\n        \"border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 ease-in-out\",\n        isDragActive ? \"border-primary bg-primary/10\" : \"border-border hover:border-primary/70 hover:bg-muted/50\",\n        className\n      )}\n      role=\"button\"\n      tabIndex={0}\n      onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') openFileDialog();}}\n      aria-label={t('dropzoneAriaLabel')}\n    >\n      <input\n        ref={inputRef}\n        type=\"file\"\n        accept=\".txt,text/plain\"\n        className=\"hidden\"\n        onChange={handleFileInputChange}\n        data-testid=\"file-upload-input-dropzone\"\n      />\n      <UploadCloud className={cn(\"mx-auto h-8 w-8 mb-3\", isDragActive ? \"text-primary\" : \"text-muted-foreground\")} />\n      <p className=\"text-sm text-muted-foreground\">\n        {t('dropzoneInstruction')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,iBAAiB,EAAE,eAAe,EAAE,SAAS,EAAyB;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM;QAEX,IAAI,KAAK,IAAI,KAAK,gBAAgB,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC7D,MAAM;gBACJ,UAAU;gBACV,gBAAgB;gBAChB,SAAS;YACX;YACA;QACF;QAEA,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,cAAc,EAAE,MAAM,EAAE;YAC9B,IAAI,OAAO,gBAAgB,UAAU;gBACnC,gBAAgB;gBAChB,MAAM;oBACJ,UAAU;oBACV,gBAAgB;gBAClB;YACF,OAAO;gBACL,MAAM;oBACJ,UAAU;oBACV,gBAAgB;oBAChB,SAAS;gBACX;YACF;QACF;QACA,OAAO,OAAO,GAAG;YACf,MAAM;gBACJ,UAAU;gBACV,gBAAgB;gBAChB,SAAS;YACX;QACF;QACA,OAAO,UAAU,CAAC;IACpB,GAAG;QAAC;QAAiB;QAAO;KAAE;IAE9B,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,CAAC,cAAc,gBAAgB,OAAO,mCAAmC;IAC/E;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;QAChB,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QACrC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC7B,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,mBAAmB;QAC1C;IACF;IAEA,MAAM,iBAAiB;QACrB,SAAS,OAAO,EAAE;IACpB;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+GACA,eAAe,iCAAiC,2DAChD;QAEF,MAAK;QACL,UAAU;QACV,WAAW,CAAC;YAAQ,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;QAAiB;QAC7E,cAAY,EAAE;;0BAEd,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,WAAU;gBACV,UAAU;gBACV,eAAY;;;;;;0BAEd,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,eAAe,iBAAiB;;;;;;0BACnF,8OAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 3063, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3116, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-tone-analysis.ts"], "sourcesContent": ["'use server';\n\n/**\n * @fileOverview An AI agent to analyze the tone of writing and provide feedback.\n *\n * - analyzeTone - A function that handles the tone analysis process.\n * - AnalyzeToneInput - The input type for the analyzeTone function.\n * - AnalyzeToneOutput - The return type for the analyzeTone function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst AnalyzeToneInputSchema = z.object({\n  text: z.string().describe('The text to analyze for tone.'),\n});\nexport type AnalyzeToneInput = z.infer<typeof AnalyzeToneInputSchema>;\n\nconst AnalyzeToneOutputSchema = z.object({\n  formality: z\n    .string()\n    .describe(\n      'The formality level of the text (e.g., formal, informal, neutral).'\n    ),\n  confidence: z\n    .string()\n    .describe('The confidence level of the text (e.g., confident, tentative).'),\n  feedback: z.string().describe('Feedback on how to adjust the writing style.'),\n});\nexport type AnalyzeToneOutput = z.infer<typeof AnalyzeToneOutputSchema>;\n\nexport async function analyzeTone(input: AnalyzeToneInput): Promise<AnalyzeToneOutput | null> {\n  return analyzeToneFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeTonePrompt',\n  input: {schema: AnalyzeToneInputSchema},\n  output: {schema: AnalyzeToneOutputSchema},\n  prompt: `You are an AI writing assistant that analyzes the tone of the given text and provides feedback on its formality and confidence levels.\n\nAnalyze the following text:\n\n{{{text}}}\n\nProvide the formality level, confidence level, and feedback on how to adjust the writing style to match the intended audience and purpose. Be concise.\n\nFormality:\nConfidence:\nFeedback:`,\n});\n\nconst analyzeToneFlow = ai.defineFlow(\n  {\n    name: 'analyzeToneFlow',\n    inputSchema: AnalyzeToneInputSchema,\n    outputSchema: AnalyzeToneOutputSchema.nullable(),\n  },\n  async (input: AnalyzeToneInput): Promise<AnalyzeToneOutput | null> => {\n    try {\n      const {output} = await prompt(input);\n       // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[analyzeToneFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IA+BsB", "debugId": null}}, {"offset": {"line": 3128, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-tone-analyzer.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Gauge, Loader2 } from \"lucide-react\";\nimport { analyzeTone, type AnalyzeToneOutput } from '@/ai/flows/ai-tone-analysis';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface AiToneAnalyzerProps {\n  currentText: string;\n}\n\nexport function AiToneAnalyzer({ currentText }: AiToneAnalyzerProps) {\n  const [analysisResult, setAnalysisResult] = useState<AnalyzeToneOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const result = await analyzeTone({ text: currentText });\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastToneAnalysisSuccess\" });\n      } else {\n        throw new Error(\"Tone analysis returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error analyzing tone:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastToneAnalysisError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiToneAnalysisDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('formalityLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.formality}</p>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('confidenceLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.confidence}</p>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('feedbackLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.feedback}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToAnalyzePlaceholder')}</p>\n          )}\n           {!isLoading && !analysisResult && currentText.trim().length > 0 && (\n             <p className=\"text-sm text-muted-foreground text-center py-8\">{t('Click the button to analyze the tone.')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Gauge className=\"mr-2 h-4 w-4\" />}\n            {t('analyzeToneButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAcO,SAAS,eAAe,EAAE,WAAW,EAAuB;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD,EAAE;gBAAE,MAAM;YAAY;YACrD,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA2B;YACpF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;QACxG,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAkB;;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,SAAS;;;;;;;;;;;;kDAExE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAmB;;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,UAAU;;;;;;;;;;;;kDAEzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAiB;;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,QAAQ;;;;;;;;;;;;;;;;;;4BAI1E,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;4BAEjE,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,GAAG,mBAC5D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGtE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,MAAM,KAAK;4BAAG,WAAU;;gCAC9E,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACjF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3406, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,qKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,qKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,qKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,qKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3486, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-text-generation.ts"], "sourcesContent": ["// This is an autogenerated file from Firebase Studio.\n\n'use server';\n\n/**\n * @fileOverview A flow for generating text content based on a user-provided prompt.\n *\n * - generateText - A function that takes a prompt and returns generatedText.\n * - GenerateTextInput - The input type for the generateText function.\n * - GenerateTextOutput - The return type for the generateText function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst GenerateTextInputSchema = z.object({\n  prompt: z.string().describe('The prompt to generate text from.'),\n});\nexport type GenerateTextInput = z.infer<typeof GenerateTextInputSchema>;\n\nconst GenerateTextOutputSchema = z.object({\n  generatedText: z.string().describe('The generated text content.'),\n});\nexport type GenerateTextOutput = z.infer<typeof GenerateTextOutputSchema>;\n\nexport async function generateText(input: GenerateTextInput): Promise<GenerateTextOutput> {\n  return aiTextGenerationFlow(input);\n}\n\nconst aiTextGenerationPrompt = ai.definePrompt({\n  name: 'aiTextGenerationPrompt',\n  input: {schema: GenerateTextInputSchema},\n  output: {schema: GenerateTextOutputSchema},\n  prompt: `Generate text content based on the following prompt:\\n\\n{{{prompt}}}`,\n});\n\nconst aiTextGenerationFlow = ai.defineFlow(\n  {\n    name: 'aiTextGenerationFlow',\n    inputSchema: GenerateTextInputSchema,\n    outputSchema: GenerateTextOutputSchema,\n  },\n  async (input: GenerateTextInput): Promise<GenerateTextOutput> => {\n    try {\n      const {output} = await aiTextGenerationPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[aiTextGenerationFlow] - Error: ${errorMessage}`, {input});\n      // Re-throw the error to be handled by the calling UI component\n      throw new Error(`AI text generation failed: ${errorMessage}`);\n    }\n  }\n);\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;IAyBhC", "debugId": null}}, {"offset": {"line": 3499, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-text-generator.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { Sparkles, Loader2, Wand2, History, Copy, ClipboardEdit, ClipboardPaste } from \"lucide-react\";\nimport { generateText } from '@/ai/flows/ai-text-generation';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface GenerationHistoryItem {\n  id: string;\n  prompt: string;\n  generatedText: string;\n}\n\ninterface AiTextGeneratorProps {\n  onInsertText: (text: string) => void;\n}\n\nconst MAX_HISTORY_ITEMS = 10;\n\nexport function AiTextGenerator({ onInsertText }: AiTextGeneratorProps) {\n  const [prompt, setPrompt] = useState(\"\");\n  const [generatedOutput, setGeneratedOutput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [generationHistory, setGenerationHistory] = useState<GenerationHistoryItem[]>([]);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    const currentPrompt = prompt.trim();\n    if (!currentPrompt) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastPromptRequiredError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setGeneratedOutput(\"\");\n\n    try {\n      const result = await generateText({ prompt: currentPrompt });\n       if (result && result.generatedText) {\n        setGeneratedOutput(result.generatedText);\n        setGenerationHistory(prev => \n          [{ id: Date.now().toString(), prompt: currentPrompt, generatedText: result.generatedText }, ...prev].slice(0, MAX_HISTORY_ITEMS)\n        );\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextGeneratedSuccess\" });\n      } else {\n        throw new Error(\"AI did not return valid generated text.\");\n      }\n    } catch (error) {\n      console.error(\"Error generating text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextGenerationError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleUsePrompt = (promptToUse: string) => {\n    setPrompt(promptToUse);\n    toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastPromptRestoredSuccess\" });\n  };\n\n  const handleCopyOutput = async (textToCopy: string) => {\n    try {\n      await navigator.clipboard.writeText(textToCopy);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextCopiedSuccess\" });\n    } catch (error) {\n      console.error(\"Failed to copy text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextCopyError\", variant: \"destructive\" });\n    }\n  };\n\n  const handleInsertIntoEditor = () => {\n    if (generatedOutput) {\n      onInsertText(generatedOutput);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiTextGenerationDescription')}</CardTitle>\n      </CardHeader>\n      <CardContent className=\"p-0\">\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"generation-prompt\" className=\"text-xs text-muted-foreground\">{t('yourPromptLabel')}</Label>\n            <Input\n              id=\"generation-prompt\"\n              value={prompt}\n              onChange={(e) => setPrompt(e.target.value)}\n              placeholder={t('promptPlaceholder')}\n              className=\"mt-1\"\n              disabled={isLoading}\n            />\n          </div>\n          {generatedOutput && (\n            <div>\n              <Label htmlFor=\"generated-text-output\" className=\"text-xs text-muted-foreground\">{t('generatedTextLabel')}</Label>\n              <Textarea\n                id=\"generated-text-output\"\n                value={generatedOutput}\n                readOnly\n                className=\"mt-1 min-h-[120px] bg-muted font-code\"\n              />\n               <div className=\"mt-2 flex justify-end\">\n                <Button \n                  type=\"button\" \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={handleInsertIntoEditor} \n                  disabled={isLoading || !generatedOutput}\n                  title={t('insertIntoEditorButtonTooltip')}\n                >\n                  <ClipboardPaste className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('insertIntoEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          <Button type=\"submit\" disabled={isLoading} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Sparkles className=\"mr-2 h-4 w-4\" />}\n            {t('generateTextButton')}\n          </Button>\n        </form>\n\n        {generationHistory.length > 0 && (\n            <Accordion type=\"single\" collapsible className=\"w-full mt-6\">\n            <AccordionItem value=\"generation-history\">\n                <AccordionTrigger className=\"text-sm font-medium\">\n                <div className=\"flex items-center\">\n                    <History className=\"mr-2 h-4 w-4\" />\n                    {t('generationHistoryTitle')} \n                </div>\n                </AccordionTrigger>\n                <AccordionContent className=\"px-1 pb-2 pt-0\">\n                <div className=\"space-y-3 max-h-80 overflow-y-auto\">\n                    {generationHistory.map(item => (\n                    <Card key={item.id} className=\"bg-muted/50\">\n                        <CardHeader className=\"p-3\">\n                        <Label className=\"text-xs text-muted-foreground\">{t('promptLabel')}</Label>\n                        <p className=\"text-xs font-code line-clamp-2\">{item.prompt}</p>\n                        </CardHeader>\n                        <CardContent className=\"p-3 pt-0\">\n                        <Label className=\"text-xs text-muted-foreground\">{t('outputLabel')}</Label>\n                        <Textarea\n                            value={item.generatedText}\n                            readOnly\n                            className=\"mt-1 h-20 text-xs font-code bg-background\"\n                        />\n                        </CardContent>\n                        <CardFooter className=\"p-3 pt-0 flex justify-end gap-2\">\n                        <Button \n                            variant=\"outline\" \n                            size=\"sm\" \n                            onClick={() => { onInsertText(item.generatedText); toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });}}\n                            title={t('insertIntoEditorButtonTooltip')}\n                        >\n                            <ClipboardPaste className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => handleUsePrompt(item.prompt)} title={t('useThisPromptButton')}>\n                            <ClipboardEdit className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => handleCopyOutput(item.generatedText)} title={t('copyOutputButton')}>\n                            <Copy className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        </CardFooter>\n                    </Card>\n                    ))}\n                </div>\n                </AccordionContent>\n            </AccordionItem>\n            </Accordion>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAwBA,MAAM,oBAAoB;AAEnB,SAAS,gBAAgB,EAAE,YAAY,EAAwB;IACpE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IACtF,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,MAAM,gBAAgB,OAAO,IAAI;QACjC,IAAI,CAAC,eAAe;YAClB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAA4B,SAAS;YAAc;YAChH;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;gBAAE,QAAQ;YAAc;YACzD,IAAI,UAAU,OAAO,aAAa,EAAE;gBACnC,mBAAmB,OAAO,aAAa;gBACvC,qBAAqB,CAAA,OACnB;wBAAC;4BAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;4BAAI,QAAQ;4BAAe,eAAe,OAAO,aAAa;wBAAC;2BAAM;qBAAK,CAAC,KAAK,CAAC,GAAG;gBAEhH,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA4B;YACrF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA4B,SAAS;YAAc;QAC1G,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU;QACV,MAAM;YAAE,UAAU;YAAkB,gBAAgB;QAA6B;IACnF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAyB;QAClF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAsB,SAAS;YAAc;QACpG;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB;YACnB,aAAa;YACb,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAiC,EAAE;;;;;;kDAChF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,aAAa,EAAE;wCACf,WAAU;wCACV,UAAU;;;;;;;;;;;;4BAGb,iCACC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEX,8OAAC;wCAAI,WAAU;kDACd,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC;4CACxB,OAAO,EAAE;;8DAET,8OAAC,0NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDACzB,EAAE;;;;;;;;;;;;;;;;;;0CAKX,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;gCAAW,WAAU;;oCAClD,0BAAY,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACpF,EAAE;;;;;;;;;;;;;oBAIN,kBAAkB,MAAM,GAAG,mBACxB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC/C,cAAA,8OAAC,qIAAA,CAAA,gBAAa;4BAAC,OAAM;;8CACjB,8OAAC,qIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,EAAE;;;;;;;;;;;;8CAGP,8OAAC,qIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAI,WAAU;kDACV,kBAAkB,GAAG,CAAC,CAAA,qBACvB,8OAAC,gIAAA,CAAA,OAAI;gDAAe,WAAU;;kEAC1B,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACtB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC,EAAE;;;;;;0EACpD,8OAAC;gEAAE,WAAU;0EAAkC,KAAK,MAAM;;;;;;;;;;;;kEAE1D,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC,EAAE;;;;;;0EACpD,8OAAC,oIAAA,CAAA,WAAQ;gEACL,OAAO,KAAK,aAAa;gEACzB,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACtB,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS;oEAAQ,aAAa,KAAK,aAAa;oEAAG,MAAM;wEAAE,UAAU;wEAAqB,gBAAgB;oEAA2B;gEAAG;gEACxI,OAAO,EAAE;0EAET,cAAA,8OAAC,0NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;0EAE9B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,SAAS,IAAM,gBAAgB,KAAK,MAAM;gEAAG,OAAO,EAAE;0EACtF,cAAA,8OAAC,uNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE7B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,SAAS,IAAM,iBAAiB,KAAK,aAAa;gEAAG,OAAO,EAAE;0EAC9F,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;+CA1Bb,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCtC", "debugId": null}}, {"offset": {"line": 3959, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-rewriter.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Loader2, CheckSquare, Wand2 } from \"lucide-react\";\nimport { rephraseText, type RephraseTextInput } from '@/ai/flows/contextual-ai-rephraser';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface AiRewriterProps {\n  currentText: string;\n  writingMode: string;\n  onApplyRewrite: (text: string) => void;\n  direction: 'ltr' | 'rtl';\n}\n\nexport function AiRewriter({ currentText, writingMode, onApplyRewrite, direction }: AiRewriterProps) {\n  const [rewrittenOutput, setRewrittenOutput] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setRewrittenOutput(null);\n\n    try {\n      const input: RephraseTextInput = {\n        selectedText: currentText,\n        contextText: currentText, \n        tone: writingMode,\n        style: writingMode,\n      };\n      const result = await rephraseText(input);\n      if (result && result.rephrasedText) {\n        setRewrittenOutput(result.rephrasedText);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastRewriteSuccess\" });\n      } else {\n        throw new Error(\"AI did not return a valid rewrite.\");\n      }\n    } catch (error) {\n      console.error(\"Error rewriting text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastRewriteError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleApplyToEditor = () => {\n    if (rewrittenOutput) {\n      onApplyRewrite(rewrittenOutput);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" }); \n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiRewriteDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {rewrittenOutput && (\n            <div className=\"relative\">\n              <Label htmlFor=\"rewritten-text-output\" className=\"text-xs text-muted-foreground\">{t('rewrittenTextLabel')}</Label>\n              <Textarea\n                id=\"rewritten-text-output\"\n                value={rewrittenOutput}\n                readOnly\n                dir={direction}\n                className=\"mt-1 min-h-[120px] bg-muted font-code pr-24\"\n              />\n              <div className=\"absolute bottom-2 right-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleApplyToEditor}\n                  disabled={isLoading || !rewrittenOutput}\n                  title={t('applyToEditorButton')}\n                >\n                  <CheckSquare className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('applyToEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          {!isLoading && !rewrittenOutput && currentText.trim().length === 0 && (\n            <div className=\"text-center py-8\">\n              <p className=\"text-sm text-muted-foreground\">{t('writeSomeTextToRewritePlaceholder')}</p>\n            </div>\n          )}\n           {!isLoading && !rewrittenOutput && currentText.trim().length > 0 && (\n             <div className=\"text-center py-8\">\n              <p className=\"text-sm text-muted-foreground\">{t('Click the button to rewrite the editor content.')}</p>\n            </div>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Wand2 className=\"mr-2 h-4 w-4\" />}\n            {t('rewriteEditorContentButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBO,SAAS,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAmB;IACjG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,QAA2B;gBAC/B,cAAc;gBACd,aAAa;gBACb,MAAM;gBACN,OAAO;YACT;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE;YAClC,IAAI,UAAU,OAAO,aAAa,EAAE;gBAClC,mBAAmB,OAAO,aAAa;gBACvC,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAsB;YAC/E,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAqB,SAAS;YAAc;QACnG,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,eAAe;YACf,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,iCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC;4CACxB,OAAO,EAAE;;8DAET,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,EAAE;;;;;;;;;;;;;;;;;;4BAKV,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAiC,EAAE;;;;;;;;;;;4BAGlD,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,GAAG,mBAC7D,8OAAC;gCAAI,WAAU;0CACd,cAAA,8OAAC;oCAAE,WAAU;8CAAiC,EAAE;;;;;;;;;;;;;;;;;kCAItD,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,+MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACjF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 4208, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/plagiarism-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect potential plagiarism in text.\n *\n * - detectPlagiarism - A function that handles the plagiarism detection process.\n * - PlagiarismDetectionInput - The input type for the detectPlagiarism function.\n * - PlagiarismDetectionOutput - The return type for the detectPlagiarism function.\n */\n\nimport {ai}from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst PlagiarismDetectionInputSchema = z.object({\n  text: z.string().describe('The text to analyze for plagiarism.'),\n});\nexport type PlagiarismDetectionInput = z.infer<typeof PlagiarismDetectionInputSchema>;\n\nconst PlagiarizedSegmentSchema = z.object({\n  plagiarizedSegment: z.string().describe('The exact text segment from the input that is potentially plagiarized.'),\n  originalSource: z.string().describe('The URL or a clear citation of the original source from which the content was likely derived.'),\n  similarityScore: z.number().min(0).max(100).describe('A score from 0 to 100 indicating the similarity to the original source.'),\n   startIndex: z\n    .number()\n    .describe(\n      'The 0-based starting character index of the plagiarized segment in the original text. This is mandatory.'\n    ),\n  endIndex: z\n    .number()\n    .describe(\n      'The 0-based ending character index (exclusive) of the plagiarized segment in the original text. This is mandatory.'\n    ),\n});\n\nconst PlagiarismDetectionOutputSchema = z.object({\n  originalityScore: z.number().min(0).max(100).describe(\"A score from 0 to 100, where 100 indicates completely original content and 0 indicates a high likelihood of plagiarism. This score should reflect the percentage of the text that appears to be original.\"),\n  detectedSources: z.array(PlagiarizedSegmentSchema).describe('A list of detected sources of potential plagiarism. If no plagiarism is found, this should be an empty array.'),\n  analysisReport: z.string().describe(\"A concise report summarizing the plagiarism analysis. Highlight any specific phrases or sentences that appear to be unoriginal, or confirm the text's originality if no issues are found. Keep this report brief, under 100 words.\"),\n});\nexport type PlagiarismDetectionOutput = z.infer<typeof PlagiarismDetectionOutputSchema>;\n\nexport async function detectPlagiarism(input: PlagiarismDetectionInput): Promise<PlagiarismDetectionOutput | null> {\n  return plagiarismDetectionFlow(input);\n}\n\nconst plagiarismDetectionPrompt = ai.definePrompt({\n  name: 'plagiarismDetectionPrompt',\n  input: {schema: PlagiarismDetectionInputSchema},\n  output: {schema: PlagiarismDetectionOutputSchema},\n  prompt: `You are an AI assistant specialized in detecting plagiarism in written text by comparing it against a vast database of existing works.\n\nYour task is to analyze the provided text for originality. For the given text, you must:\n1.  Calculate an overall 'originalityScore' from 0 to 100, where 100 means completely original and 0 indicates high plagiarism.\n2.  Identify specific sentences or paragraphs that appear to be copied or heavily paraphrased from other sources.\n3.  For each identified segment, you MUST provide the 'plagiarizedSegment', its 'startIndex' and 'endIndex' in the original text, the 'originalSource' (a URL or book citation), and a 'similarityScore' (0-100). The 'startIndex' and 'endIndex' are mandatory and must be accurate.\n4.  Provide a brief 'analysisReport' (under 100 words) summarizing your findings.\n5.  If you detect potential plagiarism, populate the 'detectedSources' array. If the text is original, the 'detectedSources' array must be empty.\n\nAnalyze the following text:\n{{{text}}}\n`,\n});\n\nconst plagiarismDetectionFlow = ai.defineFlow(\n  {\n    name: 'plagiarismDetectionFlow',\n    inputSchema: PlagiarismDetectionInputSchema,\n    outputSchema: PlagiarismDetectionOutputSchema.nullable(),\n  },\n  async (input: PlagiarismDetectionInput): Promise<PlagiarismDetectionOutput | null> => {\n    try {\n      const {output} = await plagiarismDetectionPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[plagiarismDetectionFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAyCsB", "debugId": null}}, {"offset": {"line": 4220, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/plagiarism-detector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent, useEffect } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, <PERSON>Header, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON><PERSON><PERSON>, Loader2, Al<PERSON><PERSON>riangle } from \"lucide-react\";\nimport { detectPlagiarism, type PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Progress } from '@/components/ui/progress';\n\ninterface PlagiarismDetectorProps {\n  currentText: string;\n  onResult: (result: PlagiarismDetectionOutput | null) => void;\n}\n\nexport function PlagiarismDetector({ currentText, onResult }: PlagiarismDetectorProps) {\n  const [analysisResult, setAnalysisResult] = useState<PlagiarismDetectionOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  useEffect(() => {\n    onResult(analysisResult);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [analysisResult]);\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const result = await detectPlagiarism({ text: currentText });\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastPlagiarismDetectionSuccess\" });\n      } else {\n        throw new Error(\"Plagiarism detection returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error detecting plagiarism:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPlagiarismDetectionError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('plagiarismDetectionDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('originalityScoreLabel')}:</h4>\n                <div className=\"flex items-center gap-2 mt-1\">\n                    <Progress value={analysisResult.originalityScore} className=\"w-[calc(100%-4rem)] h-2.5\" />\n                    <span className=\"text-sm text-foreground font-medium\">{analysisResult.originalityScore} / 100</span>\n                </div>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm mt-2\">{t('plagiarismReportLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.analysisReport}</p>\n              </div>\n\n              {analysisResult.detectedSources && analysisResult.detectedSources.length > 0 && (\n                <div className=\"mt-4 space-y-3\">\n                  <h4 className=\"font-semibold text-base flex items-center text-destructive\">\n                    <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                    {t('potentialSourcesFoundLabel')}\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {analysisResult.detectedSources.map((source, index) => (\n                      <div key={index} className=\"p-3 border-l-4 border-destructive bg-destructive/5 rounded-r-md\">\n                        <blockquote className=\"border-l-0 p-0\">\n                          <p className=\"text-sm font-medium text-destructive leading-relaxed\">\n                            \"{source.plagiarizedSegment}\"\n                          </p>\n                        </blockquote>\n                        <div className=\"mt-2 text-xs text-muted-foreground\">\n                          <p>\n                            <span className=\"font-semibold\">{t('similarityScoreLabel')}:</span> {source.similarityScore}%\n                          </p>\n                          <p>\n                            <span className=\"font-semibold\">{t('originalSourceLabel')}:</span>{' '}\n                            <a href={source.originalSource} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-destructive transition-colors break-all\">\n                              {source.originalSource}\n                            </a>\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToDetectPlagiarismPlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <ShieldCheck className=\"mr-2 h-4 w-4\" />}\n            {t('detectPlagiarismButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAgBO,SAAS,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAA2B;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;IACX,uDAAuD;IACvD,GAAG;QAAC;KAAe;IAEnB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,MAAM;YAAY;YAC1D,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAkC;YAC3F,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAiC,SAAS;YAAc;QAC/G,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAyB;;;;;;;0DAClE,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,gBAAgB;wDAAE,WAAU;;;;;;kEAC5D,8OAAC;wDAAK,WAAU;;4DAAuC,eAAe,gBAAgB;4DAAC;;;;;;;;;;;;;;;;;;;kDAG7F,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8B,EAAE;oDAAyB;;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,cAAc;;;;;;;;;;;;oCAG5E,eAAe,eAAe,IAAI,eAAe,eAAe,CAAC,MAAM,GAAG,mBACzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB,EAAE;;;;;;;0DAEL,8OAAC;gDAAI,WAAU;0DACZ,eAAe,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3C,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAW,WAAU;0EACpB,cAAA,8OAAC;oEAAE,WAAU;;wEAAuD;wEAChE,OAAO,kBAAkB;wEAAC;;;;;;;;;;;;0EAGhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;;oFAAiB,EAAE;oFAAwB;;;;;;;4EAAQ;4EAAE,OAAO,eAAe;4EAAC;;;;;;;kFAE9F,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;;oFAAiB,EAAE;oFAAuB;;;;;;;4EAAS;0FACnE,8OAAC;gFAAE,MAAM,OAAO,cAAc;gFAAE,QAAO;gFAAS,KAAI;gFAAsB,WAAU;0FACjF,OAAO,cAAc;;;;;;;;;;;;;;;;;;;uDAbpB;;;;;;;;;;;;;;;;;;;;;;4BAwBrB,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACvF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 4625, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-writing-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect if text was likely written by AI.\n *\n * - detectAiWriting - A function that handles the AI writing detection process.\n * - AiWritingDetectionInput - The input type for the detectAiWriting function.\n * - AiWritingDetectionOutput - The return type for the detectAiWriting function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst AiWritingDetectionInputSchema = z.object({\n  text: z.string().describe('The text to analyze for AI authorship.'),\n});\nexport type AiWritingDetectionInput = z.infer<typeof AiWritingDetectionInputSchema>;\n\nconst AiWritingDetectionOutputSchema = z.object({\n  probabilityAIWritten: z\n    .number()\n    .min(0)\n    .max(100)\n    .describe(\n      'A score from 0 to 100 indicating the likelihood that the text was written by AI. 100 means highly likely AI-generated, 0 means highly likely human-written.'\n    ),\n  summary: z\n    .string()\n    .describe(\n      \"A brief summary of the AI writing detection analysis. Explain the score and any notable characteristics found. Keep this summary concise, under 100 words.\"\n    ),\n});\nexport type AiWritingDetectionOutput = z.infer<typeof AiWritingDetectionOutputSchema>;\n\nexport async function detectAiWriting(input: AiWritingDetectionInput): Promise<AiWritingDetectionOutput | null> {\n  return aiWritingDetectionFlow(input);\n}\n\nconst aiWritingDetectionPrompt = ai.definePrompt({\n  name: 'aiWritingDetectionPrompt',\n  input: {schema: AiWritingDetectionInputSchema},\n  output: {schema: AiWritingDetectionOutputSchema},\n  prompt: `You are an expert in detecting AI-generated text. Analyze the provided text and determine the probability that it was written by an AI.\nYour task is to evaluate the text and provide a 'probabilityAIWritten' score between 0 and 100, where 100 indicates a high likelihood of AI generation and 0 indicates a high likelihood of human authorship.\nAlso, provide a brief 'summary' (under 100 words) explaining your reasoning and any stylistic indicators you found.\n\nAnalyze the following text:\n{{{text}}}\n`,\n});\n\nconst aiWritingDetectionFlow = ai.defineFlow(\n  {\n    name: 'aiWritingDetectionFlow',\n    inputSchema: AiWritingDetectionInputSchema,\n    outputSchema: AiWritingDetectionOutputSchema.nullable(),\n  },\n  async (input: AiWritingDetectionInput): Promise<AiWritingDetectionOutput | null> => {\n    try {\n      const {output} = await aiWritingDetectionPrompt(input);\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[aiWritingDetectionFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error to prevent server crashes on API failures.\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAkCsB", "debugId": null}}, {"offset": {"line": 4637, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-writing-detector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { BrainCircuit, Loader2 } from \"lucide-react\";\nimport { detectAiWriting, type AiWritingDetectionOutput } from '@/ai/flows/ai-writing-detection-flow';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Progress } from '@/components/ui/progress';\n\ninterface AiWritingDetectorProps {\n  currentText: string;\n}\n\nexport function AiWritingDetector({ currentText }: AiWritingDetectorProps) {\n  const [analysisResult, setAnalysisResult] = useState<AiWritingDetectionOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const result = await detectAiWriting({ text: currentText });\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastAiWritingDetectionSuccess\" });\n      } else {\n        throw new Error(\"AI Writing Detection returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error detecting AI writing:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastAiWritingDetectionError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiWritingDetectionDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('probabilityAIWrittenLabel')}:</h4>\n                <div className=\"flex items-center gap-2 mt-1\">\n                    <Progress value={analysisResult.probabilityAIWritten} className=\"w-[calc(100%-4rem)] h-2.5\" />\n                    <span className=\"text-sm text-foreground font-medium\">{analysisResult.probabilityAIWritten} / 100</span>\n                </div>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm mt-2\">{t('aiWritingDetectionSummaryLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.summary}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToDetectAiWritingPlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <BrainCircuit className=\"mr-2 h-4 w-4\" />}\n            {t('detectAiWritingButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAeO,SAAS,kBAAkB,EAAE,WAAW,EAA0B;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE;gBAAE,MAAM;YAAY;YACzD,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAiC;YAC1F,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAgC,SAAS;YAAc;QAC9G,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAA6B;;;;;;;0DACtE,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,oBAAoB;wDAAE,WAAU;;;;;;kEAChE,8OAAC;wDAAK,WAAU;;4DAAuC,eAAe,oBAAoB;4DAAC;;;;;;;;;;;;;;;;;;;kDAGjG,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8B,EAAE;oDAAkC;;;;;;;0DAChF,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,OAAO;;;;;;;;;;;;;;;;;;4BAIzE,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACxF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 4873, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/humanize-ai-text-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to rewrite AI-generated text into a more human-like style.\n *\n * - humanizeText - A function that handles the text humanization process.\n * - HumanizeTextInput - The input type for the humanizeText function.\n * - HumanizeTextOutput - The return type for the humanizeText function.\n */\n\nimport {ai}from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst HumanizeTextInputSchema = z.object({\n  text: z.string().describe('The AI-generated text to be humanized.'),\n});\nexport type HumanizeTextInput = z.infer<typeof HumanizeTextInputSchema>;\n\nconst HumanizeTextOutputSchema = z.object({\n  humanizedText: z\n    .string()\n    .describe('The text rewritten in a more human-like style.'),\n});\nexport type HumanizeTextOutput = z.infer<typeof HumanizeTextOutputSchema>;\n\nexport async function humanizeText(input: HumanizeTextInput): Promise<HumanizeTextOutput | null> {\n  return humanizeTextFlow(input);\n}\n\nconst humanizeTextPrompt = ai.definePrompt({\n  name: 'humanizeTextPrompt',\n  input: {schema: HumanizeTextInputSchema},\n  output: {schema: HumanizeTextOutputSchema},\n  prompt: `You are an expert editor specializing in transforming AI-generated text into writing that is clear, engaging, and indistinguishable from a skilled human author. Your task is to rewrite the provided text, focusing on the following principles to ensure it resonates with a natural, human touch, leaving no room for confusion.\n\n1.  **Enhance Readability and Flow:**\n    *   **Paragraph Structure:** Reorganize paragraphs if necessary to improve the logical progression of ideas. Ensure each paragraph focuses on a single, clear concept.\n    *   **Sentence Variety:** Eliminate monotonous sentence structures. Employ a mix of short, punchy sentences for emphasis and longer, more complex sentences to elaborate on ideas. Avoid repetitive sentence beginnings.\n    *   **Smooth Transitions:** Ensure seamless transitions between sentences and paragraphs. Use transition words and phrases naturally, avoiding clichés or predictable patterns. The goal is a smooth, logical flow from one idea to the next.\n\n2.  **Adopt a Natural, Human Voice:**\n    *   **Word Choice:** Replace robotic or overly formal vocabulary with more common, natural-sounding language. Use vivid verbs, concrete nouns, and relatable analogies where appropriate.\n    *   **Use Contractions:** Integrate contractions (e.g., \"it's,\" \"don't,\" \"you'll\") where they fit naturally to create a more conversational and approachable tone.\n    *   **Active Voice:** Strongly favor the active voice over the passive voice to make the writing more direct, energetic, and clear.\n\n3.  **Eliminate AI Hallmarks:**\n    *   **Cut Redundancy:** Aggressively remove filler words, boilerplate phrases, and repetitive statements often found in AI text (e.g., \"In conclusion,\" \"It is important to note,\" \"Moreover,\" \"Furthermore,\" \"In the world of...\").\n    *   **Be Direct and Concise:** Get straight to the point. Avoid unnecessary preambles or summaries unless they are essential to the text's purpose. Make every word count.\n\n4.  **Preserve the Core Message:**\n    *   **Accuracy is Paramount:** It is critical that you enhance the *style* and *readability* without altering the original meaning, key facts, or critical information of the text. Your rewrite must remain factually identical to the source.\n\nYour final output must ONLY be the rewritten, humanized text. Do not include any notes, explanations, apologies, or conversational filler.\n\nRewrite the following text:\n{{{text}}}\n`,\n});\n\nconst humanizeTextFlow = ai.defineFlow(\n  {\n    name: 'humanizeTextFlow',\n    inputSchema: HumanizeTextInputSchema,\n    outputSchema: HumanizeTextOutputSchema.nullable(),\n  },\n  async (input: HumanizeTextInput): Promise<HumanizeTextOutput | null> => {\n    try {\n      const {output} = await humanizeTextPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[humanizeTextFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAyBsB", "debugId": null}}, {"offset": {"line": 4885, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/humanize-ai-text.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { User<PERSON>he<PERSON>, Loader2, ClipboardPaste } from \"lucide-react\";\nimport { humanizeText, type HumanizeTextOutput } from '@/ai/flows/humanize-ai-text-flow';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface HumanizeAiTextProps {\n  currentText: string;\n  onInsertText: (text: string) => void;\n}\n\nexport function HumanizeAiText({ currentText, onInsertText }: HumanizeAiTextProps) {\n  const [humanizedOutput, setHumanizedOutput] = useState<HumanizeTextOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setHumanizedOutput(null);\n\n    try {\n      const result = await humanizeText({ text: currentText });\n      if (result) {\n        setHumanizedOutput(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastHumanizeTextSuccess\" });\n      } else {\n        throw new Error(\"Humanize text returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error humanizing text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastHumanizeTextError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInsertIntoEditor = () => {\n    if (humanizedOutput?.humanizedText) {\n      onInsertText(humanizedOutput.humanizedText);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('humanizeAiTextDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {humanizedOutput && (\n            <div>\n              <Label htmlFor=\"humanized-text-output\" className=\"text-xs text-muted-foreground\">{t('humanizedTextLabel')}</Label>\n              <Textarea\n                id=\"humanized-text-output\"\n                value={humanizedOutput.humanizedText}\n                readOnly\n                className=\"mt-1 min-h-[120px] bg-muted font-code\"\n              />\n              <div className=\"mt-2 flex justify-end\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleInsertIntoEditor}\n                  disabled={isLoading || !humanizedOutput?.humanizedText}\n                  title={t('insertIntoEditorButtonTooltip')}\n                >\n                  <ClipboardPaste className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('insertIntoEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          {!isLoading && !humanizedOutput && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToHumanizePlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <UserCheck className=\"mr-2 h-4 w-4\" />}\n            {t('humanizeTextButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAiBO,SAAS,eAAe,EAAE,WAAW,EAAE,YAAY,EAAuB;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;gBAAE,MAAM;YAAY;YACtD,IAAI,QAAQ;gBACV,mBAAmB;gBACnB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA2B;YACpF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;QACxG,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB,eAAe;YAClC,aAAa,gBAAgB,aAAa;YAC1C,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,iCACC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,gBAAgB,aAAa;wCACpC,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC,iBAAiB;4CACzC,OAAO,EAAE;;8DAET,8OAAC,0NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDACzB,EAAE;;;;;;;;;;;;;;;;;;4BAKV,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC/D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCACrF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/language-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect the language of a given text.\n *\n * - detectLanguage - A function that handles the language detection process.\n * - DetectLanguageInput - The input type for the detectLanguage function.\n * - DetectLanguageOutput - The return type for the detectLanguage function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport {z} from 'genkit';\n\nconst DetectLanguageInputSchema = z.object({\n  text: z.string().describe('The text to analyze for language detection.'),\n});\nexport type DetectLanguageInput = z.infer<typeof DetectLanguageInputSchema>;\n\nconst DetectLanguageOutputSchema = z.object({\n  languageCode: z\n    .string()\n    .describe(\n      \"The detected ISO 639-1 language code (e.g., 'en', 'es', 'fr'). Should be 'unknown' if not confident.\"\n    ),\n});\nexport type DetectLanguageOutput = z.infer<typeof DetectLanguageOutputSchema>;\n\nexport async function detectLanguage(input: DetectLanguageInput): Promise<DetectLanguageOutput> {\n  return languageDetectionFlow(input);\n}\n\nconst supportedLanguageCodes = APP_WRITING_LANGUAGES.map(lang => lang.value).join(', ');\n\nconst languageDetectionPrompt = ai.definePrompt({\n  name: 'languageDetectionPrompt',\n  input: {schema: DetectLanguageInputSchema},\n  output: {schema: DetectLanguageOutputSchema},\n  prompt: `Analyze the following text and determine its primary language. Respond with the ISO 639-1 code for the detected language (e.g., 'en', 'es', 'fr'). If the language is not clear or the text is too short, respond with the string 'unknown'. Do not provide any explanation, only the language code or 'unknown'. Supported language codes are: ${supportedLanguageCodes}.\n\nText to analyze:\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n`,\n});\n\nconst languageDetectionFlow = ai.defineFlow(\n  {\n    name: 'languageDetectionFlow',\n    inputSchema: DetectLanguageInputSchema,\n    outputSchema: DetectLanguageOutputSchema,\n  },\n  async (input: DetectLanguageInput): Promise<DetectLanguageOutput> => {\n    try {\n      const {output} = await languageDetectionPrompt(input);\n      if (!output || typeof output.languageCode !== 'string') {\n        const errorMessage = 'AI model did not return the expected languageCode string.';\n        console.error(`[languageDetectionFlow] - ${errorMessage} For input:`, input, 'Output received:', output);\n        return { languageCode: 'unknown' };\n      }\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[languageDetectionFlow] - Error during flow execution for input:`, input, error);\n      return { languageCode: 'unknown' };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IA4BsB", "debugId": null}}, {"offset": {"line": 5118, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-analysis-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent that analyzes text for grammar, spelling, punctuation, style, and clarity, providing suggestions.\n *\n * - analyzeText - A function that handles the text analysis process.\n * - TextAnalysisInput - The input type for the analyzeText function.\n * - TextAnalysisOutput - The return type for the analyzeText function.\n * - AnalysisSuggestion - The structure for individual suggestions.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst AnalysisSuggestionSchema = z.object({\n  id: z.string().describe('A unique identifier for the suggestion.'),\n  type: z\n    .enum(['spelling', 'grammar', 'rewrite', 'style'])\n    .describe(\n      \"The type of issue: 'spelling', 'grammar' (including punctuation), 'rewrite' (for clarity/flow), or 'style'.\"\n    ),\n  message: z.string().describe('A brief, user-friendly explanation of the issue and why the suggestion improves the text.'),\n  suggestion: z.string().describe('The corrected text segment.'),\n  originalSegment: z.string().describe('The original text segment that the suggestion refers to. This helps the user locate the issue if start/end indices are not perfectly accurate or for display purposes.'),\n  startIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based starting character index of the problematic segment in the original text. This can be approximate if the exact segment is hard to determine precisely.'\n    ),\n  endIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based ending character index (exclusive) of the problematic segment in the original text. This can be approximate.'\n    ),\n});\nexport type AnalysisSuggestion = z.infer<typeof AnalysisSuggestionSchema>;\n\nconst TextAnalysisInputSchema = z.object({\n  text: z.string().describe('The text to analyze.'),\n  language: z.string().describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\", \"ar\"). This helps tailor the analysis to the specific language.'),\n});\nexport type TextAnalysisInput = z.infer<typeof TextAnalysisInputSchema>;\n\nconst TextAnalysisOutputSchema = z.object({\n  suggestions: z\n    .array(AnalysisSuggestionSchema)\n    .describe('A list of suggestions for the input text.'),\n});\nexport type TextAnalysisOutput = z.infer<typeof TextAnalysisOutputSchema>;\n\nexport async function analyzeText(\n  input: TextAnalysisInput\n): Promise<TextAnalysisOutput> {\n  return textAnalysisFlow(input);\n}\n\nconst textAnalysisPrompt = ai.definePrompt({\n  name: 'textAnalysisPrompt',\n  input: {schema: TextAnalysisInputSchema},\n  output: {schema: TextAnalysisOutputSchema},\n  prompt: `You are an expert writing assistant. Your goal is to help users make their writing clear, natural, and human-sounding. The text you will analyze is in the language specified by the 'language' input field (e.g., 'en' for English, 'es' for Spanish, 'ar' for Arabic).\nYour task is to identify issues and provide suggestions for correction tailored to the grammar, spelling, punctuation, style, and clarity rules of that specific language ({{language}}).\n\nFor each issue you find, you MUST provide:\n1.  \\`id\\`: A unique string identifier for this specific suggestion (e.g., \"suggestion-1\", \"suggestion-2\").\n2.  \\`type\\`: The category of the issue. Must be one of:\n    *   'spelling': For misspelled words.\n    *   'grammar': For grammatical and punctuation errors (e.g., subject-verb agreement, tense, articles, comma usage, apostrophes).\n    *   'rewrite': For sentences that are grammatically correct but could be rewritten for better flow and impact. Focus on varying sentence structure, improving transitions, and ensuring the new phrasing connects logically with the surrounding text. Only suggest a rewrite if it provides a significant improvement; do not suggest rewrites for sentences that are already well-structured.\n    *   'style': Your primary focus for style is to enhance vocabulary by replacing weak, generic, or repeated verbs with more dynamic, vivid, and engaging alternatives. Introduce natural-sounding phrasal verbs where appropriate to make the text less robotic. Only provide 'style' suggestions for verbs. For example, instead of \"walked quickly,\" suggest \"dashed\"; instead of repeating \"said,\" suggest \"murmured\" or \"exclaimed.\"\n3.  \\`message\\`: A brief, user-friendly explanation of the issue and why your suggestion improves the text.\n4.  \\`suggestion\\`: The corrected or improved text segment.\n5.  \\`originalSegment\\`: The exact original text segment that this suggestion pertains to. This is crucial for the user to understand the context.\n6.  \\`startIndex\\` (optional): The 0-based starting character index of the 'originalSegment' in the *entire* provided text. This can be approximate if the exact segment is hard to determine precisely.\n7.  \\`endIndex\\` (optional): The 0-based ending character index (exclusive) of the 'originalSegment' in the *entire* provided text. This can be approximate.\n\nImportant Guidelines:\n- Focus on providing actionable and clear suggestions that make the writing feel more natural and human.\n- The 'style' and 'rewrite' suggestions are very important. Actively look for opportunities to make the language more powerful, engaging, and less robotic.\n- For 'rewrite' and 'style' suggestions, ensure the \\`originalSegment\\` captures enough context (e.g., a full sentence for rewrites, or a specific word/phrase for style).\n- Be conservative with \\`startIndex\\` and \\`endIndex\\`. If you cannot determine them with high confidence, it's better to rely on \\`originalSegment\\`.\n- If the text is perfect and has no issues, return an empty array for 'suggestions'.\n\nAnalyze the following text (language: {{language}}):\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n\nRespond with a JSON object containing a 'suggestions' array.\nExample for a single suggestion:\n{\n  \"suggestions\": [\n    {\n      \"id\": \"s1\",\n      \"type\": \"spelling\",\n      \"message\": \"'Helo' appears to be a misspelling of 'Hello'.\",\n      \"suggestion\": \"Hello\",\n      \"originalSegment\": \"Helo\",\n      \"startIndex\": 0,\n      \"endIndex\": 4\n    }\n  ]\n}\nIf multiple issues, add more objects to the 'suggestions' array.\n`,\n});\n\nconst textAnalysisFlow = ai.defineFlow(\n  {\n    name: 'textAnalysisFlow',\n    inputSchema: TextAnalysisInputSchema,\n    outputSchema: TextAnalysisOutputSchema,\n  },\n  async (input: TextAnalysisInput): Promise<TextAnalysisOutput> => {\n    if (!input.text.trim()) {\n      return {suggestions: []};\n    }\n    try {\n      const {output} = await textAnalysisPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      // We still handle the case where the model returns an empty suggestions array.\n      const suggestionsWithUniqueIds = (output?.suggestions || []).map((s, index) => ({\n        ...s,\n        id: s.id || `suggestion-${Date.now()}-${index}`, // Ensure unique ID\n      }));\n      return { suggestions: suggestionsWithUniqueIds };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[textAnalysisFlow] - Error: ${errorMessage}`, {input});\n       // For background tasks like real-time analysis, it's better to fail gracefully\n      // and return an empty result than to crash the UI with an error for a transient issue.\n      return { suggestions: [] };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAoDsB", "debugId": null}}, {"offset": {"line": 5130, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/lib/analysis/local-verb-repetition.ts"], "sourcesContent": ["import type { AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\n\n// Very lightweight English-only verb repetition detector with simple lemmatization\n// Emits green (style) suggestions for repeated common verbs and proposes dynamic alternatives\n\nconst COMMON_VERBS: Record<string, string[]> = {\n  say: [\"remark\", \"state\", \"mention\", \"note\", \"add\", \"point out\"],\n  go: [\"head\", \"make your way\", \"set off\", \"move\", \"venture\"],\n  get: [\"obtain\", \"secure\", \"grab\", \"pick up\", \"come by\"],\n  make: [\"create\", \"craft\", \"produce\", \"put together\", \"come up with\"],\n  take: [\"grab\", \"pick up\", \"seize\", \"adopt\", \"take on\"],\n  look: [\"glance\", \"peer\", \"gaze\", \"take a look\", \"scan\"],\n  use: [\"employ\", \"utilize\", \"leverage\", \"make use of\"],\n  find: [\"discover\", \"uncover\", \"come across\", \"identify\"],\n  tell: [\"inform\", \"notify\", \"advise\", \"let know\"],\n  work: [\"operate\", \"function\", \"collaborate\", \"work on\"],\n  try: [\"attempt\", \"endeavor\", \"have a go\", \"try out\"],\n  ask: [\"inquire\", \"question\", \"request\", \"reach out to\"],\n  need: [\"require\", \"must\", \"have to\", \"be in need of\"],\n  give: [\"provide\", \"offer\", \"grant\", \"hand over\"],\n  think: [\"consider\", \"reckon\", \"believe\", \"reflect\"],\n  goon: [\"continue\", \"carry on\", \"keep going\"],\n};\n\n// Heuristic lemmatizer for English verbs (very limited but fast and dependency-free)\nfunction lemmatize(word: string): string {\n  const w = word.toLowerCase();\n  if (w === \"was\" || w === \"were\" || w === \"am\" || w === \"is\" || w === \"are\" || w === \"been\" || w === \"being\") return \"be\";\n  if (w === \"did\" || w === \"done\" || w === \"does\" || w === \"do\") return \"do\";\n  if (w === \"had\" || w === \"has\" || w === \"have\") return \"have\";\n  // basic -ing, -ed, -s stripping\n  if (w.endsWith(\"ing\") && w.length > 5) return w.slice(0, -3);\n  if (w.endsWith(\"ed\") && w.length > 4) return w.slice(0, -2);\n  if (w.endsWith(\"es\") && w.length > 4) return w.slice(0, -2);\n  if (w.endsWith(\"s\") && w.length > 3) return w.slice(0, -1);\n  return w;\n}\n\ninterface Token {\n  text: string;\n  start: number;\n  end: number;\n}\n\nfunction tokenizeEnglish(text: string): Token[] {\n  const tokens: Token[] = [];\n  const regex = /[A-Za-z]+'?[A-Za-z]+|[A-Za-z]+/g; // words with optional apostrophes\n  let match: RegExpExecArray | null;\n  while ((match = regex.exec(text)) !== null) {\n    tokens.push({ text: match[0], start: match.index, end: match.index + match[0].length });\n  }\n  return tokens;\n}\n\nfunction chooseAlternative(lemma: string, original: string): string | null {\n  const alts = COMMON_VERBS[lemma];\n  if (!alts || alts.length === 0) return null;\n  // pick one that differs from the original ignoring case\n  const origLower = original.toLowerCase();\n  const pick = alts.find(a => a.toLowerCase() !== origLower) || alts[0];\n  return pick;\n}\n\nexport function generateRepeatedVerbSuggestions(text: string, languageBase: string): AnalysisSuggestion[] {\n  // Only run for English; for other languages defer to AI analysis\n  if (!text || languageBase !== 'en') return [];\n\n  const tokens = tokenizeEnglish(text);\n  if (tokens.length === 0) return [];\n\n  // Count occurrences by lemma for candidate verbs in our list\n  const counts: Record<string, number> = {};\n  const occurrences: Record<string, Token[]> = {};\n\n  for (const tok of tokens) {\n    const lemma = lemmatize(tok.text);\n    if (!COMMON_VERBS[lemma]) continue; // only track verbs we can suggest for\n    counts[lemma] = (counts[lemma] || 0) + 1;\n    (occurrences[lemma] ||= []).push(tok);\n  }\n\n  const suggestions: AnalysisSuggestion[] = [];\n\n  // Threshold: flag if a lemma appears 3+ times in the document\n  for (const [lemma, count] of Object.entries(counts)) {\n    if (count < 3) continue;\n    const occs = occurrences[lemma] || [];\n\n    // Suggest for latter occurrences to avoid over-highlighting; cap total suggestions per lemma\n    const maxPerLemma = 3;\n    let emitted = 0;\n    for (let i = 1; i < occs.length && emitted < maxPerLemma; i++) {\n      const tok = occs[i];\n      const alt = chooseAlternative(lemma, tok.text);\n      if (!alt) continue;\n\n      const id = `local-style-${lemma}-${tok.start}-${tok.end}`;\n      const message = `This verb appears repeatedly (\"${lemma}\"). Consider a more dynamic alternative.`;\n      suggestions.push({\n        id,\n        type: 'style',\n        message,\n        suggestion: alt,\n        originalSegment: text.slice(tok.start, tok.end),\n        startIndex: tok.start,\n        endIndex: tok.end,\n      });\n      emitted++;\n    }\n  }\n\n  return suggestions;\n}\n\n"], "names": [], "mappings": ";;;AAEA,mFAAmF;AACnF,8FAA8F;AAE9F,MAAM,eAAyC;IAC7C,KAAK;QAAC;QAAU;QAAS;QAAW;QAAQ;QAAO;KAAY;IAC/D,IAAI;QAAC;QAAQ;QAAiB;QAAW;QAAQ;KAAU;IAC3D,KAAK;QAAC;QAAU;QAAU;QAAQ;QAAW;KAAU;IACvD,MAAM;QAAC;QAAU;QAAS;QAAW;QAAgB;KAAe;IACpE,MAAM;QAAC;QAAQ;QAAW;QAAS;QAAS;KAAU;IACtD,MAAM;QAAC;QAAU;QAAQ;QAAQ;QAAe;KAAO;IACvD,KAAK;QAAC;QAAU;QAAW;QAAY;KAAc;IACrD,MAAM;QAAC;QAAY;QAAW;QAAe;KAAW;IACxD,MAAM;QAAC;QAAU;QAAU;QAAU;KAAW;IAChD,MAAM;QAAC;QAAW;QAAY;QAAe;KAAU;IACvD,KAAK;QAAC;QAAW;QAAY;QAAa;KAAU;IACpD,KAAK;QAAC;QAAW;QAAY;QAAW;KAAe;IACvD,MAAM;QAAC;QAAW;QAAQ;QAAW;KAAgB;IACrD,MAAM;QAAC;QAAW;QAAS;QAAS;KAAY;IAChD,OAAO;QAAC;QAAY;QAAU;QAAW;KAAU;IACnD,MAAM;QAAC;QAAY;QAAY;KAAa;AAC9C;AAEA,qFAAqF;AACrF,SAAS,UAAU,IAAY;IAC7B,MAAM,IAAI,KAAK,WAAW;IAC1B,IAAI,MAAM,SAAS,MAAM,UAAU,MAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS,MAAM,UAAU,MAAM,SAAS,OAAO;IACpH,IAAI,MAAM,SAAS,MAAM,UAAU,MAAM,UAAU,MAAM,MAAM,OAAO;IACtE,IAAI,MAAM,SAAS,MAAM,SAAS,MAAM,QAAQ,OAAO;IACvD,gCAAgC;IAChC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IAC1D,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACxD,OAAO;AACT;AAQA,SAAS,gBAAgB,IAAY;IACnC,MAAM,SAAkB,EAAE;IAC1B,MAAM,QAAQ,mCAAmC,kCAAkC;IACnF,IAAI;IACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,KAAM;QAC1C,OAAO,IAAI,CAAC;YAAE,MAAM,KAAK,CAAC,EAAE;YAAE,OAAO,MAAM,KAAK;YAAE,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QAAC;IACvF;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,KAAa,EAAE,QAAgB;IACxD,MAAM,OAAO,YAAY,CAAC,MAAM;IAChC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG,OAAO;IACvC,wDAAwD;IACxD,MAAM,YAAY,SAAS,WAAW;IACtC,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,OAAO,cAAc,IAAI,CAAC,EAAE;IACrE,OAAO;AACT;AAEO,SAAS,gCAAgC,IAAY,EAAE,YAAoB;IAChF,iEAAiE;IACjE,IAAI,CAAC,QAAQ,iBAAiB,MAAM,OAAO,EAAE;IAE7C,MAAM,SAAS,gBAAgB;IAC/B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,EAAE;IAElC,6DAA6D;IAC7D,MAAM,SAAiC,CAAC;IACxC,MAAM,cAAuC,CAAC;IAE9C,KAAK,MAAM,OAAO,OAAQ;QACxB,MAAM,QAAQ,UAAU,IAAI,IAAI;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,sCAAsC;QAC1E,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI;QACvC,CAAC,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE,IAAI,CAAC;IACnC;IAEA,MAAM,cAAoC,EAAE;IAE5C,8DAA8D;IAC9D,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACnD,IAAI,QAAQ,GAAG;QACf,MAAM,OAAO,WAAW,CAAC,MAAM,IAAI,EAAE;QAErC,6FAA6F;QAC7F,MAAM,cAAc;QACpB,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,UAAU,aAAa,IAAK;YAC7D,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,MAAM,kBAAkB,OAAO,IAAI,IAAI;YAC7C,IAAI,CAAC,KAAK;YAEV,MAAM,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE;YACzD,MAAM,UAAU,CAAC,+BAA+B,EAAE,MAAM,wCAAwC,CAAC;YACjG,YAAY,IAAI,CAAC;gBACf;gBACA,MAAM;gBACN;gBACA,YAAY;gBACZ,iBAAiB,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;gBAC9C,YAAY,IAAI,KAAK;gBACrB,UAAU,IAAI,GAAG;YACnB;YACA;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5321, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/linguaflow-header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Settings, Moon, Sun, HelpCircle, CheckSquare } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\n\ninterface LinguaFlowHeaderProps {\n  onSettingsClick: () => void;\n  onHelpClick: () => void;\n}\n\nexport function LinguaFlowHeader({ onSettingsClick, onHelpClick }: LinguaFlowHeaderProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const isRTL = uiLanguage === 'ar';\n\n  return (\n    <header className=\"sticky top-0 z-50 linguaflow-header shadow-sm\">\n      <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`flex justify-between items-center h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>\n          {/* Logo and Title */}\n          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <CheckSquare \n                className=\"text-2xl\" \n                style={{ color: 'var(--primary-color)' }}\n                size={32}\n              />\n              <h1 \n                className=\"text-xl font-bold\" \n                style={{ color: 'var(--text-primary)' }}\n              >\n                {t('appName')}\n              </h1>\n            </div>\n            \n            {/* Language Selector - Hidden on mobile */}\n            <div className={`hidden md:flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <span \n                className=\"text-sm\" \n                style={{ color: 'var(--text-secondary)' }}\n              >\n                {t('languageLabel')}:\n              </span>\n              <Select value={uiLanguage} onValueChange={setUiLanguage}>\n                <SelectTrigger \n                  className=\"w-auto min-w-[120px] h-8 text-sm linguaflow-input\"\n                  style={{\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-color)',\n                    color: 'var(--text-primary)'\n                  }}\n                >\n                  <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                </SelectTrigger>\n                <SelectContent>\n                  {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                    <SelectItem key={lang.value} value={lang.value}>\n                      {t(lang.labelKey)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className={`flex items-center space-x-2 sm:space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onSettingsClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('settingsTitle')}\n            >\n              <Settings className=\"h-5 w-5\" />\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={effectiveTheme === 'dark' ? t('switchToLightMode') : t('switchToDarkMode')}\n            >\n              {effectiveTheme === 'dark' ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onHelpClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('helpTitle')}\n            >\n              <HelpCircle className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;AAeO,SAAS,iBAAiB,EAAE,eAAe,EAAE,WAAW,EAAyB;IACtF,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,QAAQ,eAAe;IAE7B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,qBAAqB,IAAI;;kCAEzF,8OAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;0CAC7E,8OAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;kDAC7E,8OAAC,2NAAA,CAAA,cAAW;wCACV,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;wCACvC,MAAM;;;;;;kDAER,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAsB;kDAErC,EAAE;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,oBAAoB,IAAI;;kDACvF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAwB;;4CAEvC,EAAE;4CAAiB;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,8OAAC,kIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,OAAO;gDACT;0DAEA,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;kEAC3C,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,8OAAC;wBAAI,WAAW,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,IAAI;;0CAC1F,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,mBAAmB,SAAS,UAAU;gCAC9D,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,mBAAmB,SAAS,EAAE,uBAAuB,EAAE;0CAElE,mBAAmB,uBAClB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 5564, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/writing-tools-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { \n  Wrench, \n  FileText, \n  Zap, \n  CheckCircle, \n  Wand2, \n  Lightbulb, \n  Download, \n  Trash2,\n  Bot\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface WritingToolsSidebarProps {\n  writingMode: string;\n  onWritingModeChange: (mode: string) => void;\n  onFileImport: (file: File) => void;\n  onCheckText: () => void;\n  onAiRewrite: () => void;\n  onBrainstorm: () => void;\n  onExport: () => void;\n  onClear: () => void;\n  aiPrompt: string;\n  onAiPromptChange: (prompt: string) => void;\n  onGenerateText: () => void;\n  wordCount: number;\n  charCount: number;\n  errorCount: number;\n  qualityScore: number;\n}\n\nexport function WritingToolsSidebar({\n  writingMode,\n  onWritingModeChange,\n  onFileImport,\n  onCheckText,\n  onAiRewrite,\n  onBrainstorm,\n  onExport,\n  onClear,\n  aiPrompt,\n  onAiPromptChange,\n  onGenerateText,\n  wordCount,\n  charCount,\n  errorCount,\n  qualityScore\n}: WritingToolsSidebarProps) {\n  const { t } = useI18n();\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onFileImport(file);\n    }\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Writing Tools Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Wrench className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('writeToolsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Writing Mode */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('writingModeLabel')}\n            </label>\n            <Select value={writingMode} onValueChange={onWritingModeChange}>\n              <SelectTrigger className=\"linguaflow-input\">\n                <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"casual\">📝 {t('casualWritingMode')}</SelectItem>\n                <SelectItem value=\"formal\">👔 {t('formalWritingMode')}</SelectItem>\n                <SelectItem value=\"academic\">🎓 {t('academicWritingMode')}</SelectItem>\n                <SelectItem value=\"creative\">🎨 {t('creativeWritingMode')}</SelectItem>\n                <SelectItem value=\"business\">💼 {t('businessWritingMode')}</SelectItem>\n                <SelectItem value=\"technical\">⚙️ {t('technicalWritingMode')}</SelectItem>\n                <SelectItem value=\"professional\">💼 {t('professionalWritingMode')}</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Import Document */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('importDocumentTitle')}\n            </label>\n            <div \n              className=\"border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400\"\n              style={{ borderColor: 'var(--border-color)' }}\n              onClick={() => document.getElementById('fileInput')?.click()}\n            >\n              <FileText className=\"mx-auto h-8 w-8 mb-2\" style={{ color: 'var(--text-secondary)' }} />\n              <p className=\"text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                {t('dropzoneInstruction')}\n              </p>\n              <input\n                type=\"file\"\n                id=\"fileInput\"\n                className=\"hidden\"\n                accept=\".txt,.md,.docx\"\n                onChange={handleFileChange}\n              />\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('quickActionTitle')}\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onCheckText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <CheckCircle className=\"mr-1 h-4 w-4\" />\n                Check Text\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiRewrite}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Wand2 className=\"mr-1 h-4 w-4\" />\n                AI Rewrite ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onBrainstorm}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Lightbulb className=\"mr-1 h-4 w-4\" />\n                Brainstorm ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onExport}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Download className=\"mr-1 h-4 w-4\" />\n                Export\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onClear}\n                className=\"linguaflow-button-secondary col-span-2\"\n              >\n                <Trash2 className=\"mr-1 h-4 w-4\" />\n                {t('clearEditorButton')}\n              </Button>\n            </div>\n          </div>\n\n          {/* AI Text Generation */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              AI Text Generation ✨\n            </label>\n            <Textarea\n              value={aiPrompt}\n              onChange={(e) => onAiPromptChange(e.target.value)}\n              rows={2}\n              className=\"linguaflow-input\"\n              placeholder={t('promptPlaceholder')}\n            />\n            <Button\n              onClick={onGenerateText}\n              className=\"linguaflow-button w-full\"\n              size=\"sm\"\n            >\n              <Bot className=\"mr-1 h-4 w-4\" />\n              Generate with AI ✨\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Writing Statistics Card */}\n      <Card className=\"linguaflow-stats\">\n        <CardContent className=\"p-4\">\n          <h4 className=\"font-semibold text-center mb-3 text-white\">\n            {t('writingStatsTitle')}\n          </h4>\n          <div className=\"space-y-2 text-sm text-white\">\n            <div className=\"flex justify-between items-center\">\n              <span>{t('wordCountLabel')}:</span>\n              <span className=\"font-medium\">{wordCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>{t('charCountLabel')}:</span>\n              <span className=\"font-medium\">{charCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Issues:</span>\n              <span className=\"font-medium\">{errorCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Score:</span>\n              <span className=\"font-medium text-lg\">{qualityScore}%</span>\n            </div>\n          </div>\n          <div className=\"mt-3 linguaflow-progress\">\n            <div \n              className=\"linguaflow-progress-bar\"\n              style={{ width: `${qualityScore}%` }}\n            />\n          </div>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAlBA;;;;;;;;AAsCO,SAAS,oBAAoB,EAClC,WAAW,EACX,mBAAmB,EACnB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACa;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACvE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAa,eAAe;;0DACzC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAY;4DAAI,EAAE;;;;;;;kEACpC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAe;4DAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa;wCAAsB;wCAC5C,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc;;0DAErD,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,OAAO;oDAAE,OAAO;gDAAwB;;;;;;0DACnF,8OAAC;gDAAE,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAwB;0DAC5D,EAAE;;;;;;0DAEL,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,QAAO;gDACP,UAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAAG;;;;;;kDAGlF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,MAAM;wCACN,WAAU;wCACV,aAAa,EAAE;;;;;;kDAEjB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;;0DAEL,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAuB;gDAAa;;;;;;;;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 6189, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/analysis-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { \n  Lightbulb, \n  MessageCircle, \n  History, \n  Search,\n  Sliders\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\ninterface AnalysisSidebarProps {\n  suggestions: Suggestion[];\n  toneAnalysis: ToneAnalysis;\n  recentPrompts: RecentPrompt[];\n  onApplySuggestion: (suggestionId: string) => void;\n  onAdjustTone: () => void;\n  onUsePrompt: (prompt: string) => void;\n}\n\nexport function AnalysisSidebar({\n  suggestions,\n  toneAnalysis,\n  recentPrompts,\n  onApplySuggestion,\n  onAdjustTone,\n  onUsePrompt\n}: AnalysisSidebarProps) {\n  const { t } = useI18n();\n\n  const getSuggestionTypeColor = (type: string) => {\n    switch (type) {\n      case 'grammar':\n      case 'spelling':\n        return 'var(--error-color)';\n      case 'style':\n        return 'var(--success-color)';\n      case 'clarity':\n        return 'var(--info-color)';\n      case 'tone':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n\n  const getSuggestionTypeIcon = (type: string) => {\n    const color = getSuggestionTypeColor(type);\n    return (\n      <div \n        className=\"w-3 h-3 rounded-full mr-2 flex-shrink-0\"\n        style={{ backgroundColor: color }}\n      />\n    );\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Suggestions Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Lightbulb className=\"mr-2 h-5 w-5\" style={{ color: 'var(--warning-color)' }} />\n            {t('writingSuggestionsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-60 linguaflow-scrollbar\">\n            {suggestions.length > 0 ? (\n              <div className=\"space-y-3\">\n                {suggestions.map((suggestion) => (\n                  <div\n                    key={suggestion.id}\n                    className=\"p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onApplySuggestion(suggestion.id)}\n                  >\n                    <div className=\"flex items-start\">\n                      {getSuggestionTypeIcon(suggestion.type)}\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium\" style={{ color: 'var(--text-primary)' }}>\n                          {suggestion.type.charAt(0).toUpperCase() + suggestion.type.slice(1)}\n                        </p>\n                        <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                          \"{suggestion.text}\" → \"{suggestion.suggestion}\"\n                        </p>\n                        {suggestion.explanation && (\n                          <p className=\"text-xs mt-1 italic\" style={{ color: 'var(--text-secondary)' }}>\n                            {suggestion.explanation}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\" style={{ color: 'var(--text-secondary)' }}>\n                <Search className=\"mx-auto h-8 w-8 mb-2\" />\n                <p className=\"text-sm\">\n                  {t('startTypingForSuggestionsDescription')}\n                </p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n\n      {/* Tone Analysis Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <MessageCircle className=\"mr-2 h-5 w-5\" style={{ color: 'var(--info-color)' }} />\n            {t('aiToneAnalysisTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>Overall Tone:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-accent)' }}>\n                {toneAnalysis.overall}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('formalityLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.formality}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('confidenceLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.confidence}\n              </span>\n            </div>\n          </div>\n          <Button\n            onClick={onAdjustTone}\n            variant=\"secondary\"\n            size=\"sm\"\n            className=\"w-full linguaflow-button-secondary\"\n          >\n            <Sliders className=\"mr-1 h-4 w-4\" />\n            Adjust Tone ✨\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* Recent AI Prompts Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <History className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('generationHistoryTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-40 linguaflow-scrollbar\">\n            {recentPrompts.length > 0 ? (\n              <div className=\"space-y-2\">\n                {recentPrompts.map((prompt) => (\n                  <div\n                    key={prompt.id}\n                    className=\"p-2 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onUsePrompt(prompt.prompt)}\n                  >\n                    <p className=\"text-xs font-medium truncate\" style={{ color: 'var(--text-primary)' }}>\n                      {prompt.prompt}\n                    </p>\n                    <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                      {prompt.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-4\" style={{ color: 'var(--text-secondary)' }}>\n                <p className=\"text-sm\">{t('noGenerationsYetPlaceholder')}</p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;AA4CO,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,aAAa,EACb,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACU;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,uBAAuB;QACrC,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,iBAAiB;YAAM;;;;;;IAGtC;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCAC1E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,YAAY,MAAM,GAAG,kBACpB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,kBAAkB,WAAW,EAAE;kDAE9C,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,sBAAsB,WAAW,IAAI;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAsB;sEACtE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC;;;;;;sEAEnE,8OAAC;4DAAE,WAAU;4DAAe,OAAO;gEAAE,OAAO;4DAAwB;;gEAAG;gEACnE,WAAW,IAAI;gEAAC;gEAAM,WAAW,UAAU;gEAAC;;;;;;;wDAE/C,WAAW,WAAW,kBACrB,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAwB;sEACxE,WAAW,WAAW;;;;;;;;;;;;;;;;;;uCAnB1B,WAAW,EAAE;;;;;;;;;qDA4BxB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;;kDACxE,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAoB;;;;;;gCAC3E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;0DACjD,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAqB;0DAChE,aAAa,OAAO;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAkB;;;;;;;0DACtE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,SAAS;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAmB;;;;;;;0DACvE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,UAAU;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACxE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,cAAc,MAAM,GAAG,kBACtB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,YAAY,OAAO,MAAM;;0DAExC,8OAAC;gDAAE,WAAU;gDAA+B,OAAO;oDAAE,OAAO;gDAAsB;0DAC/E,OAAO,MAAM;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;gDAAe,OAAO;oDAAE,OAAO;gDAAwB;0DACjE,OAAO,SAAS,CAAC,kBAAkB;;;;;;;uCAZjC,OAAO,EAAE;;;;;;;;;qDAkBpB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;0CACxE,cAAA,8OAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 6695, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/linguaflow-editor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { \n  Undo2, \n  Redo2, \n  Wand2, \n  Circle,\n  X\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface LinguaFlowEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n  isAutoCorrectionEnabled: boolean;\n  onToggleAutoCorrection: () => void;\n  suggestions: any[];\n  onApplySuggestion: (suggestionId: string) => void;\n  direction: 'ltr' | 'rtl';\n  currentLanguage: string;\n  lastSaved?: Date;\n  cursorPosition?: { line: number; col: number };\n  selectionInfo?: string;\n}\n\nexport function LinguaFlowEditor({\n  value,\n  onChange,\n  onUndo,\n  onRedo,\n  canUndo,\n  canRedo,\n  isAutoCorrectionEnabled,\n  onToggleAutoCorrection,\n  suggestions,\n  onApplySuggestion,\n  direction,\n  currentLanguage,\n  lastSaved,\n  cursorPosition = { line: 1, col: 1 },\n  selectionInfo = ''\n}: LinguaFlowEditorProps) {\n  const { t } = useI18n();\n  const editorRef = useRef<HTMLDivElement>(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAiActions, setShowAiActions] = useState(false);\n  const [aiActionPosition, setAiActionPosition] = useState({ x: 0, y: 0 });\n\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    if (selection && selection.toString().trim()) {\n      const selectedText = selection.toString().trim();\n      setSelectedText(selectedText);\n      \n      // Get selection position for AI actions panel\n      const range = selection.getRangeAt(0);\n      const rect = range.getBoundingClientRect();\n      setAiActionPosition({ x: rect.left, y: rect.bottom + 10 });\n      setShowAiActions(true);\n    } else {\n      setShowAiActions(false);\n      setSelectedText('');\n    }\n  }, []);\n\n  const handleEditorChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {\n    const newValue = e.currentTarget.textContent || '';\n    onChange(newValue);\n  }, [onChange]);\n\n  const aiActions = [\n    { id: 'rewrite-formal', label: 'Formalize', action: () => console.log('Formalize') },\n    { id: 'rewrite-casual', label: 'Casualize', action: () => console.log('Casualize') },\n    { id: 'rewrite-shorter', label: 'Shorten', action: () => console.log('Shorten') },\n    { id: 'rewrite-longer', label: 'Lengthen', action: () => console.log('Lengthen') },\n    { id: 'summarize', label: 'Summarize', action: () => console.log('Summarize') },\n    { id: 'explain', label: 'Explain', action: () => console.log('Explain') },\n  ];\n\n  return (\n    <section className=\"space-y-4\">\n      {/* Main Editor Card */}\n      <Card className=\"linguaflow-card shadow-lg\">\n        <CardHeader className=\"border-b px-4 py-3 flex flex-row items-center justify-between\" style={{ borderColor: 'var(--border-color)' }}>\n          <h3 className=\"font-semibold\" style={{ color: 'var(--text-primary)' }}>\n            Document Editor\n          </h3>\n          <div className=\"flex items-center space-x-2\">\n            {/* Status Indicator */}\n            <div className=\"flex items-center space-x-1 text-sm\" style={{ color: 'var(--text-secondary)' }}>\n              <Circle \n                className=\"h-3 w-3 fill-current\" \n                style={{ color: 'var(--success-color)' }}\n              />\n              <span>Ready</span>\n            </div>\n\n            {/* Auto-correction Toggle */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onToggleAutoCorrection}\n              className=\"p-1 rounded\"\n              style={{ \n                color: isAutoCorrectionEnabled ? 'var(--primary-color)' : 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title={isAutoCorrectionEnabled ? 'Auto-correction enabled' : 'Auto-correction disabled'}\n            >\n              <Wand2 className=\"h-4 w-4\" />\n            </Button>\n\n            {/* Undo/Redo */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onUndo}\n              disabled={!canUndo}\n              className=\"p-1 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title=\"Undo (Ctrl+Z)\"\n            >\n              <Undo2 className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onRedo}\n              disabled={!canRedo}\n              className=\"p-1 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              title=\"Redo (Ctrl+Y)\"\n            >\n              <Redo2 className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n\n        {/* Editor Content */}\n        <div className=\"linguaflow-editor linguaflow-scrollbar\">\n          <div\n            ref={editorRef}\n            contentEditable\n            suppressContentEditableWarning\n            onInput={handleEditorChange}\n            onMouseUp={handleTextSelection}\n            onKeyUp={handleTextSelection}\n            className=\"p-6 min-h-[380px] outline-none\"\n            style={{\n              color: 'var(--text-primary)',\n              lineHeight: '1.75',\n              direction: direction,\n              textAlign: direction === 'rtl' ? 'right' : 'left'\n            }}\n            placeholder={t('editorPlaceholder')}\n            dangerouslySetInnerHTML={{ __html: value }}\n          />\n        </div>\n\n        {/* Editor Footer */}\n        <div \n          className=\"border-t px-4 py-2 flex items-center justify-between text-xs\"\n          style={{ \n            borderColor: 'var(--border-color)',\n            backgroundColor: 'var(--bg-alt)',\n            color: 'var(--text-secondary)'\n          }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <span>\n              Last saved: <span className=\"font-medium\">\n                {lastSaved ? lastSaved.toLocaleTimeString() : 'Never'}\n              </span>\n            </span>\n            <span>\n              Language: <span className=\"font-medium\">{currentLanguage}</span>\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {selectionInfo && <span>{selectionInfo}</span>}\n            <span>Line {cursorPosition.line}, Col {cursorPosition.col}</span>\n          </div>\n        </div>\n      </Card>\n\n      {/* AI Context Panel */}\n      {showAiActions && selectedText && (\n        <Card \n          className=\"linguaflow-card animate-in fade-in-0 zoom-in-95\"\n          style={{\n            position: 'fixed',\n            top: aiActionPosition.y,\n            left: aiActionPosition.x,\n            zIndex: 1000,\n            maxWidth: '400px'\n          }}\n        >\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-semibold flex items-center text-sm\" style={{ color: 'var(--primary-color)' }}>\n                <Wand2 className=\"mr-2 h-4 w-4\" />\n                AI Actions for Selection ✨\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setShowAiActions(false)}\n                className=\"h-6 w-6 p-1\"\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <p \n              className=\"text-sm italic p-2 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n            >\n              \"{selectedText}\"\n            </p>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {aiActions.map((action) => (\n                <Button\n                  key={action.id}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={action.action}\n                  className=\"linguaflow-button-secondary text-xs\"\n                >\n                  {action.label}\n                </Button>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAZA;;;;;;;AAgCO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,uBAAuB,EACvB,sBAAsB,EACtB,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,eAAe,EACf,SAAS,EACT,iBAAiB;IAAE,MAAM;IAAG,KAAK;AAAE,CAAC,EACpC,gBAAgB,EAAE,EACI;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,YAAY,OAAO,YAAY;QACrC,IAAI,aAAa,UAAU,QAAQ,GAAG,IAAI,IAAI;YAC5C,MAAM,eAAe,UAAU,QAAQ,GAAG,IAAI;YAC9C,gBAAgB;YAEhB,8CAA8C;YAC9C,MAAM,QAAQ,UAAU,UAAU,CAAC;YACnC,MAAM,OAAO,MAAM,qBAAqB;YACxC,oBAAoB;gBAAE,GAAG,KAAK,IAAI;gBAAE,GAAG,KAAK,MAAM,GAAG;YAAG;YACxD,iBAAiB;QACnB,OAAO;YACL,iBAAiB;YACjB,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI;QAChD,SAAS;IACX,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAmB,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;QAChF;YAAE,IAAI;YAAkB,OAAO;YAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAY;QACjF;YAAE,IAAI;YAAa,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;KACzE;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAgE,OAAO;4BAAE,aAAa;wBAAsB;;0CAChI,8OAAC;gCAAG,WAAU;gCAAgB,OAAO;oCAAE,OAAO;gCAAsB;0CAAG;;;;;;0CAGvE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;wCAAsC,OAAO;4CAAE,OAAO;wCAAwB;;0DAC3F,8OAAC,sMAAA,CAAA,SAAM;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAuB;;;;;;0DAEzC,8OAAC;0DAAK;;;;;;;;;;;;kDAIR,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAO;4CACL,OAAO,0BAA0B,yBAAyB;4CAC1D,iBAAiB;wCACnB;wCACA,OAAO,0BAA0B,4BAA4B;kDAE7D,cAAA,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAInB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,OAAM;kDAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,OAAM;kDAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,eAAe;4BACf,8BAA8B;4BAC9B,SAAS;4BACT,WAAW;4BACX,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,YAAY;gCACZ,WAAW;gCACX,WAAW,cAAc,QAAQ,UAAU;4BAC7C;4BACA,aAAa,EAAE;4BACf,yBAAyB;gCAAE,QAAQ;4BAAM;;;;;;;;;;;kCAK7C,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,iBAAiB;4BACjB,OAAO;wBACT;;0CAEA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;0DACQ,8OAAC;gDAAK,WAAU;0DACzB,YAAY,UAAU,kBAAkB,KAAK;;;;;;;;;;;;kDAGlD,8OAAC;;4CAAK;0DACM,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;;oCACZ,+BAAiB,8OAAC;kDAAM;;;;;;kDACzB,8OAAC;;4CAAK;4CAAM,eAAe,IAAI;4CAAC;4CAAO,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAM9D,iBAAiB,8BAChB,8OAAC,gIAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK,iBAAiB,CAAC;oBACvB,MAAM,iBAAiB,CAAC;oBACxB,QAAQ;oBACR,UAAU;gBACZ;;kCAEA,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAA0C,OAAO;wCAAE,OAAO;oCAAuB;;sDAC7F,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;;oCACD;oCACG;oCAAa;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,uBACd,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,OAAO,MAAM;wCACtB,WAAU;kDAET,OAAO,KAAK;uCANR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}, {"offset": {"line": 7164, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/new-design.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\nimport { LinguaFlowHeader } from '@/components/layout/linguaflow-header';\nimport { WritingToolsSidebar } from '@/components/layout/writing-tools-sidebar';\nimport { AnalysisSidebar } from '@/components/layout/analysis-sidebar';\nimport { LinguaFlowEditor } from '@/components/editor/linguaflow-editor';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\nexport default function NewDesignPage() {\n  const { t, uiLanguage, writingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n  const isRTL = uiLanguage === 'ar';\n\n  // Editor state\n  const [editorValue, setEditorValue] = useState('');\n  const [writingMode, setWritingMode] = useState('formal');\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const [isAutoCorrectionEnabled, setIsAutoCorrectionEnabled] = useState(true);\n  const [aiPrompt, setAiPrompt] = useState('');\n\n  // Statistics\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [errorCount, setErrorCount] = useState(0);\n  const [qualityScore, setQualityScore] = useState(100);\n\n  // Analysis data\n  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);\n  const [toneAnalysis, setToneAnalysis] = useState<ToneAnalysis>({\n    overall: 'Neutral',\n    formality: 'Professional (75%)',\n    confidence: 'Confident (80%)'\n  });\n  const [recentPrompts, setRecentPrompts] = useState<RecentPrompt[]>([]);\n\n  // Modal states\n  const [showSettings, setShowSettings] = useState(false);\n  const [showHelp, setShowHelp] = useState(false);\n\n  // Update statistics when editor value changes\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(word => word.length > 0);\n    setWordCount(words.length);\n    setCharCount(editorValue.length);\n    \n    // Mock error count and quality score\n    const errors = Math.max(0, Math.floor(words.length * 0.05) - Math.floor(Math.random() * 3));\n    setErrorCount(errors);\n    setQualityScore(Math.max(60, 100 - errors * 5));\n  }, [editorValue]);\n\n  // Handlers\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    // Implement undo logic\n    console.log('Undo');\n  }, []);\n\n  const handleRedo = useCallback(() => {\n    // Implement redo logic\n    console.log('Redo');\n  }, []);\n\n  const handleFileImport = useCallback((file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      setEditorValue(content);\n      toast({\n        title: t('toastFileImportSuccessTitle'),\n        description: t('toastFileImportSuccessMessage'),\n      });\n    };\n    reader.readAsText(file);\n  }, [toast, t]);\n\n  const handleCheckText = useCallback(() => {\n    // Mock suggestions\n    const mockSuggestions: Suggestion[] = [\n      {\n        id: '1',\n        type: 'grammar',\n        text: 'are',\n        suggestion: 'is',\n        explanation: 'Subject-verb agreement error'\n      },\n      {\n        id: '2',\n        type: 'style',\n        text: 'very good',\n        suggestion: 'excellent',\n        explanation: 'More precise word choice'\n      }\n    ];\n    setSuggestions(mockSuggestions);\n    toast({\n      title: 'Text Analysis Complete',\n      description: `Found ${mockSuggestions.length} suggestions`,\n    });\n  }, [toast]);\n\n  const handleAiRewrite = useCallback(() => {\n    toast({\n      title: 'AI Rewrite',\n      description: 'AI rewrite feature activated',\n    });\n  }, [toast]);\n\n  const handleBrainstorm = useCallback(() => {\n    toast({\n      title: 'Brainstorm',\n      description: 'Brainstorm feature activated',\n    });\n  }, [toast]);\n\n  const handleExport = useCallback(() => {\n    const blob = new Blob([editorValue], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'document.txt';\n    a.click();\n    URL.revokeObjectURL(url);\n    toast({\n      title: 'Export Complete',\n      description: 'Document exported successfully',\n    });\n  }, [editorValue, toast]);\n\n  const handleClear = useCallback(() => {\n    setEditorValue('');\n    setSuggestions([]);\n    toast({\n      title: t('toastEditorClearedSuccess'),\n    });\n  }, [toast, t]);\n\n  const handleGenerateText = useCallback(() => {\n    if (!aiPrompt.trim()) {\n      toast({\n        title: t('toastInputRequiredTitle'),\n        description: t('toastPromptRequiredError'),\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    // Mock text generation\n    const generatedText = `Generated text based on: \"${aiPrompt}\"`;\n    setEditorValue(prev => prev + (prev ? '\\n\\n' : '') + generatedText);\n    \n    // Add to recent prompts\n    const newPrompt: RecentPrompt = {\n      id: Date.now().toString(),\n      prompt: aiPrompt,\n      timestamp: new Date()\n    };\n    setRecentPrompts(prev => [newPrompt, ...prev.slice(0, 4)]);\n    setAiPrompt('');\n    \n    toast({\n      title: t('toastTextGeneratedSuccess'),\n    });\n  }, [aiPrompt, toast, t]);\n\n  const handleApplySuggestion = useCallback((suggestionId: string) => {\n    const suggestion = suggestions.find(s => s.id === suggestionId);\n    if (suggestion) {\n      const newValue = editorValue.replace(suggestion.text, suggestion.suggestion);\n      setEditorValue(newValue);\n      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n      toast({\n        title: t('toastSuggestionAppliedSuccess'),\n      });\n    }\n  }, [suggestions, editorValue, toast, t]);\n\n  const handleAdjustTone = useCallback(() => {\n    toast({\n      title: 'Tone Adjustment',\n      description: 'Tone adjustment feature activated',\n    });\n  }, [toast]);\n\n  const handleUsePrompt = useCallback((prompt: string) => {\n    setAiPrompt(prompt);\n  }, []);\n\n  const writingDirection = writingLanguageDialect?.startsWith('ar') ? 'rtl' : 'ltr';\n  const currentLanguage = writingLanguageDialect || 'English';\n\n  return (\n    <div \n      className=\"min-h-screen transition-all duration-300\"\n      style={{ \n        backgroundColor: 'var(--bg-primary)',\n        color: 'var(--text-primary)'\n      }}\n    >\n      {/* Header */}\n      <LinguaFlowHeader\n        onSettingsClick={() => setShowSettings(true)}\n        onHelpClick={() => setShowHelp(true)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className={`grid grid-cols-1 lg:grid-cols-12 gap-6 ${isRTL ? 'direction-rtl' : ''}`}>\n          {/* Left Sidebar - Writing Tools */}\n          <div className=\"lg:col-span-3\">\n            <WritingToolsSidebar\n              writingMode={writingMode}\n              onWritingModeChange={setWritingMode}\n              onFileImport={handleFileImport}\n              onCheckText={handleCheckText}\n              onAiRewrite={handleAiRewrite}\n              onBrainstorm={handleBrainstorm}\n              onExport={handleExport}\n              onClear={handleClear}\n              aiPrompt={aiPrompt}\n              onAiPromptChange={setAiPrompt}\n              onGenerateText={handleGenerateText}\n              wordCount={wordCount}\n              charCount={charCount}\n              errorCount={errorCount}\n              qualityScore={qualityScore}\n            />\n          </div>\n\n          {/* Center - Editor */}\n          <div className=\"lg:col-span-6\">\n            <LinguaFlowEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n              isAutoCorrectionEnabled={isAutoCorrectionEnabled}\n              onToggleAutoCorrection={() => setIsAutoCorrectionEnabled(!isAutoCorrectionEnabled)}\n              suggestions={suggestions}\n              onApplySuggestion={handleApplySuggestion}\n              direction={writingDirection}\n              currentLanguage={currentLanguage}\n              lastSaved={new Date()}\n            />\n          </div>\n\n          {/* Right Sidebar - Analysis */}\n          <div className=\"lg:col-span-3\">\n            <AnalysisSidebar\n              suggestions={suggestions}\n              toneAnalysis={toneAnalysis}\n              recentPrompts={recentPrompts}\n              onApplySuggestion={handleApplySuggestion}\n              onAdjustTone={handleAdjustTone}\n              onUsePrompt={handleUsePrompt}\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAgCe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACtC,MAAM,QAAQ,eAAe;IAE7B,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,SAAS;QACT,WAAW;QACX,YAAY;IACd;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAC3E,aAAa,MAAM,MAAM;QACzB,aAAa,YAAY,MAAM;QAE/B,qCAAqC;QACrC,MAAM,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxF,cAAc;QACd,gBAAgB,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS;IAC9C,GAAG;QAAC;KAAY;IAEhB,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,eAAe;YACf,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;YACjB;QACF;QACA,OAAO,UAAU,CAAC;IACpB,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,mBAAmB;QACnB,MAAM,kBAAgC;YACpC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;SACD;QACD,eAAe;QACf,MAAM;YACJ,OAAO;YACP,aAAa,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;QAC5D;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;QACpB,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,eAAe;QACf,eAAe,EAAE;QACjB,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,uBAAuB;QACvB,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QAC9D,eAAe,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;QAErD,wBAAwB;QACxB,MAAM,YAA0B;YAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ;YACR,WAAW,IAAI;QACjB;QACA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAc,KAAK,KAAK,CAAC,GAAG;aAAG;QACzD,YAAY;QAEZ,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAU;QAAO;KAAE;IAEvB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,YAAY;YACd,MAAM,WAAW,YAAY,OAAO,CAAC,WAAW,IAAI,EAAE,WAAW,UAAU;YAC3E,eAAe;YACf,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACjD,MAAM;gBACJ,OAAO,EAAE;YACX;QACF;IACF,GAAG;QAAC;QAAa;QAAa;QAAO;KAAE;IAEvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,mBAAmB,wBAAwB,WAAW,QAAQ,QAAQ;IAC5E,MAAM,kBAAkB,0BAA0B;IAElD,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;0BAGA,8OAAC,oJAAA,CAAA,mBAAgB;gBACf,iBAAiB,IAAM,gBAAgB;gBACvC,aAAa,IAAM,YAAY;;;;;;0BAIjC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,kBAAkB,IAAI;;sCAEtF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,sBAAmB;gCAClB,aAAa;gCACb,qBAAqB;gCACrB,cAAc;gCACd,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,UAAU;gCACV,SAAS;gCACT,UAAU;gCACV,kBAAkB;gCAClB,gBAAgB;gCAChB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,cAAc;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oJAAA,CAAA,mBAAgB;gCACf,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,SAAS;gCACT,SAAS;gCACT,yBAAyB;gCACzB,wBAAwB,IAAM,2BAA2B,CAAC;gCAC1D,aAAa;gCACb,mBAAmB;gCACnB,WAAW;gCACX,iBAAiB;gCACjB,WAAW,IAAI;;;;;;;;;;;sCAKnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mJAAA,CAAA,kBAAe;gCACd,aAAa;gCACb,cAAc;gCACd,eAAe;gCACf,mBAAmB;gCACnB,cAAc;gCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 7502, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { AppShell } from '@/components/layout/app-shell';\nimport { EnhancedTextEditor, type PlagiarismSource } from '@/components/editor/enhanced-text-editor';\nimport { WritingSuggestionsPanel } from '@/components/editor/writing-suggestions-panel';\nimport { WritingStatistics } from '@/components/stats/writing-statistics';\nimport { DocumentDropzone } from '@/components/shared/document-dropzone';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { AiToneAnalyzer } from '@/components/ai/ai-tone-analyzer';\nimport { AiTextGenerator } from '@/components/ai/ai-text-generator';\nimport { AiRewriter } from '@/components/ai/ai-rewriter';\nimport { PlagiarismDetector } from '@/components/ai/plagiarism-detector';\nimport { AiWritingDetector } from '@/components/ai/ai-writing-detector';\nimport { HumanizeAiText } from '@/components/ai/humanize-ai-text';\nimport { Edit3, Wand2, ShieldCheck, BrainCircuit, UserCheck, Gauge } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\nimport { detectLanguage } from '@/ai/flows/language-detection-flow';\nimport { analyzeText, type TextAnalysisInput, type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { generateRepeatedVerbSuggestions } from '@/lib/analysis/local-verb-repetition';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\n\nimport type { PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';\nimport { useDebounce } from '@/hooks/use-debounce';\n\ninterface LinguaFlowPageProps {\n  writingMode: string;\n}\n\nfunction LinguaFlowPage({ writingMode }: LinguaFlowPageProps) {\n  const { t, getWritingLanguageBase, setWritingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n\n  const [editorValue, setEditorValue] = useState(\"\");\n  const history = useRef<{ past: string[]; present: string; future: string[] }>({ past: [], present: \"\", future: [] });\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const isUndoRedoAction = useRef(false);\n\n  const [writingDirection, setWritingDirection] = useState<'ltr' | 'rtl'>('ltr');\n  const isRTL = writingDirection === 'rtl';\n  \n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [writingScore, setWritingScore] = useState(0);\n  \n  const [analysisSuggestions, setAnalysisSuggestions] = useState<AnalysisSuggestion[]>([]);\n  const [isAnalyzingText, setIsAnalyzingText] = useState(false);\n  const [plagiarismResult, setPlagiarismResult] = useState<PlagiarismDetectionOutput | null>(null);\n\n  const debouncedEditorValueForHistory = useDebounce(editorValue, 800);\n  const debouncedEditorValueForAnalysis = useDebounce(editorValue, 1500);\n  const debouncedEditorValueForLangDetection = useDebounce(editorValue, 2000);\n\n  const plagiarismSuggestions = useMemo((): PlagiarismSource[] => {\n    if (!plagiarismResult || !plagiarismResult.detectedSources) return [];\n    \n    return plagiarismResult.detectedSources.map((source, index) => ({\n      id: `plagiarism-${index}-${source.startIndex}`,\n      type: 'plagiarism',\n      originalSegment: source.plagiarizedSegment,\n      message: `Potential plagiarism detected. Source: ${source.originalSource} (Similarity: ${source.similarityScore}%)`,\n      suggestion: source.originalSource, // Store source in suggestion field\n      startIndex: source.startIndex,\n      endIndex: source.endIndex,\n    }));\n  }, [plagiarismResult]);\n  \n  // History and Undo/Redo Logic\n  const updateHistory = useCallback((value: string) => {\n    const { past, present } = history.current;\n    if (value === present) return;\n    \n    history.current = {\n        past: [...past, present],\n        present: value,\n        future: [],\n    };\n    setCanUndo(history.current.past.length > 0);\n    setCanRedo(history.current.future.length > 0);\n  }, []);\n\n  useEffect(() => {\n    if (isUndoRedoAction.current) {\n        isUndoRedoAction.current = false;\n        return;\n    }\n    updateHistory(debouncedEditorValueForHistory);\n  }, [debouncedEditorValueForHistory, updateHistory]);\n\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    const { past, present, future } = history.current;\n    if (past.length === 0) return;\n    isUndoRedoAction.current = true;\n    const newPresent = past[past.length - 1];\n    const newPast = past.slice(0, past.length - 1);\n    history.current = {\n        past: newPast,\n        present: newPresent,\n        future: [present, ...future],\n    };\n    setEditorValue(newPresent);\n    setCanUndo(newPast.length > 0);\n    setCanRedo(true);\n  }, []);\n\n  const handleRedo = useCallback(() => {\n      const { past, present, future } = history.current;\n      if (future.length === 0) return;\n      isUndoRedoAction.current = true;\n      const newPresent = future[0];\n      const newFuture = future.slice(1);\n      history.current = {\n          past: [...past, present],\n          present: newPresent,\n          future: newFuture,\n      };\n      setEditorValue(newPresent);\n      setCanUndo(true);\n      setCanRedo(newFuture.length > 0);\n  }, []);\n\n  // Text Analysis Logic\n  const requestTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {\n    if (!textToAnalyze.trim()) {\n      setAnalysisSuggestions([]);\n      setIsAnalyzingText(false);\n      return;\n    }\n    setIsAnalyzingText(true);\n    try {\n      const analysisInput: TextAnalysisInput = {\n        text: textToAnalyze,\n        language: lang,\n      };\n      const result = await analyzeText(analysisInput);\n      const aiSuggestions = result.suggestions || [];\n\n      // Merge in local repeated-verb suggestions (green underline), avoiding duplicates by id range overlap\n      const localVerbSuggestions = settings.enableLocalVerbRepetitionDetection\n        ? generateRepeatedVerbSuggestions(textToAnalyze, lang)\n        : [];\n\n      // Deduplicate: avoid overlapping same-span style suggestions\n      const merged = [...aiSuggestions];\n      for (const ls of localVerbSuggestions) {\n        const overlaps = merged.some(ms => {\n          if (ms.type !== 'style' || ls.startIndex == null || ls.endIndex == null || ms.startIndex == null || ms.endIndex == null) return false;\n          return Math.max(ls.startIndex, ms.startIndex) < Math.min(ls.endIndex, ms.endIndex);\n        });\n        if (!overlaps) merged.push(ls);\n      }\n\n      setAnalysisSuggestions(merged);\n    } catch (error) {\n      console.error(\"Error analyzing text for suggestions:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextAnalysisError\", variant: \"destructive\" });\n      setAnalysisSuggestions([]);\n    } finally {\n      setIsAnalyzingText(false);\n    }\n  }, [toast]);\n  \n  useEffect(() => {\n    requestTextAnalysis(debouncedEditorValueForAnalysis, getWritingLanguageBase());\n  }, [debouncedEditorValueForAnalysis, getWritingLanguageBase, requestTextAnalysis]);\n\n  // Language Detection Logic\n  const handleLanguageDetection = useCallback(async (text: string) => {\n    if (!text || typeof text !== 'string' || text.trim().length < 50) return; \n\n    try {\n        const result = await detectLanguage({ text });\n        const detectedLangCode = result.languageCode;\n        const currentBaseLang = getWritingLanguageBase();\n\n        if (detectedLangCode && detectedLangCode !== 'unknown' && detectedLangCode !== currentBaseLang) {\n            const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === detectedLangCode);\n            if (langInfo) {\n                const newDialect = langInfo.dialects && langInfo.dialects.length > 0\n                    ? langInfo.dialects[0].value\n                    : langInfo.value;\n                \n                setWritingLanguageDialect(newDialect);\n                \n                toast({\n                    titleKey: \"toastInfoTitle\",\n                    descriptionKey: \"toastLanguageSwitched\",\n                    descriptionParams: { language: t(langInfo.labelKey) }\n                });\n            }\n        }\n    } catch (error) {\n        console.error(\"Automatic language detection failed:\", error);\n    }\n  }, [getWritingLanguageBase, setWritingLanguageDialect, toast, t]);\n\n  useEffect(() => {\n    const currentBaseLang = getWritingLanguageBase();\n    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === currentBaseLang);\n    if (langInfo) {\n        setWritingDirection(langInfo.dir || 'ltr');\n    }\n\n    if (settings.enableAutomaticLanguageDetection) {\n      handleLanguageDetection(debouncedEditorValueForLangDetection);\n    }\n  }, [debouncedEditorValueForLangDetection, settings.enableAutomaticLanguageDetection, getWritingLanguageBase, handleLanguageDetection]);\n\n  // Editor stats\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(Boolean);\n    const currentWordCount = words.length === 1 && words[0] === \"\" ? 0 : words.length;\n    setWordCount(currentWordCount);\n    setCharCount(editorValue.length);\n    // A more balanced scoring system\n    const baseScore = Math.max(0, 100 - (analysisSuggestions.length * 5) - (plagiarismSuggestions.length * 10));\n    const lengthBonus = Math.min(20, Math.floor(currentWordCount / 25));\n    setWritingScore(Math.max(0, Math.min(100, Math.round(baseScore + lengthBonus))));\n  }, [editorValue, analysisSuggestions, plagiarismSuggestions]);\n\n  const handleApplySuggestion = useCallback((suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => {\n    let newValue = editorValue;\n    let applied = false;\n    if (typeof startIndex === 'number' && typeof endIndex === 'number' && endIndex >= startIndex) {\n        const textBefore = editorValue.substring(0, startIndex);\n        const textAfter = editorValue.substring(endIndex);\n        if (editorValue.substring(startIndex, endIndex) === originalSegment) {\n            newValue = textBefore + suggestionText + textAfter;\n            applied = true;\n        }\n    }\n    if (!applied) {\n        // Fallback for when indices are not perfect\n        const firstOccurrenceIndex = editorValue.indexOf(originalSegment);\n        if (firstOccurrenceIndex !== -1) {\n            newValue = editorValue.substring(0, firstOccurrenceIndex) + suggestionText + editorValue.substring(firstOccurrenceIndex + originalSegment.length);\n            applied = true;\n        }\n    }\n    if (applied) {\n        setEditorValue(newValue); // Directly set value, history will update via effect\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastSuggestionAppliedSuccess\" });\n    } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastSuggestionApplyError\", variant: \"destructive\" });\n    }\n  }, [editorValue, toast]);\n\n  const handleDismissSuggestion = useCallback((suggestionId: string) => {\n    setAnalysisSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n    toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastSuggestionDismissed\" });\n  }, [toast]);\n  \n  const handleInsertGeneratedText = useCallback((textToInsert: string) => {\n    const newText = editorValue.trim() === \"\" ? textToInsert : `${editorValue}\\n\\n${textToInsert}`;\n    setEditorValue(newText);\n  }, [editorValue]);\n\n  const handleApplyRewrite = useCallback((newText: string) => {\n    setEditorValue(newText);\n  }, []);\n\n  return (\n      <div className=\"flex-1 flex flex-col gap-6 p-4 md:p-6\">\n        {/* Top Section: Editor and Suggestions */}\n        <div className={`grid grid-cols-1 xl:grid-cols-4 gap-6 flex-grow ${isRTL ? 'direction-rtl' : ''}`}>\n          <div className=\"xl:col-span-3 h-full min-h-[500px]\">\n            <EnhancedTextEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              writingMode={writingMode}\n              direction={writingDirection}\n              suggestions={analysisSuggestions}\n              plagiarismSources={plagiarismSuggestions}\n              onApplySuggestion={handleApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n            />\n          </div>\n          <div className=\"h-full flex flex-col gap-4\">\n            <WritingSuggestionsPanel\n              suggestions={analysisSuggestions}\n              isAnalyzing={isAnalyzingText}\n              onApplySuggestion={handleApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n            />\n            <WritingStatistics\n              wordCount={wordCount}\n              charCount={charCount}\n              writingScore={writingScore}\n            />\n\n            {/* Smaller Dropzone positioned below Writing Statistics */}\n            <DocumentDropzone onTextExtracted={setEditorValue} className=\"p-3 text-xs [&>svg]:h-5 [&>svg]:w-5 [&>svg]:mb-2\" />\n\n            {/* AI Tools Tabs positioned under dropzone with flexible spacing */}\n            <Tabs defaultValue=\"ai-writer\" className=\"w-full\">\n              <div className=\"space-y-3\">\n                <TabsList className={`h-auto p-1 bg-muted grid grid-cols-2 gap-1 sm:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 ${isRTL ? 'text-right' : ''}`}>\n                  <TabsTrigger\n                    value=\"ai-writer\"\n                    className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                  >\n                    <Wand2 className=\"h-3 w-3 mr-1.5\"/>\n                    <span className=\"truncate\">{t('aiRewriteTitle')}</span>\n                  </TabsTrigger>\n                  <TabsTrigger\n                    value=\"content-generator\"\n                    className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                  >\n                    <Edit3 className=\"h-3 w-3 mr-1.5\"/>\n                    <span className=\"truncate\">{t('aiTextGenerationTitle')}</span>\n                  </TabsTrigger>\n                  <TabsTrigger\n                    value=\"tone-analyzer\"\n                    className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                  >\n                    <Gauge className=\"h-3 w-3 mr-1.5\"/>\n                    <span className=\"truncate\">{t('aiToneAnalysisTitle')}</span>\n                  </TabsTrigger>\n                  {settings.enablePlagiarismDetection && (\n                    <TabsTrigger\n                      value=\"plagiarism-detector\"\n                      className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                    >\n                      <ShieldCheck className=\"h-3 w-3 mr-1.5\"/>\n                      <span className=\"truncate\">{t('plagiarismDetectionTitle')}</span>\n                    </TabsTrigger>\n                  )}\n                  <TabsTrigger\n                    value=\"ai-writing-detector\"\n                    className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                  >\n                    <BrainCircuit className=\"h-3 w-3 mr-1.5\"/>\n                    <span className=\"truncate\">{t('aiWritingDetectionTitle')}</span>\n                  </TabsTrigger>\n                  <TabsTrigger\n                    value=\"humanize-text\"\n                    className=\"h-8 px-2 text-xs font-medium justify-start data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\"\n                  >\n                    <UserCheck className=\"h-3 w-3 mr-1.5\"/>\n                    <span className=\"truncate\">{t('humanizeAiTextTitle')}</span>\n                  </TabsTrigger>\n                </TabsList>\n              </div>\n\n              <div className=\"pt-6\">\n                <TabsContent value=\"ai-writer\">\n                  <AiRewriter currentText={editorValue} onApplyRewrite={handleApplyRewrite} writingMode={writingMode} direction={writingDirection} />\n                </TabsContent>\n                <TabsContent value=\"content-generator\">\n                  <AiTextGenerator onInsertText={handleInsertGeneratedText} />\n                </TabsContent>\n                <TabsContent value=\"tone-analyzer\">\n                  {settings.enableToneDetection && <AiToneAnalyzer currentText={editorValue} />}\n                </TabsContent>\n                <TabsContent value=\"plagiarism-detector\">\n                  {settings.enablePlagiarismDetection && <PlagiarismDetector currentText={editorValue} onResult={setPlagiarismResult} />}\n                </TabsContent>\n                <TabsContent value=\"ai-writing-detector\">\n                  <AiWritingDetector currentText={editorValue} />\n                </TabsContent>\n                <TabsContent value=\"humanize-text\">\n                  <HumanizeAiText currentText={editorValue} onInsertText={handleInsertGeneratedText} />\n                </TabsContent>\n              </div>\n            </Tabs>\n          </div>\n        </div>\n      </div>\n  );\n}\n\n// Import the new design\nimport NewDesignPage from './new-design';\n\nexport default function LinguaFlowPageContainer() {\n  // Use the new design instead of the old AppShell\n  return <NewDesignPage />;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAsWA,wBAAwB;AACxB;AAhYA;;;;;;;;;;;;;;;;;;;;;;;AA+BA,SAAS,eAAe,EAAE,WAAW,EAAuB;IAC1D,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IAEtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyD;QAAE,MAAM,EAAE;QAAE,SAAS;QAAI,QAAQ,EAAE;IAAC;IAClH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,QAAQ,qBAAqB;IAEnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IAE3F,MAAM,iCAAiC,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IAChE,MAAM,kCAAkC,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IACjE,MAAM,uCAAuC,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IAEtE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,eAAe,EAAE,OAAO,EAAE;QAErE,OAAO,iBAAiB,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;gBAC9D,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,OAAO,UAAU,EAAE;gBAC9C,MAAM;gBACN,iBAAiB,OAAO,kBAAkB;gBAC1C,SAAS,CAAC,uCAAuC,EAAE,OAAO,cAAc,CAAC,cAAc,EAAE,OAAO,eAAe,CAAC,EAAE,CAAC;gBACnH,YAAY,OAAO,cAAc;gBACjC,YAAY,OAAO,UAAU;gBAC7B,UAAU,OAAO,QAAQ;YAC3B,CAAC;IACH,GAAG;QAAC;KAAiB;IAErB,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,QAAQ,OAAO;QACzC,IAAI,UAAU,SAAS;QAEvB,QAAQ,OAAO,GAAG;YACd,MAAM;mBAAI;gBAAM;aAAQ;YACxB,SAAS;YACT,QAAQ,EAAE;QACd;QACA,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;QACzC,WAAW,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;IAC7C,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;YAC1B,iBAAiB,OAAO,GAAG;YAC3B;QACJ;QACA,cAAc;IAChB,GAAG;QAAC;QAAgC;KAAc;IAElD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;QACjD,IAAI,KAAK,MAAM,KAAK,GAAG;QACvB,iBAAiB,OAAO,GAAG;QAC3B,MAAM,aAAa,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACxC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;QAC5C,QAAQ,OAAO,GAAG;YACd,MAAM;YACN,SAAS;YACT,QAAQ;gBAAC;mBAAY;aAAO;QAChC;QACA,eAAe;QACf,WAAW,QAAQ,MAAM,GAAG;QAC5B,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;QACjD,IAAI,OAAO,MAAM,KAAK,GAAG;QACzB,iBAAiB,OAAO,GAAG;QAC3B,MAAM,aAAa,MAAM,CAAC,EAAE;QAC5B,MAAM,YAAY,OAAO,KAAK,CAAC;QAC/B,QAAQ,OAAO,GAAG;YACd,MAAM;mBAAI;gBAAM;aAAQ;YACxB,SAAS;YACT,QAAQ;QACZ;QACA,eAAe;QACf,WAAW;QACX,WAAW,UAAU,MAAM,GAAG;IAClC,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,eAAuB;QACpE,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,uBAAuB,EAAE;YACzB,mBAAmB;YACnB;QACF;QACA,mBAAmB;QACnB,IAAI;YACF,MAAM,gBAAmC;gBACvC,MAAM;gBACN,UAAU;YACZ;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE;YACjC,MAAM,gBAAgB,OAAO,WAAW,IAAI,EAAE;YAE9C,sGAAsG;YACtG,MAAM,uBAAuB,SAAS,kCAAkC,GACpE,CAAA,GAAA,qJAAA,CAAA,kCAA+B,AAAD,EAAE,eAAe,QAC/C,EAAE;YAEN,6DAA6D;YAC7D,MAAM,SAAS;mBAAI;aAAc;YACjC,KAAK,MAAM,MAAM,qBAAsB;gBACrC,MAAM,WAAW,OAAO,IAAI,CAAC,CAAA;oBAC3B,IAAI,GAAG,IAAI,KAAK,WAAW,GAAG,UAAU,IAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,GAAG,UAAU,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,OAAO;oBAChI,OAAO,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,UAAU,IAAI,KAAK,GAAG,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ;gBACnF;gBACA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC;YAC7B;YAEA,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;YACtG,uBAAuB,EAAE;QAC3B,SAAU;YACR,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB,iCAAiC;IACvD,GAAG;QAAC;QAAiC;QAAwB;KAAoB;IAEjF,2BAA2B;IAC3B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjD,IAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI;QAElE,IAAI;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE;YAAK;YAC3C,MAAM,mBAAmB,OAAO,YAAY;YAC5C,MAAM,kBAAkB;YAExB,IAAI,oBAAoB,qBAAqB,aAAa,qBAAqB,iBAAiB;gBAC5F,MAAM,WAAW,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;gBAC7D,IAAI,UAAU;oBACV,MAAM,aAAa,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,IAC7D,SAAS,QAAQ,CAAC,EAAE,CAAC,KAAK,GAC1B,SAAS,KAAK;oBAEpB,0BAA0B;oBAE1B,MAAM;wBACF,UAAU;wBACV,gBAAgB;wBAChB,mBAAmB;4BAAE,UAAU,EAAE,SAAS,QAAQ;wBAAE;oBACxD;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wCAAwC;QAC1D;IACF,GAAG;QAAC;QAAwB;QAA2B;QAAO;KAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;QACxB,MAAM,WAAW,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC7D,IAAI,UAAU;YACV,oBAAoB,SAAS,GAAG,IAAI;QACxC;QAEA,IAAI,SAAS,gCAAgC,EAAE;YAC7C,wBAAwB;QAC1B;IACF,GAAG;QAAC;QAAsC,SAAS,gCAAgC;QAAE;QAAwB;KAAwB;IAErI,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC;QACrD,MAAM,mBAAmB,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,MAAM,MAAM;QACjF,aAAa;QACb,aAAa,YAAY,MAAM;QAC/B,iCAAiC;QACjC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,MAAO,oBAAoB,MAAM,GAAG,IAAM,sBAAsB,MAAM,GAAG;QACvG,MAAM,cAAc,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,mBAAmB;QAC/D,gBAAgB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY;IACnE,GAAG;QAAC;QAAa;QAAqB;KAAsB;IAE5D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,gBAAwB,iBAAyB,YAAqB;QAC/G,IAAI,WAAW;QACf,IAAI,UAAU;QACd,IAAI,OAAO,eAAe,YAAY,OAAO,aAAa,YAAY,YAAY,YAAY;YAC1F,MAAM,aAAa,YAAY,SAAS,CAAC,GAAG;YAC5C,MAAM,YAAY,YAAY,SAAS,CAAC;YACxC,IAAI,YAAY,SAAS,CAAC,YAAY,cAAc,iBAAiB;gBACjE,WAAW,aAAa,iBAAiB;gBACzC,UAAU;YACd;QACJ;QACA,IAAI,CAAC,SAAS;YACV,4CAA4C;YAC5C,MAAM,uBAAuB,YAAY,OAAO,CAAC;YACjD,IAAI,yBAAyB,CAAC,GAAG;gBAC7B,WAAW,YAAY,SAAS,CAAC,GAAG,wBAAwB,iBAAiB,YAAY,SAAS,CAAC,uBAAuB,gBAAgB,MAAM;gBAChJ,UAAU;YACd;QACJ;QACA,IAAI,SAAS;YACT,eAAe,WAAW,qDAAqD;YAC/E,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAgC;QAC3F,OAAO;YACH,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA6B,SAAS;YAAc;QAC7G;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,uBAAuB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,MAAM;YAAE,UAAU;YAAkB,gBAAgB;QAA2B;IACjF,GAAG;QAAC;KAAM;IAEV,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,MAAM,UAAU,YAAY,IAAI,OAAO,KAAK,eAAe,GAAG,YAAY,IAAI,EAAE,cAAc;QAC9F,eAAe;IACjB,GAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;IACjB,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAW,CAAC,gDAAgD,EAAE,QAAQ,kBAAkB,IAAI;;8BAC/F,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0JAAA,CAAA,qBAAkB;wBACjB,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,mBAAmB;wBACnB,mBAAmB;wBACnB,qBAAqB;wBACrB,QAAQ;wBACR,QAAQ;wBACR,SAAS;wBACT,SAAS;;;;;;;;;;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,+JAAA,CAAA,0BAAuB;4BACtB,aAAa;4BACb,aAAa;4BACb,mBAAmB;4BACnB,qBAAqB;;;;;;sCAEvB,8OAAC,oJAAA,CAAA,oBAAiB;4BAChB,WAAW;4BACX,WAAW;4BACX,cAAc;;;;;;sCAIhB,8OAAC,oJAAA,CAAA,mBAAgB;4BAAC,iBAAiB;4BAAgB,WAAU;;;;;;sCAG7D,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAY,WAAU;;8CACvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAW,CAAC,wFAAwF,EAAE,QAAQ,eAAe,IAAI;;0DACzI,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;0DAEhC,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;0DAEhC,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;4CAE/B,SAAS,yBAAyB,kBACjC,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;0DAGlC,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;0DAEhC,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDACjB,cAAA,8OAAC,0IAAA,CAAA,aAAU;gDAAC,aAAa;gDAAa,gBAAgB;gDAAoB,aAAa;gDAAa,WAAW;;;;;;;;;;;sDAEjH,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDACjB,cAAA,8OAAC,mJAAA,CAAA,kBAAe;gDAAC,cAAc;;;;;;;;;;;sDAEjC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAChB,SAAS,mBAAmB,kBAAI,8OAAC,kJAAA,CAAA,iBAAc;gDAAC,aAAa;;;;;;;;;;;sDAEhE,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAChB,SAAS,yBAAyB,kBAAI,8OAAC,kJAAA,CAAA,qBAAkB;gDAAC,aAAa;gDAAa,UAAU;;;;;;;;;;;sDAEjG,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDACjB,cAAA,8OAAC,qJAAA,CAAA,oBAAiB;gDAAC,aAAa;;;;;;;;;;;sDAElC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDACjB,cAAA,8OAAC,kJAAA,CAAA,iBAAc;gDAAC,aAAa;gDAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E;;AAKe,SAAS;IACtB,iDAAiD;IACjD,qBAAO,8OAAC,4HAAA,CAAA,UAAa;;;;;AACvB", "debugId": null}}]}