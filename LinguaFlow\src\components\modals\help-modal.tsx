'use client';

import React from 'react';
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useI18n } from '@/contexts/i18n-context';

interface HelpModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function HelpModal({ open, onOpenChange }: HelpModalProps) {
  const { t } = useI18n();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>{t('helpTitle')}</DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('helpEditorTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpEditorDescription') }} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('helpAiToolsTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpAiToolsDescription') }} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('helpLanguageSettingsTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpLanguageSettingsDescription') }} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('helpAppearanceSettingsTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpAppearanceSettingsDescription') }} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('helpFeatureSettingsTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpFeatureSettingsDescription') }} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('helpAdvancedSettingsTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div dangerouslySetInnerHTML={{ __html: t('helpAdvancedSettingsDescription') }} />
              </CardContent>
            </Card>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                💡 {t('helpPanelTip')}
              </p>
            </div>
          </div>
        </ScrollArea>

        <div className="flex justify-end pt-4">
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
