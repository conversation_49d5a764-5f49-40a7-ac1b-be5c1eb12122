{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,qKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,qKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,qKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,qKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,qKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/sidebar.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { ChevronsLeft, ChevronsRight, Menu } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  <PERSON><PERSON><PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"icon\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile, toggleSidebar } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden md:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n            onDoubleClick={toggleSidebar}\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <Menu />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarCollapse = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar, state } = useSidebar();\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"collapse\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event);\n        toggleSidebar();\n      }}\n      {...props}\n    >\n      {state === 'expanded' ? <ChevronsLeft /> : <ChevronsRight />}\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  );\n});\nSidebarCollapse.displayName = \"SidebarCollapse\";\n\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex items-center justify-between gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"mt-auto flex flex-col\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      onClick,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        onClick={(e) => {\n          e.preventDefault();\n          onClick?.(e);\n        }}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarCollapse,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WACH;8DAAc,CAAC,OAAS,CAAC;+DACzB;8DAAQ,CAAC,OAAS,CAAC;;QACzB;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;;QArFmB,iIAAA,CAAA,cAAW;;;;QAAX,iIAAA,CAAA,cAAW;;;;AAuFhC,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,MAAM,EACpB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG;IAEtE,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,6LAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,WAAU;oBACV,eAAe;8BAEd;;;;;;;;;;;;;;;;;AAKX;;QAjFwE;;;;QAAA;;;;AAmF1E,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,qMAAA,CAAA,OAAI;;;;;0BACL,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;;QAnB4B;;;;QAAA;;;;AAoB5B,eAAe,WAAW,GAAG;AAE7B,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGrC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;;IACnC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG;IACjC,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;YAER,UAAU,2BAAa,6LAAC,yNAAA,CAAA,eAAY;;;;qCAAM,6LAAC,2NAAA,CAAA,gBAAa;;;;;0BACzD,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;;QAlBmC;;;;QAAA;;;;AAmBnC,gBAAgB,WAAW,GAAG;AAG9B,MAAM,4BAAc,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;;QAtB4B;;;;QAAA;;;;AAuB5B,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;;;;;AAGf;;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,OAAO,EACP,GAAG,OACJ,EACD;;IAEA,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC5D,SAAS,CAAC;YACR,EAAE,cAAc;YAChB,UAAU;QACZ;QACC,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;;QAtC8B;;;;QAAA;;;;AAwChC,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6LAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/icons/logo.tsx"], "sourcesContent": ["\"use client\";\nimport type { SVGProps } from 'react';\nimport { useId } from 'react';\n\ninterface LogoProps extends SVGProps<SVGSVGElement> {\n  size?: number | string;\n  title?: string;\n  decorative?: boolean;\n}\n\n// LinguaFlow brand logo component generated from public/LinguaFlow-logo.svg\nexport function Logo({\n  size = 32,\n  title = 'LinguaFlow',\n  decorative = false,\n  ...props\n}: LogoProps) {\n  // Ensure unique gradient IDs when multiple instances are rendered\n  const reactId = useId();\n  const gradientId = `lf-logo-gradient-${reactId.replace(/:/g, '')}`;\n\n  const ariaProps = decorative && !title\n    ? { 'aria-hidden': true }\n    : { role: 'img', 'aria-label': title } as const;\n\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 100 100\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      focusable=\"false\"\n      {...ariaProps}\n      {...props}\n    >\n      {title ? <title>{title}</title> : null}\n      <defs>\n        <linearGradient id={gradientId} gradientUnits=\"userSpaceOnUse\" x1=\"228.26253\" y1=\"474.16306\" x2=\"253.2735\" y2=\"46.282173\">\n          <stop offset=\"0\" stopColor=\"#3485C0\" />\n          <stop offset=\"1\" stopColor=\"#5BBCEB\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"#FFFFFF\" />\n\n      <path fill={`url(#${gradientId})`} transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M51.176 364.081C33.2039 330.028 23.9599 296.118 24.1262 257.566C24.1931 238.367 26.8956 219.267 32.1579 200.802C48.4007 146.151 85.496 100.086 135.427 72.5623C174.083 51.3524 218.238 42.2968 262.122 46.579C268.282 47.1509 274.342 48.11 280.439 49.1269C283.542 49.6445 286.737 50.03 289.756 50.9172L290.028 50.9416C291.361 51.0761 292.551 51.1576 293.588 52.0723C294.923 54.6511 293.757 59.1065 293.389 61.9008C291.504 76.2266 288.172 90.0415 283.791 103.804C280.839 113.078 277.423 122.685 273.264 131.495C270.282 137.811 266.611 143.876 263.804 150.253C268.864 144.594 273.534 138.868 279.377 133.953C296.189 119.808 319.807 114.493 341.251 113.32C347.826 112.96 354.499 113.18 361.085 113.183C375.993 113.189 390.937 113.417 405.838 113.03C416.792 112.744 427.658 112.297 438.365 109.745C445.403 108.067 451.949 105.278 458.616 102.541C461.909 104.38 464.236 107.071 466.245 110.241C466.395 125.668 453.929 145.562 443.434 156.298C439.819 159.996 435.676 163.189 431.395 166.076C414.561 177.431 398.453 179.513 378.581 179.645C371.724 179.691 364.86 179.492 358.002 179.447C343.98 179.355 332.014 180.14 321.546 190.752C314.912 197.477 313.415 205.98 311.127 214.777C311.563 215.001 321.647 214.53 323.36 214.535C343.265 214.713 363.17 214.713 383.075 214.535C382.093 230.526 376.191 245.933 363.945 256.688C349.401 269.463 329.18 271.579 310.573 270.759C311.346 276.332 311.067 282.125 311.062 287.744C311.055 294.512 311.028 301.174 310.066 307.89C308.201 320.904 302.079 334.292 294.18 344.771C283.989 358.291 269.536 367.97 254.678 375.722C250.886 377.7 246.926 379.88 242.815 381.09C252.207 381.117 261.5 382.126 270.875 382.467C282.748 382.785 294.625 382.871 306.501 382.725C319.681 382.626 332.913 382.445 345.992 380.655C375.298 376.643 403.507 363.776 424.601 342.827C434.41 333.102 442.802 322.043 449.527 309.978C452.006 305.489 453.491 300.556 456.378 296.285C454.582 317.355 445.431 338.977 435.906 357.61C430.781 367.637 425.48 377.948 416.738 385.341C406.876 393.681 395.651 399.462 382.957 402.021C370.357 404.561 357.353 403.473 344.584 403.48L288.702 403.493L235.779 403.539L219.158 403.56C215.881 403.562 212.512 403.717 209.25 403.432C208.66 403.381 208.075 403.296 207.495 403.178C201.87 415.074 185.422 428.245 174.339 435.311C170.507 437.754 166.494 439.854 162.473 441.96C179.818 441.189 197.256 441.355 214.613 441.475L255.279 441.568L322.011 441.609C333.594 441.616 345.244 441.37 356.819 441.67C348.94 446.839 340.733 451.124 332.208 455.1C289.986 474.721 242.527 480.069 196.997 470.338C176.63 466.116 156.746 458.311 138.48 448.447C135.274 446.716 131.257 445.026 128.729 442.398C125.671 439.838 122.297 437.955 119.024 435.71C112.83 434.737 100.502 423.229 95.8622 418.823C93.4331 416.516 90.8907 413.478 88.0738 411.69L88.1191 411.507C86.9167 409.519 84.4893 407.599 82.8344 405.924C82.5902 406.651 82.917 407.325 82.6435 407.659L81.7785 407.738L81.5892 408.032C82.0739 409.208 83.9708 412.202 85.1482 412.654C85.2151 412.679 85.2879 412.685 85.3578 412.701C85.7692 412.352 86.1124 411.999 86.4632 411.59C86.9501 411.693 87.2218 411.886 87.4857 412.34C87.7752 412.838 87.6724 413.463 87.4269 413.953C87.1053 414.596 86.5988 415.116 85.9844 415.475C85.5255 415.505 85.0782 415.532 84.6181 415.528C84.5381 415.46 84.4573 415.392 84.3781 415.323C79.9609 411.445 77.1017 406.21 73.5107 401.651C67.3013 393.768 50.5644 376.563 50.1845 366.975C50.1339 365.699 50.3025 365.023 51.176 364.081Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M289.756 50.9172L290.028 50.9416C289.259 57.1266 287.266 62.9096 285.285 68.7903C279.759 85.1666 272.908 101.065 264.799 116.328C262.151 121.42 259.621 128.003 254.956 131.603C245.652 138.783 227.823 145.484 216.49 149.514C213.191 150.687 209.777 151.648 206.524 152.927C221.223 153.396 236.544 150.1 249.913 144.094L202.567 203.74C199.337 207.784 196.168 211.877 193.063 216.019C188.092 222.65 183.543 229.161 176.97 234.364C163.662 244.898 148.173 253.522 132.823 260.676C126.951 263.412 120.86 265.703 115.046 268.558C136.132 265.058 150.644 263.197 168.879 250.929C164.431 257.858 160.719 265.226 156.179 272.09C145.732 287.884 130.449 299.118 113.426 306.968C110.662 308.272 107.838 309.443 104.963 310.479C102.447 311.38 99.8021 312.14 97.3704 313.238C113.353 312.082 122.664 310.69 137.573 304.525C128.821 311.259 119.181 316.752 108.926 320.848C99.2487 324.81 89.426 328.022 80.0306 332.689C85.3872 311.764 91.3078 291.172 99.8823 271.289C123.724 216.006 163.554 168.023 207.744 127.715C212.927 122.987 218.003 117.991 223.382 113.492L222.96 112.753C200.889 126.248 180.06 141.676 160.719 158.855C119.524 195.338 87.1925 238.862 66.9172 290.207C63.496 298.87 58.1623 311.173 57.6073 320.318C55.6107 315.012 52.6968 309.976 50.392 304.786C44.4753 291.461 39.8733 277.503 37.8603 263.028C40.0414 266.601 41.0377 270.995 42.7085 274.838C46.4576 283.46 52.0541 290.809 58.1245 297.904C49.0177 267.242 48.1239 249.522 55.6109 218.463C56.4491 229.285 57.5776 239.35 61.9223 249.45C63.0333 252.033 64.2775 254.701 65.9249 256.991C60.031 238.735 61.6644 213.965 69.7227 196.623C70.2245 198.013 69.9002 203.126 69.9462 204.882C70.0574 209.124 71.4483 223.142 73.5628 226.364C74.0766 223.684 73.9212 220.654 74.1255 217.914C74.5509 212.209 75.3301 206.539 76.7474 200.99C82.2319 179.517 95.0083 163.05 110.272 147.474C116.792 140.727 123.633 134.298 130.773 128.21C137.278 122.641 144.256 117.495 150.539 111.688C147.518 123.91 148.006 140.933 150.483 153.21C152.037 148.192 152.158 142.302 152.954 137.089C154.751 125.329 158.21 107.968 167.025 99.5818C171.715 95.1198 177.398 91.9439 182.588 88.1279C178.323 97.1155 173.986 106.127 171.616 115.836C170.404 120.797 169.722 125.898 168.991 130.947C172.553 124.616 175.006 117.729 178.526 111.362C183.977 101.499 191.709 89.2841 200.879 82.6601C205.557 79.2812 211.261 77.2579 216.569 75.0831C226.181 71.1449 236.078 67.6417 245.51 63.3018C238.335 70.4083 232.903 78.0964 229.03 87.4629C241.532 73.3625 253.335 65.2729 270.906 58.3559C274.74 56.8465 278.699 55.696 282.526 54.2109C284.99 53.2546 287.313 51.8292 289.756 50.9172Z\"/>\n      <path fill=\"#4EA8D8\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M222.96 112.753C224.558 111.5 226.309 110.56 228.153 109.72C226.565 110.979 224.988 112.257 223.382 113.492L222.96 112.753Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M205.821 225.402C205.372 240.368 205.746 255.411 205.736 270.39L205.526 346.42C205.497 351.135 205.526 355.85 205.613 360.564C205.76 368.287 206.646 384.231 204.487 391.107C202.336 397.957 192.012 407.784 186.707 412.776C171.807 426.793 158.556 435.53 138.453 440.524C135.23 441.325 131.971 441.737 128.729 442.398C125.671 439.838 122.297 437.955 119.024 435.71C119.115 435.686 119.206 435.66 119.298 435.636C122.793 434.729 126.472 434.593 129.998 433.784C140.96 431.27 150.019 424.13 155.75 414.534C158.947 409.156 161.105 403.226 162.113 397.051C164.404 383.314 163.287 368.002 163.171 354.102L163.186 306.302C163.155 300.345 161.964 288.834 163.765 283.671C165.489 278.726 169.983 273.083 172.977 268.75C183.198 253.96 193.956 238.907 205.821 225.402Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M182.69 145.534L183.109 146.291C179.22 150.014 175.179 153.633 171.411 157.477C117.28 212.684 79.2571 279.278 69.3628 356.824C68.1239 366.534 66.7236 376.326 66.4861 386.124C71.5567 393.028 77.0149 399.638 82.8344 405.924C82.5902 406.651 82.917 407.325 82.6435 407.659L81.7785 407.738L81.5892 408.032C82.0739 409.208 83.9708 412.202 85.1482 412.654C85.2151 412.679 85.2879 412.685 85.3578 412.701C85.7692 412.352 86.1124 411.999 86.4632 411.59C86.9501 411.693 87.2218 411.886 87.4857 412.34C87.7752 412.838 87.6724 413.463 87.4269 413.953C87.1053 414.596 86.5988 415.116 85.9844 415.475C85.5255 415.505 85.0782 415.532 84.6181 415.528C84.5381 415.46 84.4573 415.392 84.3781 415.323C79.9609 411.445 77.1017 406.21 73.5107 401.651C67.3013 393.768 50.5644 376.563 50.1845 366.975C50.1339 365.699 50.3025 365.023 51.176 364.081C52.3821 365.102 53.4113 366.336 54.4741 367.503C54.4819 365.811 54.661 364.176 54.9157 362.505C56.0667 354.953 57.7857 347.406 59.4327 339.947C61.487 330.245 63.9702 320.64 66.875 311.158C84.3157 255.113 117.34 205.679 160.298 165.933C164.244 162.312 168.171 158.671 172.078 155.009C175.513 151.769 178.883 148.339 182.69 145.534Z\"/>\n      <path fill=\"#D8F6FE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M182.69 145.534C183.516 144.762 184.487 144.24 185.455 143.668C184.695 144.561 183.957 145.481 183.109 146.291L182.69 145.534Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M447.576 122.082L447.933 122.245C447.943 122.323 447.962 122.4 447.964 122.479C447.992 123.482 447.354 124.889 446.974 125.827C443.934 133.337 438.465 140.936 432.783 146.629C410.823 168.629 386.3 165.139 358.328 165.177C341.352 165.201 324.158 167.556 311.612 180.215C298.964 192.977 296.944 207.057 297.016 224.251C321.839 224.244 346.708 224.743 371.523 224.23C369.17 232.977 365.543 241.044 359.042 247.504C347.979 258.498 335.262 260.245 320.378 260.83C312.498 261.14 304.593 261.03 296.707 261.028C296.877 280.904 298.281 301.484 291.257 320.429C286.191 332.759 277.818 343.449 267.062 351.322C263.135 354.151 258.832 356.508 254.66 358.958C258.518 345.004 257.502 329.851 257.502 315.531L257.586 246.608C257.533 229.175 256.204 211.452 259.223 194.19C260.881 184.741 264.28 175.683 269.247 167.476C283.632 144.02 308.07 130.549 335.114 127.815C349.639 126.347 366.122 127.347 380.875 127.467C396.2 127.592 411.767 127.875 426.999 125.998C433.964 125.14 440.761 123.734 447.576 122.082Z\"/>\n      <path fill=\"#4EA8D8\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M268.431 271.343C267.611 262.633 267.751 253.819 267.529 245.077C267.287 235.573 266.759 226.079 266.724 216.571C266.644 194.603 272.133 174.968 288.128 158.997C304.66 142.492 327.827 137.167 350.461 137.237L350.768 138.053C337.076 139.768 323.46 143.003 311.327 149.769C303.549 154.203 296.599 159.952 290.785 166.76C275.557 184.577 272.748 202.824 271.282 225.196L269.86 248.739C269.503 255.395 270.317 264.889 268.522 271.044C268.493 271.144 268.461 271.244 268.431 271.343Z\"/>\n      <path fill=\"#83A9C0\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M350.461 137.237C351.282 137.241 352.417 137.1 353.151 137.493C352.398 137.863 351.588 137.931 350.768 138.053L350.461 137.237Z\"/>\n      <path fillOpacity=\"0.89803922\" fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M82.8344 405.924C84.4893 407.599 86.9167 409.519 88.1191 411.507L88.0738 411.69C87.9639 413.239 87.9654 414.724 86.8582 415.934C86.296 416.142 85.9657 416.321 85.366 416.052C85.1523 415.956 84.8106 415.677 84.6181 415.528C85.0782 415.532 85.5255 415.505 85.9844 415.475C86.5988 415.116 87.1053 414.596 87.4269 413.953C87.6724 413.463 87.7752 412.838 87.4857 412.34C87.2218 411.886 86.9501 411.693 86.4632 411.59C86.1124 411.999 85.7692 412.352 85.3578 412.701C85.2879 412.685 85.2151 412.679 85.1482 412.654C83.9708 412.202 82.0739 409.208 81.5892 408.032L81.7785 407.738L82.6435 407.659C82.917 407.325 82.5902 406.651 82.8344 405.924Z\"/>\n    </svg>\n  );\n}\n\nexport function FaviconLogo(props: SVGProps<SVGSVGElement>) {\n  return (\n    <Logo\n      {...props}\n      size={16}\n      decorative\n      className=\"favicon-logo\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWO,SAAS,KAAK,EACnB,OAAO,EAAE,EACT,QAAQ,YAAY,EACpB,aAAa,KAAK,EAClB,GAAG,OACO;;IACV,kEAAkE;IAClE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACpB,MAAM,aAAa,CAAC,iBAAiB,EAAE,QAAQ,OAAO,CAAC,MAAM,KAAK;IAElE,MAAM,YAAY,cAAc,CAAC,QAC7B;QAAE,eAAe;IAAK,IACtB;QAAE,MAAM;QAAO,cAAc;IAAM;IAEvC,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,OAAM;QACN,WAAU;QACT,GAAG,SAAS;QACZ,GAAG,KAAK;;YAER,sBAAQ,6LAAC;0BAAO;;;;;uBAAiB;0BAClC,6LAAC;0BACC,cAAA,6LAAC;oBAAe,IAAI;oBAAY,eAAc;oBAAiB,IAAG;oBAAY,IAAG;oBAAY,IAAG;oBAAW,IAAG;;sCAC5G,6LAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;sCAC3B,6LAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAG/B,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BAEpC,6LAAC;gBAAK,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;gBAAE,WAAU;gBAA6C,GAAE;;;;;;0BAC5F,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,6LAAC;gBAAK,aAAY;gBAAa,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;;;;;;;AAG7G;GA7CgB;;QAOE,6JAAA,CAAA,QAAK;;;KAPP;AA+CT,SAAS,YAAY,KAA8B;IACxD,qBACE,6LAAC;QACE,GAAG,KAAK;QACT,MAAM;QACN,UAAU;QACV,WAAU;;;;;;AAGhB;MATgB", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/language-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Languages, Globe, MapPin } from \"lucide-react\"; \nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { APP_SUPPORTED_UI_LANGUAGES, APP_WRITING_LANGUAGES } from \"@/config/languages\";\nimport { useMemo } from \"react\";\n\nexport function LanguageSettings() {\n  const { \n    t, \n    uiLanguage, \n    setUiLanguage,\n    writingLanguageDialect,\n    setWritingLanguageDialect,\n    getWritingLanguageBase,\n  } = useI18n();\n\n  const currentWritingBaseLang = getWritingLanguageBase();\n\n  const handleWritingLanguageChange = (newBaseLang: string) => {\n    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === newBaseLang);\n    if (langInfo) {\n      // If it has dialects, pick the first one. Otherwise, use the base lang itself (e.g. for Arabic)\n      const newDialect = langInfo.dialects && langInfo.dialects.length > 0 ? langInfo.dialects[0].value : newBaseLang;\n      setWritingLanguageDialect(newDialect);\n    }\n  };\n\n  const handleDialectChange = (newDialect: string) => {\n    setWritingLanguageDialect(newDialect);\n  };\n\n  const currentWritingLanguageInfo = useMemo(() => {\n    return APP_WRITING_LANGUAGES.find(lang => lang.value === currentWritingBaseLang);\n  }, [currentWritingBaseLang]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <Label htmlFor=\"ui-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Globe className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('uiLanguageLabel')}\n        </Label>\n        <Select value={uiLanguage} onValueChange={setUiLanguage}>\n          <SelectTrigger id=\"ui-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectUiLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('uiLanguageDescription')}</p>\n      </div>\n\n      <div>\n        <Label htmlFor=\"writing-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Languages className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('writingLanguageLabel')}\n        </Label>\n        <Select value={currentWritingBaseLang} onValueChange={handleWritingLanguageChange}>\n          <SelectTrigger id=\"writing-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectWritingLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_WRITING_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('writingLanguageDescription')}</p>\n      </div>\n\n      {currentWritingLanguageInfo?.dialects && currentWritingLanguageInfo.dialects.length > 0 && (\n        <div>\n          <Label htmlFor=\"regional-dialect-select\" className=\"flex items-center text-sm font-medium mb-1\">\n            <MapPin className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('regionalDialectLabel')}\n          </Label>\n          <Select value={writingLanguageDialect} onValueChange={handleDialectChange}>\n            <SelectTrigger id=\"regional-dialect-select\" className=\"w-full\">\n              <SelectValue placeholder={t('selectRegionalDialectPlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n              {currentWritingLanguageInfo.dialects.map((dialect) => (\n                <SelectItem key={dialect.value} value={dialect.value}>\n                  {t(dialect.labelKey)}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('regionalDialectDescription')}</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,EACJ,CAAC,EACD,UAAU,EACV,aAAa,EACb,sBAAsB,EACtB,yBAAyB,EACzB,sBAAsB,EACvB,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEV,MAAM,yBAAyB;IAE/B,MAAM,8BAA8B,CAAC;QACnC,MAAM,WAAW,6HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC7D,IAAI,UAAU;YACZ,gGAAgG;YAChG,MAAM,aAAa,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,IAAI,SAAS,QAAQ,CAAC,EAAE,CAAC,KAAK,GAAG;YACpG,0BAA0B;QAC5B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,0BAA0B;IAC5B;IAEA,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gEAAE;YACzC,OAAO,6HAAA,CAAA,wBAAqB,CAAC,IAAI;wEAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;;QAC3D;+DAAG;QAAC;KAAuB;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAqB,WAAU;;0CAC5C,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAY,eAAe;;0CACxC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAqB,WAAU;0CAC/C,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,6HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,6LAAC,qIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMhC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA0B,WAAU;;0CACjD,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAwB,eAAe;;0CACpD,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA0B,WAAU;0CACpD,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,6HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC,qIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMjC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;YAG3D,4BAA4B,YAAY,2BAA2B,QAAQ,CAAC,MAAM,GAAG,mBACpF,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA0B,WAAU;;0CACjD,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAwB,eAAe;;0CACpD,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA0B,WAAU;0CACpD,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,2BAA2B,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACxC,6LAAC,qIAAA,CAAA,aAAU;wCAAqB,OAAO,QAAQ,KAAK;kDACjD,EAAE,QAAQ,QAAQ;uCADJ,QAAQ,KAAK;;;;;;;;;;;;;;;;kCAMpC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;;;;;;;AAKpE;GA9FgB;;QAQV,sIAAA,CAAA,UAAO;;;KARG", "debugId": null}}, {"offset": {"line": 2011, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/appearance-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Pa<PERSON>, <PERSON>, Sun, Type, Contrast } from \"lucide-react\"; // Changed FontSize to Type\nimport { useAppearance, type FontSizeSetting, type ThemeSetting } from \"@/contexts/theme-context\"; // Use new name\nimport { useI18n } from \"@/contexts/i18n-context\";\n\nconst themeOptions: Array<{value: ThemeSetting, labelKey: string, icon?: JSX.Element}> = [\n    { value: \"light\", labelKey: \"themeLight\", icon: <Sun className=\"mr-2 h-4 w-4\" /> },\n    { value: \"dark\", labelKey: \"themeDark\", icon: <Moon className=\"mr-2 h-4 w-4\" /> },\n    { value: \"system\", labelKey: \"themeSystem\", icon: <Palette className=\"mr-2 h-4 w-4\" /> },\n];\n\nconst fontSizeOptions: Array<{value: FontSizeSetting, labelKey: string}> = [\n    { value: \"small\", labelKey: \"fontSizeSmall\" },\n    { value: \"medium\", labelKey: \"fontSizeMedium\" },\n    { value: \"large\", labelKey: \"fontSizeLarge\" },\n];\n\nexport function AppearanceSettings() {\n  const { t } = useI18n();\n  const { \n    theme, \n    setTheme, \n    fontSize, \n    setFontSize, \n    isHighContrast, \n    toggleHighContrast,\n    effectiveTheme \n  } = useAppearance();\n  \n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <Label htmlFor=\"theme-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Palette className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('themeLabel')}\n        </Label>\n        <Select value={theme} onValueChange={(value) => setTheme(value as ThemeSetting)}>\n            <SelectTrigger id=\"theme-select\" className=\"w-full\">\n                <SelectValue placeholder={t('selectThemePlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n                {themeOptions.map(opt => (\n                    <SelectItem key={opt.value} value={opt.value}>\n                        <div className=\"flex items-center\">\n                            {opt.icon}\n                            {t(opt.labelKey)}\n                        </div>\n                    </SelectItem>\n                ))}\n            </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('themeDescription')}</p>\n      </div>\n\n      <div>\n        <Label htmlFor=\"font-size-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Type className=\"mr-2 h-4 w-4 text-primary\" /> \n          {t('fontSizeLabel')}\n        </Label>\n        <Select value={fontSize} onValueChange={(value) => setFontSize(value as FontSizeSetting)}>\n            <SelectTrigger id=\"font-size-select\" className=\"w-full\">\n                <SelectValue placeholder={t('selectFontSizePlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n                {fontSizeOptions.map(opt => (\n                    <SelectItem key={opt.value} value={opt.value}>\n                        {t(opt.labelKey)}\n                    </SelectItem>\n                ))}\n            </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('fontSizeDescription')}</p>\n      </div>\n      \n      <div>\n        <Label htmlFor=\"high-contrast-switch\" className=\"flex items-center text-sm font-medium mb-1\">\n            <Contrast className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('highContrastModeLabel')}\n        </Label>\n        <div className=\"flex items-center space-x-2 pl-1\">\n            <Switch\n                id=\"high-contrast-switch\"\n                checked={isHighContrast}\n                onCheckedChange={toggleHighContrast}\n                aria-label={t('highContrastModeLabel')}\n            />\n            <span className=\"text-sm text-muted-foreground\">\n                {isHighContrast ? t('enabledLabel') : t('disabledLabel')}\n            </span>\n        </div>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('highContrastModeDescription')}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA,kWAAmE,2BAA2B;AAA9F;AAAA;AAAA;AAAA;AACA,6OAAmG,eAAe;AAClH;;;AARA;;;;;;;AAUA,MAAM,eAAmF;IACrF;QAAE,OAAO;QAAS,UAAU;QAAc,oBAAM,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAAkB;IACjF;QAAE,OAAO;QAAQ,UAAU;QAAa,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAkB;IAChF;QAAE,OAAO;QAAU,UAAU;QAAe,oBAAM,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAAkB;CAC1F;AAED,MAAM,kBAAqE;IACvE;QAAE,OAAO;QAAS,UAAU;IAAgB;IAC5C;QAAE,OAAO;QAAU,UAAU;IAAiB;IAC9C;QAAE,OAAO;QAAS,UAAU;IAAgB;CAC/C;AAEM,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,cAAc,EACf,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAEhB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;;0CACtC,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClB,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,eAAe,CAAC,QAAU,SAAS;;0CACrD,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAe,WAAU;0CACvC,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAEhC,6LAAC,qIAAA,CAAA,gBAAa;0CACT,aAAa,GAAG,CAAC,CAAA,oBACd,6LAAC,qIAAA,CAAA,aAAU;wCAAiB,OAAO,IAAI,KAAK;kDACxC,cAAA,6LAAC;4CAAI,WAAU;;gDACV,IAAI,IAAI;gDACR,EAAE,IAAI,QAAQ;;;;;;;uCAHN,IAAI,KAAK;;;;;;;;;;;;;;;;kCAStC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG5D,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAmB,WAAU;;0CAC1C,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe,CAAC,QAAU,YAAY;;0CAC3D,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAmB,WAAU;0CAC3C,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAEhC,6LAAC,qIAAA,CAAA,gBAAa;0CACT,gBAAgB,GAAG,CAAC,CAAA,oBACjB,6LAAC,qIAAA,CAAA,aAAU;wCAAiB,OAAO,IAAI,KAAK;kDACvC,EAAE,IAAI,QAAQ;uCADF,IAAI,KAAK;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG5D,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAuB,WAAU;;0CAC5C,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,qIAAA,CAAA,SAAM;gCACH,IAAG;gCACH,SAAS;gCACT,iBAAiB;gCACjB,cAAY,EAAE;;;;;;0CAElB,6LAAC;gCAAK,WAAU;0CACX,iBAAiB,EAAE,kBAAkB,EAAE;;;;;;;;;;;;kCAGhD,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;;;;;;;AAIlE;GA7EgB;;QACA,sIAAA,CAAA,UAAO;QASjB,uIAAA,CAAA,gBAAa;;;KAVH", "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2604, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/dictionary-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent, useRef, useMemo } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from \"@/components/ui/alert-dialog\";\nimport { BookMarked, PlusCircle, Trash2, Upload, Download, FileJson, XCircle, Languages } from \"lucide-react\";\nimport { useDictionary } from \"@/contexts/dictionary-context\";\nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { useToast } from '@/hooks/use-toast';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';\n\nexport function DictionarySettings() {\n  const { t, getWritingLanguageBase } = useI18n();\n  const { getWordsForLanguage, addWord, deleteWord, importWords, exportWordsString, clearDictionary } = useDictionary();\n  const [newWord, setNewWord] = useState(\"\");\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { toast } = useToast();\n  \n  const [selectedLanguage, setSelectedLanguage] = useState(getWritingLanguageBase());\n\n  const wordsForSelectedLang = useMemo(() => getWordsForLanguage(selectedLanguage), [getWordsForLanguage, selectedLanguage]);\n\n  const handleAddWord = (event: FormEvent) => {\n    event.preventDefault();\n    if (newWord.trim()) {\n      if(addWord(newWord.trim(), selectedLanguage)) {\n        setNewWord(\"\");\n      }\n    }\n  };\n\n  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const content = e.target?.result;\n          if (typeof content === 'string') {\n            const importedArray = JSON.parse(content);\n            if (Array.isArray(importedArray) && importedArray.every(item => typeof item === 'string')) {\n              importWords(importedArray, selectedLanguage, false); // false for merge\n            } else {\n              toast({titleKey: \"toastDictionaryImportInvalidFormat\", variant: \"destructive\"});\n            }\n          }\n        } catch (error) {\n          toast({titleKey: \"toastErrorTitle\", descriptionKey: \"toastDictionaryImportError\", variant: \"destructive\"});\n          console.error(\"Error importing dictionary:\", error);\n        }\n      };\n      reader.readAsText(file);\n    }\n    if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n    }\n  };\n\n  const handleExport = () => {\n    const jsonString = exportWordsString(selectedLanguage);\n    const blob = new Blob([jsonString], { type: \"application/json\" });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = `lingua_flow_dictionary_${selectedLanguage}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    toast({titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryExportSuccess\"});\n  };\n  \n  const handleClearDictionary = () => {\n    clearDictionary(selectedLanguage);\n  };\n\n  const selectedLanguageInfo = APP_WRITING_LANGUAGES.find(lang => lang.value === selectedLanguage);\n  const selectedLanguageLabel = selectedLanguageInfo ? t(selectedLanguageInfo.labelKey) : selectedLanguage;\n\n  return (\n    <div className=\"space-y-6\">\n       <div>\n        <Label htmlFor=\"dictionary-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Languages className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('dictionaryLanguageLabel')}\n        </Label>\n        <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>\n          <SelectTrigger id=\"dictionary-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectDictionaryLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_WRITING_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('dictionaryLanguageDescription')}</p>\n      </div>\n\n      <div>\n        <Label className=\"flex items-center text-sm font-medium mb-2\">\n            <BookMarked className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('personalDictionaryLabel')}\n        </Label>\n        <p className=\"text-xs text-muted-foreground mb-3 pl-1\">{t('personalDictionaryDescription')}</p>\n\n        <form onSubmit={handleAddWord} className=\"flex items-center gap-2 mb-4\">\n          <Input\n            type=\"text\"\n            value={newWord}\n            onChange={(e) => setNewWord(e.target.value)}\n            placeholder={t('addWordPlaceholder')}\n            className=\"flex-grow\"\n            aria-label={t('addWordPlaceholder')}\n          />\n          <Button type=\"submit\" size=\"icon\" variant=\"outline\" aria-label={t('addWordButton')}>\n            <PlusCircle className=\"h-4 w-4\" />\n          </Button>\n        </form>\n\n        {wordsForSelectedLang.length > 0 ? (\n          <ScrollArea className=\"h-40 w-full rounded-md border p-2 bg-muted/30\">\n            <ul className=\"space-y-1\">\n              {wordsForSelectedLang.map((word) => (\n                <li key={word} className=\"flex items-center justify-between p-1.5 rounded hover:bg-muted\">\n                  <span className=\"text-sm\">{word}</span>\n                  <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\" onClick={() => deleteWord(word, selectedLanguage)} aria-label={t('deleteWordButtonAria', { word })}>\n                    <XCircle className=\"h-4 w-4 text-destructive/80 hover:text-destructive\" />\n                  </Button>\n                </li>\n              ))}\n            </ul>\n          </ScrollArea>\n        ) : (\n          <p className=\"text-sm text-muted-foreground text-center py-4 border rounded-md bg-muted/30\">{t('dictionaryEmptyPlaceholder')}</p>\n        )}\n      </div>\n      \n      <div className=\"space-y-2\">\n         <Label className=\"flex items-center text-sm font-medium\">\n            <FileJson className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('dictionaryImportExportLabel')}\n        </Label>\n        <div className=\"grid grid-cols-2 gap-2 pt-1\">\n            <Button variant=\"outline\" onClick={() => fileInputRef.current?.click()}>\n                <Upload className=\"mr-2 h-4 w-4\" /> {t('importDictionaryButton')}\n            </Button>\n            <input type=\"file\" accept=\".json\" ref={fileInputRef} onChange={handleImport} className=\"hidden\" />\n            \n            <Button variant=\"outline\" onClick={handleExport} disabled={wordsForSelectedLang.length === 0}>\n                <Download className=\"mr-2 h-4 w-4\" /> {t('exportDictionaryButton')}\n            </Button>\n        </div>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('dictionaryImportExportDescription')}</p>\n      </div>\n\n      <div>\n        <AlertDialog>\n          <AlertDialogTrigger asChild>\n            <Button variant=\"destructive\" className=\"w-full\" disabled={wordsForSelectedLang.length === 0}>\n              <Trash2 className=\"mr-2 h-4 w-4\" /> {t('clearDictionaryForLanguageButton', { language: selectedLanguageLabel })}\n            </Button>\n          </AlertDialogTrigger>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>{t('clearDictionaryConfirmTitle')}</AlertDialogTitle>\n              <AlertDialogDescription>\n                {t('clearDictionaryForLanguageConfirmDescription', { language: selectedLanguageLabel })}\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel>{t('cancelButton')}</AlertDialogCancel>\n              <AlertDialogAction onClick={handleClearDictionary} className=\"bg-destructive hover:bg-destructive/90\">\n                {t('confirmClearButton')}\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1 text-center\">{t('clearDictionaryWarning')}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAeO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAC5C,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD;IAClH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4DAAE,IAAM,oBAAoB;2DAAmB;QAAC;QAAqB;KAAiB;IAEzH,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc;QACpB,IAAI,QAAQ,IAAI,IAAI;YAClB,IAAG,QAAQ,QAAQ,IAAI,IAAI,mBAAmB;gBAC5C,WAAW;YACb;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI;oBACF,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,IAAI,OAAO,YAAY,UAAU;wBAC/B,MAAM,gBAAgB,KAAK,KAAK,CAAC;wBACjC,IAAI,MAAM,OAAO,CAAC,kBAAkB,cAAc,KAAK,CAAC,CAAA,OAAQ,OAAO,SAAS,WAAW;4BACzF,YAAY,eAAe,kBAAkB,QAAQ,kBAAkB;wBACzE,OAAO;4BACL,MAAM;gCAAC,UAAU;gCAAsC,SAAS;4BAAa;wBAC/E;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM;wBAAC,UAAU;wBAAmB,gBAAgB;wBAA8B,SAAS;oBAAa;oBACxG,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YACA,OAAO,UAAU,CAAC;QACpB;QACA,IAAI,aAAa,OAAO,EAAE;YACtB,aAAa,OAAO,CAAC,KAAK,GAAG;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,aAAa,kBAAkB;QACrC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAmB;QAC/D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,CAAC;QAC9D,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QACpB,MAAM;YAAC,UAAU;YAAqB,gBAAgB;QAA8B;IACtF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,6HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAC/E,MAAM,wBAAwB,uBAAuB,EAAE,qBAAqB,QAAQ,IAAI;IAExF,qBACE,6LAAC;QAAI,WAAU;;0BACZ,6LAAC;;kCACA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA6B,WAAU;;0CACpD,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAkB,eAAe;;0CAC9C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA6B,WAAU;0CACvD,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,6HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC,qIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMhC,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACrB,EAAE;;;;;;;kCAEP,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;kCAE1D,6LAAC;wBAAK,UAAU;wBAAe,WAAU;;0CACvC,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAa,EAAE;gCACf,WAAU;gCACV,cAAY,EAAE;;;;;;0CAEhB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,MAAK;gCAAO,SAAQ;gCAAU,cAAY,EAAE;0CAChE,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIzB,qBAAqB,MAAM,GAAG,kBAC7B,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAG,WAAU;sCACX,qBAAqB,GAAG,CAAC,CAAC,qBACzB,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAU,SAAS,IAAM,WAAW,MAAM;4CAAmB,cAAY,EAAE,wBAAwB;gDAAE;4CAAK;sDACtJ,cAAA,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;mCAHd;;;;;;;;;;;;;;6CAUf,6LAAC;wBAAE,WAAU;kCAAgF,EAAE;;;;;;;;;;;;0BAInG,6LAAC;gBAAI,WAAU;;kCACZ,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACd,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa,OAAO,EAAE;;kDAC3D,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;;;;;;;0CAE3C,6LAAC;gCAAM,MAAK;gCAAO,QAAO;gCAAQ,KAAK;gCAAc,UAAU;gCAAc,WAAU;;;;;;0CAEvF,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAc,UAAU,qBAAqB,MAAM,KAAK;;kDACvF,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;;;;;;;;;;;;;kCAGhD,6LAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,6LAAC;;kCACC,6LAAC,8IAAA,CAAA,cAAW;;0CACV,6LAAC,8IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,WAAU;oCAAS,UAAU,qBAAqB,MAAM,KAAK;;sDACzF,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;wCAAE,EAAE,oCAAoC;4CAAE,UAAU;wCAAsB;;;;;;;;;;;;0CAGjH,6LAAC,8IAAA,CAAA,qBAAkB;;kDACjB,6LAAC,8IAAA,CAAA,oBAAiB;;0DAChB,6LAAC,8IAAA,CAAA,mBAAgB;0DAAE,EAAE;;;;;;0DACrB,6LAAC,8IAAA,CAAA,yBAAsB;0DACpB,EAAE,gDAAgD;oDAAE,UAAU;gDAAsB;;;;;;;;;;;;kDAGzF,6LAAC,8IAAA,CAAA,oBAAiB;;0DAChB,6LAAC,8IAAA,CAAA,oBAAiB;0DAAE,EAAE;;;;;;0DACtB,6LAAC,8IAAA,CAAA,oBAAiB;gDAAC,SAAS;gDAAuB,WAAU;0DAC1D,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAKX,6LAAC;wBAAE,WAAU;kCAAuD,EAAE;;;;;;;;;;;;;;;;;;AAI9E;GA7KgB;;QACwB,sIAAA,CAAA,UAAO;QACyD,4IAAA,CAAA,gBAAa;QAGjG,+HAAA,CAAA,WAAQ;;;KALZ", "debugId": null}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/feature-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface FeatureToggleProps {\n  featureKey: keyof FeatureSettings;\n  labelKey: string;\n  descriptionKey: string;\n}\n\nfunction FeatureToggle({ featureKey, labelKey, descriptionKey }: FeatureToggleProps) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between rounded-lg border p-3 shadow-sm\">\n      <div className=\"space-y-0.5\">\n        <Label htmlFor={featureKey} className=\"font-medium\">\n          {t(labelKey)}\n        </Label>\n        <p className=\"text-xs text-muted-foreground text-start\">\n          {t(descriptionKey)}\n        </p>\n      </div>\n      <Switch\n        id={featureKey}\n        checked={isChecked}\n        onCheckedChange={handleToggle}\n        aria-label={t(labelKey)}\n      />\n    </div>\n  );\n}\n\nfunction FeatureSection({ titleKey, children }: { titleKey: string; children: React.ReactNode }) {\n    const { t } = useI18n();\n    return (\n        <div>\n            <h4 className=\"mb-4 text-lg font-semibold leading-none tracking-tight\">{t(titleKey)} ✨</h4>\n            <div className=\"space-y-3\">\n                {children}\n            </div>\n        </div>\n    );\n}\n\nexport function FeatureSettings() {\n  const {t} = useI18n();\n  return (\n    <div className=\"space-y-8\">\n       <FeatureSection titleKey=\"generativeAiFeaturesLabel\">\n            <FeatureToggle \n                featureKey=\"showAiSuggestionsOnSelection\"\n                labelKey=\"showAiSuggestionsOnTextSelectionLabel\"\n                descriptionKey=\"showAiSuggestionsOnTextSelectionDescription\"\n            />\n             <FeatureToggle \n                featureKey=\"quickAiActions\"\n                labelKey=\"quickAiActionsForSelectedTextLabel\"\n                descriptionKey=\"quickAiActionsForSelectedTextDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"aiQuickReplies\"\n                labelKey=\"aiQuickReplySuggestionsLabel\"\n                descriptionKey=\"aiQuickReplySuggestionsDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"promptHistory\"\n                labelKey=\"viewAndReuseRecentPromptHistoryLabel\"\n                descriptionKey=\"viewAndReuseRecentPromptHistoryDescription\"\n            />\n       </FeatureSection>\n       \n       <FeatureSection titleKey=\"autoCorrectionFeaturesLabel\">\n            <FeatureToggle \n                featureKey=\"autoCorrect\"\n                labelKey=\"automaticTextCorrectionLabel\"\n                descriptionKey=\"automaticTextCorrectionDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"realTimeCorrection\"\n                labelKey=\"realTimeCorrectionLabel\"\n                descriptionKey=\"realTimeCorrectionDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"sentenceEnhancement\"\n                labelKey=\"sentenceEnhancementLabel\"\n                descriptionKey=\"sentenceEnhancementDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"showCorrectionFeedback\"\n                labelKey=\"showCorrectionFeedbackLabel\"\n                descriptionKey=\"showCorrectionFeedbackDescription\"\n            />\n       </FeatureSection>\n\n       <FeatureSection titleKey=\"coreCheckingFeaturesLabel\">\n         <FeatureToggle \n              featureKey=\"realTimeGrammarCheck\"\n              labelKey=\"realTimeGrammarCheckingLabel\"\n              descriptionKey=\"realTimeGrammarCheckingDescription\"\n          />\n          <FeatureToggle \n              featureKey=\"realTimeSpellCheck\"\n              labelKey=\"realTimeSpellCheckingLabel\"\n              descriptionKey=\"realTimeSpellCheckingDescription\"\n          />\n          <FeatureToggle\n              featureKey=\"styleSuggestions\"\n              labelKey=\"styleSuggestionsLabel\"\n              descriptionKey=\"styleSuggestionsDescription\"\n          />\n          <FeatureToggle\n              featureKey=\"enableLocalVerbRepetitionDetection\"\n              labelKey=\"localVerbRepetitionDetectionLabel\"\n              descriptionKey=\"localVerbRepetitionDetectionDescription\"\n          />\n       </FeatureSection>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AALA;;;;;AAaA,SAAS,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAsB;;IACjF,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,QAAQ,CAAC,WAAW;IACtC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAS;wBAAY,WAAU;kCACnC,EAAE;;;;;;kCAEL,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAGP,6LAAC,qIAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,SAAS;gBACT,iBAAiB;gBACjB,cAAY,EAAE;;;;;;;;;;;;AAItB;GA3BS;;QACiC,qJAAA,CAAA,qBAAkB;QAC5C,sIAAA,CAAA,UAAO;;;KAFd;AA6BT,SAAS,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAmD;;IAC3F,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,qBACI,6LAAC;;0BACG,6LAAC;gBAAG,WAAU;;oBAA0D,EAAE;oBAAU;;;;;;;0BACpF,6LAAC;gBAAI,WAAU;0BACV;;;;;;;;;;;;AAIjB;IAVS;;QACS,sIAAA,CAAA,UAAO;;;MADhB;AAYF,SAAS;;IACd,MAAM,EAAC,CAAC,EAAC,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAClB,qBACE,6LAAC;QAAI,WAAU;;0BACZ,6LAAC;gBAAe,UAAS;;kCACpB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAElB,6LAAC;wBACE,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIxB,6LAAC;gBAAe,UAAS;;kCACpB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIxB,6LAAC;gBAAe,UAAS;;kCACvB,6LAAC;wBACI,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;;;;;;;AAK7B;IA1EgB;;QACF,sIAAA,CAAA,UAAO;;;MADL", "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/writing-aid-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { PROFICIENCY_LEVELS, APP_WRITING_LANGUAGES, type ProficiencyLevel } from '@/config/languages';\nimport { useMemo } from 'react';\nimport { MicVocal, SearchCheck, Vote, Users } from 'lucide-react';\n\ninterface FeatureToggleProps {\n  featureKey: keyof FeatureSettings;\n  titleKey: string;\n  descriptionKey: string;\n  icon: React.ReactNode;\n}\n\nfunction FeatureToggle({ featureKey, titleKey, descriptionKey, icon }: FeatureToggleProps) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = !!settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"p-4 rounded-lg border bg-muted/30\">\n      <div className=\"flex items-start justify-between gap-4\">\n        <div className=\"flex items-start gap-3\">\n          <div className=\"text-primary mt-1\">{icon}</div>\n          <div className=\"space-y-1\">\n            <Label htmlFor={featureKey as string} className=\"font-semibold text-base\">\n              {t(titleKey)}\n            </Label>\n            <p className=\"text-sm text-muted-foreground text-start\">\n              {t(descriptionKey)}\n            </p>\n          </div>\n        </div>\n        <Switch\n          id={featureKey as string}\n          checked={isChecked}\n          onCheckedChange={handleToggle}\n          aria-label={t(titleKey)}\n          className=\"shrink-0 mt-1\"\n        />\n      </div>\n    </div>\n  );\n}\n\n\nexport function WritingAidSettings() {\n    const { t, getWritingLanguageBase, languageProficiency, setLanguageProficiency } = useI18n();\n    const currentWritingBaseLang = getWritingLanguageBase();\n\n    const handleProficiencyChange = (newProficiency: ProficiencyLevel) => {\n        setLanguageProficiency(currentWritingBaseLang, newProficiency);\n    };\n\n    const currentWritingLanguageInfo = useMemo(() => {\n        return APP_WRITING_LANGUAGES.find(lang => lang.value === currentWritingBaseLang);\n    }, [currentWritingBaseLang]);\n\n    const currentProficiency = languageProficiency[currentWritingBaseLang] || 'none';\n\n    return (\n        <div className=\"space-y-6 p-1\">\n            <div className=\"space-y-1\">\n                <h3 className=\"text-lg font-semibold\">{t('writingAssistanceTitle')}</h3>\n                <p className=\"text-sm text-muted-foreground text-start\">{t('writingAssistanceDescription')}</p>\n            </div>\n\n            {currentWritingLanguageInfo?.supportsProficiency && (\n                <div className=\"p-4 rounded-lg border bg-muted/30 space-y-2\">\n                    <Label htmlFor=\"language-proficiency-select\" className=\"flex items-center text-base font-semibold\">\n                        <MicVocal className=\"mr-2 h-5 w-5 text-primary\" />\n                        {t('yourLanguageProficiencyTitle')}\n                    </Label>\n                    <p className=\"text-sm text-muted-foreground pb-2 text-start\">\n                        {t('yourLanguageProficiencyDescription')}\n                    </p>\n                    <Select value={currentProficiency} onValueChange={(value) => handleProficiencyChange(value as ProficiencyLevel)}>\n                        <SelectTrigger id=\"language-proficiency-select\" className=\"w-full bg-background\">\n                        <SelectValue placeholder={t('selectProficiencyPlaceholder')} />\n                        </SelectTrigger>\n                        <SelectContent>\n                        {PROFICIENCY_LEVELS.map((level) => (\n                            <SelectItem key={level.value} value={level.value}>\n                            {t(level.labelKey)}\n                            </SelectItem>\n                        ))}\n                        </SelectContent>\n                    </Select>\n                </div>\n            )}\n            \n            <div className=\"space-y-4\">\n                <FeatureToggle\n                    featureKey=\"enableToneDetection\"\n                    titleKey=\"toneDetectionTitle\"\n                    descriptionKey=\"toneDetectionDescription\"\n                    icon={<Vote className=\"h-5 w-5\" />}\n                />\n                <FeatureToggle\n                    featureKey=\"enablePlagiarismDetection\"\n                    titleKey=\"plagiarismDetectionSettingsTitle\"\n                    descriptionKey=\"plagiarismDetectionDescription\"\n                    icon={<SearchCheck className=\"h-5 w-5\" />}\n                />\n                <FeatureToggle\n                    featureKey=\"enableNonNativeSpeakerSupport\"\n                    titleKey=\"nonNativeSupportTitle\"\n                    descriptionKey=\"nonNativeSupportDescription\"\n                    icon={<Users className=\"h-5 w-5\" />}\n                />\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAkBA,SAAS,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,EAAsB;;IACvF,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,CAAC,CAAC,QAAQ,CAAC,WAAW;IACxC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAqB;;;;;;sCACpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAS;oCAAsB,WAAU;8CAC7C,EAAE;;;;;;8CAEL,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;;;8BAIT,6LAAC,qIAAA,CAAA,SAAM;oBACL,IAAI;oBACJ,SAAS;oBACT,iBAAiB;oBACjB,cAAY,EAAE;oBACd,WAAU;;;;;;;;;;;;;;;;;AAKpB;GAjCS;;QACiC,qJAAA,CAAA,qBAAkB;QAC5C,sIAAA,CAAA,UAAO;;;KAFd;AAoCF,SAAS;;IACZ,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACzF,MAAM,yBAAyB;IAE/B,MAAM,0BAA0B,CAAC;QAC7B,uBAAuB,wBAAwB;IACnD;IAEA,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kEAAE;YACvC,OAAO,6HAAA,CAAA,wBAAqB,CAAC,IAAI;0EAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;;QAC7D;iEAAG;QAAC;KAAuB;IAE3B,MAAM,qBAAqB,mBAAmB,CAAC,uBAAuB,IAAI;IAE1E,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAA4C,EAAE;;;;;;;;;;;;YAG9D,4BAA4B,qCACzB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA8B,WAAU;;0CACnD,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,6LAAC;wBAAE,WAAU;kCACR,EAAE;;;;;;kCAEP,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAoB,eAAe,CAAC,QAAU,wBAAwB;;0CACjF,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA8B,WAAU;0CAC1D,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE5B,6LAAC,qIAAA,CAAA,gBAAa;0CACb,6HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,sBACrB,6LAAC,qIAAA,CAAA,aAAU;wCAAmB,OAAO,MAAM,KAAK;kDAC/C,EAAE,MAAM,QAAQ;uCADA,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAS5C,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAE1B,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEjC,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3C;IAnEgB;;QACuE,sIAAA,CAAA,UAAO;;;MAD9E", "debugId": null}}, {"offset": {"line": 3749, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/advanced-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Button } from '../ui/button';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../ui/alert-dialog';\nimport { Trash2 } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { Separator } from '../ui/separator';\n\nfunction AdvancedFeatureToggle({ featureKey, labelKey, descriptionKey }: { featureKey: keyof FeatureSettings, labelKey: string, descriptionKey: string }) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = !!settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between rounded-lg border p-3 shadow-sm\">\n      <div className=\"space-y-0.5\">\n        <Label htmlFor={featureKey as string} className=\"font-medium\">\n          {t(labelKey)}\n        </Label>\n        <p className=\"text-xs text-muted-foreground\">\n          {t(descriptionKey)}\n        </p>\n      </div>\n      <Switch\n        id={featureKey as string}\n        checked={isChecked}\n        onCheckedChange={handleToggle}\n        aria-label={t(labelKey)}\n      />\n    </div>\n  );\n}\n\n\nexport function AdvancedSettings() {\n    const { t } = useI18n();\n    const { toast } = useToast();\n\n    const handleResetSettings = () => {\n        // Clear all known settings from localStorage\n        localStorage.removeItem('lingua-flow-feature-settings');\n        localStorage.removeItem('lingua-flow-theme');\n        localStorage.removeItem('lingua-flow-font-size');\n        localStorage.removeItem('lingua-flow-high-contrast');\n        localStorage.removeItem('lingua-flow-ui-language');\n        localStorage.removeItem('lingua-flow-writing-language-dialect');\n        localStorage.removeItem('lingua-flow-language-proficiency');\n        localStorage.removeItem('lingua-flow-dictionary');\n        \n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastResetSuccess\" });\n\n        // Reload the page to apply defaults\n        setTimeout(() => {\n            window.location.reload();\n        }, 1000);\n    };\n\n    return (\n        <div className=\"space-y-6\">\n            <div className=\"space-y-4\">\n                 <AdvancedFeatureToggle\n                    featureKey=\"enableAutomaticLanguageDetection\"\n                    labelKey=\"enableAutomaticLanguageDetectionLabel\"\n                    descriptionKey=\"enableAutomaticLanguageDetectionDescription\"\n                />\n                <AdvancedFeatureToggle\n                    featureKey=\"enableOfflineFunctionality\"\n                    labelKey=\"enableOfflineFunctionalityLabel\"\n                    descriptionKey=\"enableOfflineFunctionalityDescription\"\n                />\n            </div>\n\n            <Separator />\n            \n            <div className=\"space-y-3\">\n                <h3 className=\"text-lg font-semibold leading-none tracking-tight\">{t('dataManagementLabel')}</h3>\n                <div className=\"rounded-lg border border-destructive/50 p-4\">\n                    <div className=\"flex items-center justify-between gap-4\">\n                         <div className=\"space-y-1\">\n                            <Label className=\"font-medium text-destructive\">{t('resetAllSettingsLabel')}</Label>\n                            <p className=\"text-xs text-muted-foreground\">{t('resetAllSettingsDescription')}</p>\n                         </div>\n                         <AlertDialog>\n                            <AlertDialogTrigger asChild>\n                                <Button variant=\"destructive\" className=\"shrink-0\">\n                                    <Trash2 className=\"mr-2 h-4 w-4\" /> {t('resetButtonLabel')}\n                                </Button>\n                            </AlertDialogTrigger>\n                            <AlertDialogContent>\n                                <AlertDialogHeader>\n                                <AlertDialogTitle>{t('resetAllSettingsConfirmTitle')}</AlertDialogTitle>\n                                <AlertDialogDescription>\n                                    {t('resetAllSettingsConfirmDescription')}\n                                </AlertDialogDescription>\n                                </AlertDialogHeader>\n                                <AlertDialogFooter>\n                                <AlertDialogCancel>{t('cancelButton')}</AlertDialogCancel>\n                                <AlertDialogAction onClick={handleResetSettings} className=\"bg-destructive hover:bg-destructive/90\">\n                                    {t('confirmResetButton')}\n                                </AlertDialogAction>\n                                </AlertDialogFooter>\n                            </AlertDialogContent>\n                        </AlertDialog>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,SAAS,sBAAsB,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAmF;;IACtJ,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,CAAC,CAAC,QAAQ,CAAC,WAAW;IACxC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAS;wBAAsB,WAAU;kCAC7C,EAAE;;;;;;kCAEL,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAGP,6LAAC,qIAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,SAAS;gBACT,iBAAiB;gBACjB,cAAY,EAAE;;;;;;;;;;;;AAItB;GA3BS;;QACiC,qJAAA,CAAA,qBAAkB;QAC5C,sIAAA,CAAA,UAAO;;;KAFd;AA8BF,SAAS;;IACZ,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,sBAAsB;QACxB,6CAA6C;QAC7C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,MAAM;YAAE,UAAU;YAAqB,gBAAgB;QAAoB;QAE3E,oCAAoC;QACpC,WAAW;YACP,OAAO,QAAQ,CAAC,MAAM;QAC1B,GAAG;IACP;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACV,6LAAC;wBACE,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,6LAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIvB,6LAAC,wIAAA,CAAA,YAAS;;;;;0BAEV,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAqD,EAAE;;;;;;kCACrE,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACV,6LAAC;oCAAI,WAAU;;sDACZ,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC,EAAE;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;8CAEnD,6LAAC,8IAAA,CAAA,cAAW;;sDACT,6LAAC,8IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAc,WAAU;;kEACpC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;oDAAE,EAAE;;;;;;;;;;;;sDAG/C,6LAAC,8IAAA,CAAA,qBAAkB;;8DACf,6LAAC,8IAAA,CAAA,oBAAiB;;sEAClB,6LAAC,8IAAA,CAAA,mBAAgB;sEAAE,EAAE;;;;;;sEACrB,6LAAC,8IAAA,CAAA,yBAAsB;sEAClB,EAAE;;;;;;;;;;;;8DAGP,6LAAC,8IAAA,CAAA,oBAAiB;;sEAClB,6LAAC,8IAAA,CAAA,oBAAiB;sEAAE,EAAE;;;;;;sEACtB,6LAAC,8IAAA,CAAA,oBAAiB;4DAAC,SAAS;4DAAqB,WAAU;sEACtD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC;IA1EgB;;QACE,sIAAA,CAAA,UAAO;QACH,+HAAA,CAAA,WAAQ;;;MAFd", "debugId": null}}, {"offset": {"line": 4065, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/settings-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState } from \"react\";\nimport { Settings2, Languages, Palette, BookMarked, SlidersHorizontal, PenTool, Cog } from \"lucide-react\"; \nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { LanguageSettings } from \"./language-settings\";\nimport { AppearanceSettings } from \"./appearance-settings\";\nimport { DictionarySettings } from \"./dictionary-settings\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { FeatureSettings } from \"./feature-settings\";\nimport { WritingAidSettings } from \"./writing-aid-settings\";\nimport { AdvancedSettings } from \"./advanced-settings\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nexport function SettingsPanel() {\n  const { t } = useI18n(); \n\n  const settingsCategories = [\n    { id: 'language', labelKey: 'languageLabel', Icon: Languages, component: <LanguageSettings /> },\n    { id: 'appearance', labelKey: 'appearanceLabel', Icon: Palette, component: <AppearanceSettings /> },\n    { id: 'dictionary', labelKey: 'dictionaryLabel', Icon: BookMarked, component: <DictionarySettings /> },\n    { id: 'features', labelKey: 'featuresLabel', Icon: SlidersHorizontal, component: <FeatureSettings /> },\n    { id: 'writing-aid', labelKey: 'writingAidLabel', Icon: PenTool, component: <WritingAidSettings /> },\n    { id: 'advanced', labelKey: 'advancedSettingsLabel', Icon: Cog, component: <AdvancedSettings /> },\n  ];\n\n  const [activeCategory, setActiveCategory] = useState(settingsCategories[0].id);\n\n  const activeComponent = settingsCategories.find(c => c.id === activeCategory)?.component;\n\n  return (\n    <div className=\"w-full bg-background text-foreground flex flex-col h-full p-4 md:p-6\">\n      <div className=\"pb-4 border-b shrink-0\">\n        <h3 className=\"flex items-center text-2xl font-semibold\">\n          <Settings2 className=\"mr-3 h-6 w-6\" />\n          {t('settingsTitle')}\n        </h3>\n        <p className=\"text-sm text-muted-foreground mt-1\">\n          {t('settingsDescription')}\n        </p>\n      </div>\n      \n      <div className=\"flex flex-col md:flex-row flex-grow min-h-0 pt-6 gap-6 md:gap-8\">\n        {/* Navigation Panel */}\n        <div className=\"w-full md:w-[16rem] shrink-0\">\n          <nav className=\"flex flex-col gap-1\">\n            {settingsCategories.map(({ id, labelKey, Icon }) => (\n              <Button\n                key={id}\n                variant=\"ghost\"\n                onClick={() => setActiveCategory(id)}\n                className={cn(\n                  \"w-full justify-start text-left h-auto py-2 px-3 text-base md:text-sm\",\n                  activeCategory === id && \"bg-accent text-accent-foreground\"\n                )}\n              >\n                <Icon className=\"mr-2.5 h-4 w-4 shrink-0\" />\n                <span className=\"truncate\">{t(labelKey)}</span>\n              </Button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content Panel */}\n        <div className=\"flex-1 relative md:border-l md:pl-8\">\n            <div className=\"absolute inset-0\">\n                <ScrollArea className=\"h-full w-full\">\n                    <div className=\"pr-2\">\n                        {activeComponent}\n                    </div>\n                </ScrollArea>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAeO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,qBAAqB;QACzB;YAAE,IAAI;YAAY,UAAU;YAAiB,MAAM,+MAAA,CAAA,YAAS;YAAE,yBAAW,6LAAC,yJAAA,CAAA,mBAAgB;;;;;QAAI;QAC9F;YAAE,IAAI;YAAc,UAAU;YAAmB,MAAM,2MAAA,CAAA,UAAO;YAAE,yBAAW,6LAAC,2JAAA,CAAA,qBAAkB;;;;;QAAI;QAClG;YAAE,IAAI;YAAc,UAAU;YAAmB,MAAM,qNAAA,CAAA,aAAU;YAAE,yBAAW,6LAAC,2JAAA,CAAA,qBAAkB;;;;;QAAI;QACrG;YAAE,IAAI;YAAY,UAAU;YAAiB,MAAM,mOAAA,CAAA,oBAAiB;YAAE,yBAAW,6LAAC,wJAAA,CAAA,kBAAe;;;;;QAAI;QACrG;YAAE,IAAI;YAAe,UAAU;YAAmB,MAAM,+MAAA,CAAA,UAAO;YAAE,yBAAW,6LAAC,+JAAA,CAAA,qBAAkB;;;;;QAAI;QACnG;YAAE,IAAI;YAAY,UAAU;YAAyB,MAAM,mMAAA,CAAA,MAAG;YAAE,yBAAW,6LAAC,yJAAA,CAAA,mBAAgB;;;;;QAAI;KACjG;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB,CAAC,EAAE,CAAC,EAAE;IAE7E,MAAM,kBAAkB,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;IAE/E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAIP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,iBAC7C,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;oCACjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,mBAAmB,MAAM;;sDAG3B,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAY,EAAE;;;;;;;mCATzB;;;;;;;;;;;;;;;kCAgBb,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,6IAAA,CAAA,aAAU;gCAAC,WAAU;0CAClB,cAAA,6LAAC;oCAAI,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GA9DgB;;QACA,sIAAA,CAAA,UAAO;;;KADP", "debugId": null}}, {"offset": {"line": 4308, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,wKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,wKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4401, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/help/help-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { \n  HelpCircle, Edit3, Wand2, Languages, Palette, BookMarked, PenTool, Cog, SlidersHorizontal\n} from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\nexport function HelpPanel() {\n  const { t } = useI18n();\n\n  const helpSections = [\n    {\n      id: \"editor\",\n      titleKey: \"helpEditorTitle\",\n      icon: <Edit3 className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpEditorDescription\"\n    },\n    {\n      id: \"ai-tools\",\n      titleKey: \"helpAiToolsTitle\",\n      icon: <Wand2 className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAiToolsDescription\"\n    },\n    {\n      id: \"language-settings\",\n      titleKey: \"helpLanguageSettingsTitle\",\n      icon: <Languages className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpLanguageSettingsDescription\"\n    },\n    {\n      id: \"appearance-settings\",\n      titleKey: \"helpAppearanceSettingsTitle\",\n      icon: <Palette className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAppearanceSettingsDescription\"\n    },\n    {\n      id: \"dictionary-settings\",\n      titleKey: \"helpDictionarySettingsTitle\",\n      icon: <BookMarked className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpDictionarySettingsDescription\"\n    },\n    {\n      id: \"feature-settings\",\n      titleKey: \"helpFeatureSettingsTitle\",\n      icon: <SlidersHorizontal className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpFeatureSettingsDescription\"\n    },\n    {\n      id: \"writing-aid-settings\",\n      titleKey: \"helpWritingAidSettingsTitle\",\n      icon: <PenTool className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpWritingAidSettingsDescription\"\n    },\n    {\n      id: \"advanced-settings\",\n      titleKey: \"helpAdvancedSettingsTitle\",\n      icon: <Cog className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAdvancedSettingsDescription\"\n    },\n  ];\n\n  return (\n    <div className=\"flex flex-col h-full p-4 md:p-6\">\n      <div className=\"pb-4 border-b shrink-0\">\n        <h3 className=\"flex items-center text-2xl font-semibold text-foreground\">\n          <HelpCircle className=\"mr-3 h-6 w-6\" />\n          {t('helpPanelTitle')}\n        </h3>\n        <p className=\"text-sm text-muted-foreground mt-1 text-start\">{t('helpPanelDescription')}</p>\n      </div>\n      <ScrollArea className=\"flex-grow pt-6 -mr-4 pr-4\">\n        <div className=\"space-y-4\">\n            <p className=\"text-base text-muted-foreground leading-relaxed text-start\">{t('helpPanelIntro')}</p>\n\n            <Accordion type=\"multiple\" className=\"w-full\">\n              {helpSections.map(section => (\n                <AccordionItem value={section.id} key={section.id}>\n                  <AccordionTrigger className=\"text-lg\">\n                    <div className=\"flex items-center\">\n                      {section.icon} {t(section.titleKey)}\n                    </div>\n                  </AccordionTrigger>\n                  <AccordionContent className=\"space-y-2 py-2 pl-4 text-base text-muted-foreground leading-relaxed text-start\">\n                    <p dangerouslySetInnerHTML={{ __html: t(section.contentKey) }}></p>\n                  </AccordionContent>\n                </AccordionItem>\n              ))}\n            </Accordion>\n            \n            <p className=\"text-base text-muted-foreground pt-4 text-start\">{t('helpPanelTip')}</p>\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;;;AAPA;;;;;AASO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,6MAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,kNAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,mOAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;;YACnC,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,YAAY;QACd;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACrB,EAAE;;;;;;;kCAEL,6LAAC;wBAAE,WAAU;kCAAiD,EAAE;;;;;;;;;;;;0BAElE,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAE,WAAU;sCAA8D,EAAE;;;;;;sCAE7E,6LAAC,wIAAA,CAAA,YAAS;4BAAC,MAAK;4BAAW,WAAU;sCAClC,aAAa,GAAG,CAAC,CAAA,wBAChB,6LAAC,wIAAA,CAAA,gBAAa;oCAAC,OAAO,QAAQ,EAAE;;sDAC9B,6LAAC,wIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,IAAI;oDAAC;oDAAE,EAAE,QAAQ,QAAQ;;;;;;;;;;;;sDAGtC,6LAAC,wIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,6LAAC;gDAAE,yBAAyB;oDAAE,QAAQ,EAAE,QAAQ,UAAU;gDAAE;;;;;;;;;;;;mCAPzB,QAAQ,EAAE;;;;;;;;;;sCAarD,6LAAC;4BAAE,WAAU;sCAAmD,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAK9E;GAvFgB;;QACA,sIAAA,CAAA,UAAO;;;KADP", "debugId": null}}, {"offset": {"line": 4670, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/writing-mode-selector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Palette } from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\ninterface WritingModeSelectorProps {\n  value: string;\n  onChange: (value: string) => void;\n}\n\nconst writingModes = [\n  { value: \"Casual\", labelKey: \"casualWritingMode\" },\n  { value: \"Formal\", labelKey: \"formalWritingMode\" },\n  { value: \"Professional\", labelKey: \"professionalWritingMode\" },\n  { value: \"Creative\", labelKey: \"creativeWritingMode\" },\n  { value: \"Technical\", labelKey: \"technicalWritingMode\" },\n  { value: \"Academic\", labelKey: \"academicWritingMode\" },\n  { value: \"Business\", labelKey: \"businessWritingMode\" },\n];\n\nexport function WritingModeSelector({ value, onChange }: WritingModeSelectorProps) {\n  const { t } = useI18n();\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Label htmlFor=\"writing-mode\" className=\"flex items-center text-sm font-medium text-muted-foreground\">\n        <Palette className=\"mr-2 h-3.5 w-3.5\" />\n        {t('writingModeLabel')}:\n      </Label>\n      <Select value={value} onValueChange={onChange}>\n        <SelectTrigger id=\"writing-mode\" className=\"w-full sm:w-[180px] h-9\">\n          <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n        </SelectTrigger>\n        <SelectContent>\n          {writingModes.map((mode) => (\n            <SelectItem key={mode.value} value={mode.value}>\n              {t(mode.labelKey)}\n            </SelectItem>\n          ))}\n        </SelectContent>\n      </Select>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AALA;;;;;AAYA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAU,UAAU;IAAoB;IACjD;QAAE,OAAO;QAAU,UAAU;IAAoB;IACjD;QAAE,OAAO;QAAgB,UAAU;IAA0B;IAC7D;QAAE,OAAO;QAAY,UAAU;IAAsB;IACrD;QAAE,OAAO;QAAa,UAAU;IAAuB;IACvD;QAAE,OAAO;QAAY,UAAU;IAAsB;IACrD;QAAE,OAAO;QAAY,UAAU;IAAsB;CACtD;AAEM,SAAS,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAA4B;;IAC/E,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAe,WAAU;;kCACtC,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAClB,EAAE;oBAAoB;;;;;;;0BAEzB,6LAAC,qIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAO,eAAe;;kCACnC,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,IAAG;wBAAe,WAAU;kCACzC,cAAA,6LAAC,qIAAA,CAAA,cAAW;4BAAC,aAAa,EAAE;;;;;;;;;;;kCAE9B,6LAAC,qIAAA,CAAA,gBAAa;kCACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;gCAAkB,OAAO,KAAK,KAAK;0CAC3C,EAAE,KAAK,QAAQ;+BADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;AAQvC;GAtBgB;;QACA,sIAAA,CAAA,UAAO;;;KADP", "debugId": null}}, {"offset": {"line": 4803, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/app-shell.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { type ReactNode } from 'react';\nimport { Sidebar<PERSON>rovider, Sidebar, SidebarHeader, SidebarContent, SidebarFooter, SidebarTrigger, SidebarInset, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarCollapse } from '@/components/ui/sidebar';\nimport { Logo } from '@/components/icons/logo';\nimport { Button } from '@/components/ui/button';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { Moon, Sun, Home, Settings, HelpCircle, User, Globe } from 'lucide-react';\nimport { useState } from 'react';\nimport { SettingsPanel } from '@/components/settings/settings-panel';\nimport { HelpPanel } from '@/components/help/help-panel';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\nimport { Label } from '@/components/ui/label';\nimport { Separator } from '../ui/separator';\nimport { WritingModeSelector } from '../ai/writing-mode-selector';\n\ninterface AppShellProps {\n  children: (props: { writingMode: string }) => ReactNode;\n}\n\nexport function AppShell({ children }: AppShellProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const [activeView, setActiveView] = useState('editor');\n  const [writingMode, setWritingMode] = useState(\"Formal\");\n\n  const mainContent = () => {\n    switch (activeView) {\n      case 'settings':\n        return <SettingsPanel />;\n      case 'help':\n        return <HelpPanel />;\n      default:\n        return children({ writingMode });\n    }\n  };\n  \n  const sidebarDirection = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === uiLanguage)?.dir === 'rtl' ? 'right' : 'left';\n\n  return (\n    <SidebarProvider>\n      <Sidebar side={sidebarDirection}>\n        <SidebarHeader>\n          <div className=\"flex items-center gap-2\">\n            <Logo className=\"h-10 w-10\" />\n            <div className=\"flex flex-col group-data-[collapsible=icon]:hidden\">\n                <h2 className=\"text-lg font-semibold text-sidebar-primary\">{t('appName')}</h2>\n                <p className=\"text-xs text-muted-foreground\">{t('appDescription')}</p>\n            </div>\n          </div>\n          <div className=\"ml-auto flex items-center gap-2\">\n            <SidebarCollapse className=\"group-data-[collapsible=icon]:hidden\"/>\n          </div>\n        </SidebarHeader>\n        <SidebarContent>\n            <SidebarMenu>\n                <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('editor'); }} isActive={activeView === 'editor'} tooltip={{ children: t('editorTitle') }}>\n                        <Home/>\n                        <span>{t('editorTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n                 <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('settings'); }} isActive={activeView === 'settings'} tooltip={{ children: t('settingsTitle') }}>\n                        <Settings />\n                        <span>{t('settingsTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n                 <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('help'); }} isActive={activeView === 'help'} tooltip={{ children: t('helpTitle') }}>\n                        <HelpCircle />\n                        <span>{t('helpTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n            </SidebarMenu>\n        </SidebarContent>\n        <SidebarFooter className=\"border-t border-sidebar-border\">\n          <Separator className=\"my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden\" />\n          <div className=\"flex items-center justify-between gap-2 group-data-[collapsible=icon]:justify-center p-2\">\n            <Button \n                variant=\"ghost\" \n                size=\"icon\" \n                className=\"text-sidebar-foreground hover:bg-sidebar-accent\"\n                onClick={() => setTheme(effectiveTheme === 'light' ? 'dark' : 'light')} \n                aria-label={effectiveTheme === 'light' ? t('switchToDarkMode') : t('switchToLightMode')}\n            >\n              {effectiveTheme === 'dark' ? <Sun/> : <Moon />}\n            </Button>\n            <Select value={uiLanguage} onValueChange={setUiLanguage}>\n              <SelectTrigger \n                className=\"h-9 text-xs bg-sidebar-background border-sidebar-border text-sidebar-foreground focus:ring-sidebar-ring group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:justify-center\"\n                aria-label={t('selectLanguagePlaceholder')}\n              >\n                  <Globe className=\"h-4 w-4\" />\n                  <span className=\"group-data-[collapsible=icon]:hidden\">\n                    <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                  </span>\n              </SelectTrigger>\n              <SelectContent>\n                {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                  <SelectItem key={lang.value} value={lang.value} className=\"text-xs\">\n                    {t(lang.labelKey)}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n          <Separator className=\"my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden\" />\n          <div className=\"hidden items-center justify-center group-data-[collapsible=icon]:flex\">\n             <SidebarCollapse />\n          </div>\n        </SidebarFooter>\n      </Sidebar>\n      \n      <SidebarInset className=\"flex flex-col\">\n        <header className=\"sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4 sm:px-6\">\n            <SidebarTrigger className=\"md:hidden\"/>\n            <div className=\"flex-1\">\n                <WritingModeSelector value={writingMode} onChange={setWritingMode} />\n            </div>\n        </header>\n        <main className=\"flex-1 overflow-auto\">\n            {mainContent()}\n        </main>\n      </SidebarInset>\n    </SidebarProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;;;AAjBA;;;;;;;;;;;;;;AAuBO,SAAS,SAAS,EAAE,QAAQ,EAAiB;;IAClD,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,sJAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,YAAS;;;;;YACnB;gBACE,OAAO,SAAS;oBAAE;gBAAY;QAClC;IACF;IAEA,MAAM,mBAAmB,6HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,QAAQ,QAAQ,UAAU;IAEjH,qBACE,6LAAC,sIAAA,CAAA,kBAAe;;0BACd,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;;kCACb,6LAAC,sIAAA,CAAA,gBAAa;;0CACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sIAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAA8C,EAAE;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sIAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG/B,6LAAC,sIAAA,CAAA,iBAAc;kCACX,cAAA,6LAAC,sIAAA,CAAA,cAAW;;8CACR,6LAAC,sIAAA,CAAA,kBAAe;8CACZ,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAW;wCAAG,UAAU,eAAe;wCAAU,SAAS;4CAAE,UAAU,EAAE;wCAAe;;0DAC1J,6LAAC,sMAAA,CAAA,OAAI;;;;;0DACL,6LAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;8CAGhB,6LAAC,sIAAA,CAAA,kBAAe;8CACb,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAa;wCAAG,UAAU,eAAe;wCAAY,SAAS;4CAAE,UAAU,EAAE;wCAAiB;;0DAChK,6LAAC,6MAAA,CAAA,WAAQ;;;;;0DACT,6LAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;8CAGhB,6LAAC,sIAAA,CAAA,kBAAe;8CACb,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAS;wCAAG,UAAU,eAAe;wCAAQ,SAAS;4CAAE,UAAU,EAAE;wCAAa;;0DACpJ,6LAAC,qNAAA,CAAA,aAAU;;;;;0DACX,6LAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC,sIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,SAAS,mBAAmB,UAAU,SAAS;wCAC9D,cAAY,mBAAmB,UAAU,EAAE,sBAAsB,EAAE;kDAEpE,mBAAmB,uBAAS,6LAAC,mMAAA,CAAA,MAAG;;;;iEAAK,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;kDAE7C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6LAAC,qIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,cAAY,EAAE;;kEAEZ,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEACd,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAa,EAAE;;;;;;;;;;;;;;;;;0DAGlC,6LAAC,qIAAA,CAAA,gBAAa;0DACX,6HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,6LAAC,qIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;wDAAE,WAAU;kEACvD,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAOnC,6LAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAI,WAAU;0CACZ,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;0BAKvB,6LAAC,sIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,6LAAC;wBAAO,WAAU;;0CACd,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,0JAAA,CAAA,sBAAmB;oCAAC,OAAO;oCAAa,UAAU;;;;;;;;;;;;;;;;;kCAG3D,6LAAC;wBAAK,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAKb;GA3GgB;;QAC2B,sIAAA,CAAA,UAAO;QACJ,uIAAA,CAAA,gBAAa;;;KAF3C", "debugId": null}}, {"offset": {"line": 5251, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5286, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5389, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5437, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/contextual-ai-rephraser.ts"], "sourcesContent": ["'use server';\n/**\n * @fileOverview An AI agent that rephrases text based on context.\n *\n * - rephraseText - A function that handles the text rephrasing process.\n * - RephraseTextInput - The input type for the rephraseText function.\n * - RephraseTextOutput - The return type for the rephraseText function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst RephraseTextInputSchema = z.object({\n  selectedText: z.string().describe('The text selected by the user to rephrase.'),\n  contextText: z.string().describe('The surrounding context of the selected text.'),\n  tone: z.string().optional().describe('The desired tone of the rephrased text.'),\n  style: z.string().optional().describe('The desired style of the rephrased text.'),\n});\nexport type RephraseTextInput = z.infer<typeof RephraseTextInputSchema>;\n\nconst RephraseTextOutputSchema = z.object({\n  rephrasedText: z.string().describe('The rephrased text based on the context.'),\n});\nexport type RephraseTextOutput = z.infer<typeof RephraseTextOutputSchema>;\n\nexport async function rephraseText(input: RephraseTextInput): Promise<RephraseTextOutput | null> {\n  return rephraseTextFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'rephraseTextPrompt',\n  input: {schema: RephraseTextInputSchema},\n  output: {schema: RephraseTextOutputSchema},\n  prompt: `You are an AI assistant that helps users rephrase text to improve clarity and flow.\n\n  Selected Text: {{{selectedText}}}\n  Context: {{{contextText}}}\n  Tone: {{{tone}}}\n  Style: {{{style}}}\n\n  Rephrased Text:`,\n});\n\nconst rephraseTextFlow = ai.defineFlow(\n  {\n    name: 'rephraseTextFlow',\n    inputSchema: RephraseTextInputSchema,\n    outputSchema: RephraseTextOutputSchema.nullable(),\n  },\n  async (input: RephraseTextInput): Promise<RephraseTextOutput | null> => {\n    try {\n      const {output} = await prompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[rephraseTextFlow] - Error: ${errorMessage}`, {input});\n      // Return null on error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAyBsB", "debugId": null}}, {"offset": {"line": 5449, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 5497, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/word-toolkit-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to provide synonyms and spelling for a word.\n *\n * - getWordSuggestions - A function that handles the word analysis process.\n * - WordToolkitInput - The input type for the getWordSuggestions function.\n * - WordToolkitOutput - The return type for the getWordSuggestions function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst WordToolkitInputSchema = z.object({\n  word: z.string().describe('The single word to be analyzed.'),\n  context: z\n    .string()\n    .describe('The surrounding sentence or text to provide context.'),\n  language: z\n    .string()\n    .describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\").'),\n});\nexport type WordToolkitInput = z.infer<typeof WordToolkitInputSchema>;\n\nconst WordToolkitOutputSchema = z.object({\n  synonyms: z\n    .array(z.string())\n    .describe('An array of relevant synonyms for the word, based on its context. Should be 5 or less.'),\n  correctSpelling: z\n    .string()\n    .describe(\n      'The correct spelling of the word. If the word is already spelled correctly, returns the original word.'\n    ),\n});\nexport type WordToolkitOutput = z.infer<typeof WordToolkitOutputSchema>;\n\nexport async function getWordSuggestions(\n  input: WordToolkitInput\n): Promise<WordToolkitOutput> {\n  return wordToolkitFlow(input);\n}\n\nconst wordToolkitPrompt = ai.definePrompt({\n  name: 'wordToolkitPrompt',\n  input: {schema: WordToolkitInputSchema},\n  output: {schema: WordToolkitOutputSchema},\n  prompt: `You are a linguistic expert providing quick tools for writers. You will be given a specific word, its surrounding context, and its language.\n\nYour task is to provide a list of synonyms and the correct spelling for the given word in the specified language ({{language}}).\n\n- Synonyms should be relevant to the word's usage in the provided context. Provide up to 5 synonyms. If no relevant synonyms are found, return an empty array.\n- For spelling, if the word is already spelled correctly, return the word itself. If it is misspelled, return the correct spelling.\n\nAnalyze the following:\nWord: {{{word}}}\nContext: {{{context}}}\nLanguage: {{language}}\n`,\n});\n\nconst wordToolkitFlow = ai.defineFlow(\n  {\n    name: 'wordToolkitFlow',\n    inputSchema: WordToolkitInputSchema,\n    outputSchema: WordToolkitOutputSchema,\n  },\n  async (input: WordToolkitInput): Promise<WordToolkitOutput> => {\n    try {\n      const {output} = await wordToolkitPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : String(error);\n      console.error(`[wordToolkitFlow] - Error: ${errorMessage}`, {input});\n      // Return a default/empty state to prevent the client from crashing\n      // on transient errors like API quota limits.\n      return {\n        synonyms: [],\n        correctSpelling: input.word,\n      };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAoCsB", "debugId": null}}, {"offset": {"line": 5509, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-to-speech-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to convert a word into spoken audio of its spelling.\n *\n * - getSpelledOutAudio - A function that takes a word and generates a WAV audio data URI of it being spelled out.\n * - SpelledOutAudioInput - The input type for the getSpelledOutAudio function.\n * - SpelledOutAudioOutput - The return type for the getSpelledOutAudio function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {googleAI} from '@genkit-ai/googleai';\nimport {z} from 'genkit';\nimport wav from 'wav';\n\nconst SpelledOutAudioInputSchema = z.object({\n  word: z.string().describe('The word to be spelled out.'),\n  lang: z.string().describe('The BCP-47 language code for the pronunciation voice.'),\n});\nexport type SpelledOutAudioInput = z.infer<typeof SpelledOutAudioInputSchema>;\n\nconst SpelledOutAudioOutputSchema = z.object({\n  audioDataUri: z.string().nullable().describe(\"A data URI of the WAV audio file. Expected format: 'data:audio/wav;base64,<encoded_data>'. Is null on failure.\"),\n});\nexport type SpelledOutAudioOutput = z.infer<typeof SpelledOutAudioOutputSchema>;\n\nexport async function getSpelledOutAudio(input: SpelledOutAudioInput): Promise<SpelledOutAudioOutput> {\n  return spellWordToAudioFlow(input);\n}\n\nasync function toWav(\n  pcmData: Buffer,\n  channels = 1,\n  rate = 24000,\n  sampleWidth = 2\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const writer = new wav.Writer({\n      channels,\n      sampleRate: rate,\n      bitDepth: sampleWidth * 8,\n    });\n\n    const bufs: Buffer[] = [];\n    writer.on('error', reject);\n    writer.on('data', (d) => {\n      bufs.push(d);\n    });\n    writer.on('end', () => {\n      resolve(Buffer.concat(bufs).toString('base64'));\n    });\n\n    writer.write(pcmData);\n    writer.end();\n  });\n}\n\nconst spellWordToAudioFlow = ai.defineFlow(\n  {\n    name: 'spellWordToAudioFlow',\n    inputSchema: SpelledOutAudioInputSchema,\n    outputSchema: SpelledOutAudioOutputSchema,\n  },\n  async ({ word, lang }) => {\n    try {\n      // Format the word to be spelled out letter by letter.\n      const spelledOutWord = word.split('').join(' ');\n\n      const { media } = await ai.generate({\n        model: googleAI.model('gemini-2.5-flash-preview-tts'),\n        config: {\n          responseModalities: ['AUDIO'],\n          speechConfig: {\n            voiceConfig: {\n              prebuiltVoiceConfig: { voiceName: 'Algenib' }, // A standard voice\n            },\n          },\n        },\n        prompt: spelledOutWord,\n      });\n\n      if (!media) {\n        throw new Error('No audio media was generated by the model.');\n      }\n\n      const audioBuffer = Buffer.from(\n        media.url.substring(media.url.indexOf(',') + 1),\n        'base64'\n      );\n      \n      const wavBase64 = await toWav(audioBuffer);\n\n      return {\n        audioDataUri: 'data:audio/wav;base64,' + wavBase64,\n      };\n    } catch (error) {\n       const errorMessage = error instanceof Error ? error.message : String(error);\n       console.error(`[spellWordToAudioFlow] - Error: ${errorMessage}`, { word, lang });\n       // Return null instead of throwing an error to prevent server crashes.\n       return { audioDataUri: null };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IA0BsB", "debugId": null}}, {"offset": {"line": 5521, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/word-toolkit-popover.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { PopoverContent } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from \"@/components/ui/button\";\nimport { Loader2, Volume2 } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useToast } from '@/hooks/use-toast';\nimport { getWordSuggestions, type WordToolkitOutput, type WordToolkitInput } from '@/ai/flows/word-toolkit-flow';\nimport { getSpelledOutAudio, type SpelledOutAudioInput } from '@/ai/flows/text-to-speech-flow';\n\ninterface WordToolkitPopoverProps {\n  selectedWord: string;\n  contextText: string;\n  language: string;\n  onSynonymSelect: (synonym: string) => void;\n}\n\nexport function WordToolkitPopover({ selectedWord, contextText, language, onSynonymSelect }: WordToolkitPopoverProps) {\n  const { t } = useI18n();\n  const { toast } = useToast();\n  const [isLoading, setIsLoading] = useState(true);\n  const [analysisResult, setAnalysisResult] = useState<WordToolkitOutput | null>(null);\n  const [isPronouncing, setIsPronouncing] = useState(false);\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  const handleWordAnalysis = useCallback(async () => {\n    setIsLoading(true);\n    setAnalysisResult(null);\n    try {\n      const input: WordToolkitInput = {\n        word: selectedWord,\n        context: contextText,\n        language,\n      };\n      const result = await getWordSuggestions(input);\n      setAnalysisResult(result);\n    } catch (error) {\n      console.error(\"Error in word toolkit analysis:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastWordToolkitError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  }, [selectedWord, contextText, language, toast]);\n\n  useEffect(() => {\n    if (selectedWord) {\n        handleWordAnalysis();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedWord, contextText, language]);\n\n  const handlePronounce = async () => {\n    if (!analysisResult?.correctSpelling) return;\n    setIsPronouncing(true);\n    try {\n      const input: SpelledOutAudioInput = {\n        word: analysisResult.correctSpelling,\n        lang: language,\n      };\n      const { audioDataUri } = await getSpelledOutAudio(input);\n      \n      if (audioDataUri && audioRef.current) {\n        audioRef.current.src = audioDataUri;\n        audioRef.current.play();\n      } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n      }\n    } catch (error) {\n      console.error(\"Error generating or playing pronunciation:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n    } finally {\n      setIsPronouncing(false);\n    }\n  };\n\n  return (\n    <PopoverContent className=\"w-80\" side=\"top\" align=\"start\">\n      <div className=\"grid gap-4\">\n        <div className=\"space-y-2\">\n          <h4 className=\"font-medium leading-none\">{t('wordToolkitTitle')}</h4>\n          <p className=\"text-sm text-muted-foreground\">{t('wordToolkitPopoverDescription')}</p>\n        </div>\n\n        {isLoading && (\n          <div className=\"flex items-center justify-center h-24\">\n            <Loader2 className=\"h-5 w-5 animate-spin text-primary\" />\n          </div>\n        )}\n\n        {!isLoading && analysisResult && (\n          <div className=\"space-y-4\">\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('synonymsLabel')}</h5>\n              <div className=\"flex flex-wrap gap-2\">\n                {analysisResult.synonyms.length > 0 ? (\n                  analysisResult.synonyms.map((synonym) => (\n                    <Badge\n                      key={synonym}\n                      variant=\"secondary\"\n                      className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground\"\n                      onClick={() => onSynonymSelect(synonym)}\n                      title={t('applySynonymTooltip', { synonym })}\n                    >\n                      {synonym}\n                    </Badge>\n                  ))\n                ) : (\n                  <p className=\"text-xs text-muted-foreground\">{t('noSynonymsFound')}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('pronunciationLabel')}</h5>\n              <div className=\"flex items-center justify-between gap-2 p-2 bg-muted rounded-md\">\n                <span className=\"text-sm font-semibold\">{analysisResult.correctSpelling}</span>\n                <Button size=\"icon\" variant=\"ghost\" className=\"h-7 w-7\" onClick={handlePronounce} disabled={isPronouncing} title={t('pronounceButton')}>\n                  {isPronouncing ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Volume2 className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n      <audio ref={audioRef} className=\"hidden\" onEnded={() => setIsPronouncing(false)} onError={() => setIsPronouncing(false)}/>\n    </PopoverContent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAmBO,SAAS,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAA2B;;IAClH,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IAEjD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACrC,aAAa;YACb,kBAAkB;YAClB,IAAI;gBACF,MAAM,QAA0B;oBAC9B,MAAM;oBACN,SAAS;oBACT;gBACF;gBACA,MAAM,SAAS,MAAM,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE;gBACxC,kBAAkB;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAAyB,SAAS;gBAAc;YACvG,SAAU;gBACR,aAAa;YACf;QACF;6DAAG;QAAC;QAAc;QAAa;QAAU;KAAM;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,cAAc;gBACd;YACJ;QACF,uDAAuD;QACvD;uCAAG;QAAC;QAAc;QAAa;KAAS;IAExC,MAAM,kBAAkB;QACtB,IAAI,CAAC,gBAAgB,iBAAiB;QACtC,iBAAiB;QACjB,IAAI;YACF,MAAM,QAA8B;gBAClC,MAAM,eAAe,eAAe;gBACpC,MAAM;YACR;YACA,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE;YAElD,IAAI,gBAAgB,SAAS,OAAO,EAAE;gBACpC,SAAS,OAAO,CAAC,GAAG,GAAG;gBACvB,SAAS,OAAO,CAAC,IAAI;YACvB,OAAO;gBACL,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAA2B,SAAS;gBAAc;YACzG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA2B,SAAS;YAAc;QACzG,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,iBAAc;QAAC,WAAU;QAAO,MAAK;QAAM,OAAM;;0BAChD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4B,EAAE;;;;;;0CAC5C,6LAAC;gCAAE,WAAU;0CAAiC,EAAE;;;;;;;;;;;;oBAGjD,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAItB,CAAC,aAAa,gCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;kDACZ,eAAe,QAAQ,CAAC,MAAM,GAAG,IAChC,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC3B,6LAAC,oIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,gBAAgB;gDAC/B,OAAO,EAAE,uBAAuB;oDAAE;gDAAQ;0DAEzC;+CANI;;;;sEAUT,6LAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;;;;;;0CAKtD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAyB,eAAe,eAAe;;;;;;0DACvE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,SAAQ;gDAAQ,WAAU;gDAAU,SAAS;gDAAiB,UAAU;gDAAe,OAAO,EAAE;0DACjH,8BAAgB,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAA4B,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/F,6LAAC;gBAAM,KAAK;gBAAU,WAAU;gBAAS,SAAS,IAAM,iBAAiB;gBAAQ,SAAS,IAAM,iBAAiB;;;;;;;;;;;;AAGvH;GA9GgB;;QACA,sIAAA,CAAA,UAAO;QACH,+HAAA,CAAA,WAAQ;;;KAFZ", "debugId": null}}, {"offset": {"line": 5824, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-debounce.ts"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    // Set debouncedValue to value (passed in) after the specified delay\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    // Return a cleanup function that will be called every time ...\n    // ... useEffect is re-called. useEffect will only be re-called ...\n    // ... if value or delay changes (see the inputs array below). \n    // This is how we prevent debouncedValue from changing if value is ...\n    // ... changing within the delay period. Timeout gets cleared and restarted.\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]); // Only re-call effect if value or delay changes\n\n  return debouncedValue;\n}\n"], "names": [], "mappings": ";;;AAGA;;AAFA;;AAIO,SAAS,YAAe,KAAQ,EAAE,KAAa;;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,oEAAoE;YACpE,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;gBACpB;gDAAG;YAEH,+DAA+D;YAC/D,mEAAmE;YACnE,+DAA+D;YAC/D,sEAAsE;YACtE,4EAA4E;YAC5E;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM,GAAG,gDAAgD;IAEpE,OAAO;AACT;GApBgB", "debugId": null}}, {"offset": {"line": 5869, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/enhanced-text-editor.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useCallback, useEffect, forwardRef, useMemo, type ChangeEvent, type UIEvent } from 'react';\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { FileText, IterationCw, Loader2, Eraser, Copy, Check, Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, Undo2, Redo2, Wand2, List, ListOrdered, ShieldCheck } from \"lucide-react\";\nimport { rephraseText, type RephraseTextInput } from '@/ai/flows/contextual-ai-rephraser';\nimport { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { useToast } from '@/hooks/use-toast';\nimport { Label } from '../ui/label';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { cn } from '@/lib/utils';\nimport { Separator } from '../ui/separator';\nimport { WordToolkitPopover } from './word-toolkit-popover';\nimport { useDebounce } from '@/hooks/use-debounce';\n\nexport interface SelectionDetail {\n  text: string;\n  start: number;\n  end: number;\n}\n\nexport type PlagiarismSource = Omit<AnalysisSuggestion, 'type'> & { type: 'plagiarism' };\nexport type RephraseSource = Omit<AnalysisSuggestion, 'type'> & { type: 'rephrase' };\n\ntype CombinedSuggestion = AnalysisSuggestion | PlagiarismSource;\n\ninterface EnhancedTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  writingMode: string;\n  direction: 'ltr' | 'rtl';\n  suggestions: AnalysisSuggestion[];\n  plagiarismSources: PlagiarismSource[];\n  onApplySuggestion: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;\n  onDismissSuggestion: (suggestionId: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n}\n\ninterface SuggestionPopoverProps {\n    suggestion: AnalysisSuggestion, \n    onApply: (s: AnalysisSuggestion) => void,\n    onDismiss: (id: string) => void,\n    onClose: () => void;\n}\n\ninterface PlagiarismPopoverProps {\n    source: PlagiarismSource, \n    onClose: () => void;\n}\n\nconst fontOptions = [\n    { value: \"'Inter', sans-serif\", label: 'Inter' },\n    { value: \"'Source Code Pro', monospace\", label: 'Source Code Pro' },\n    { value: \"'Georgia', serif\", label: 'Georgia' },\n    { value: \"'Times New Roman', Times, serif\", label: 'Times New Roman' },\n];\n\n\n// A single popover for inline suggestions\nfunction SuggestionPopover({ \n    suggestion, \n    onApply,\n    onDismiss,\n    onClose,\n}: SuggestionPopoverProps) {\n    const { t } = useI18n();\n\n    const handleApply = () => {\n        onApply(suggestion);\n        onClose();\n    };\n\n    const handleDismiss = () => {\n        onDismiss(suggestion.id);\n        onClose();\n    };\n\n    return (\n        <PopoverContent className=\"w-auto max-w-sm p-3 shadow-xl\" side=\"top\" align=\"start\" onEscapeKeyDown={onClose}>\n            <div className=\"space-y-3\">\n                <p className=\"text-sm text-muted-foreground\">{suggestion.message}</p>\n                <div className=\"p-2 rounded-md bg-muted border\">\n                    <p className=\"text-sm text-destructive line-through\">{suggestion.originalSegment}</p>\n                    <p className=\"text-sm text-primary font-semibold\">{suggestion.suggestion}</p>\n                </div>\n                <div className=\"flex justify-end gap-2\">\n                    <Button size=\"sm\" variant=\"ghost\" onClick={handleDismiss}>\n                        {t('dismissButton')}\n                    </Button>\n                    <Button size=\"sm\" onClick={handleApply}>\n                        <Check className=\"mr-2 h-4 w-4\" />\n                        {t('correctButton')}\n                    </Button>\n                </div>\n            </div>\n        </PopoverContent>\n    );\n}\n\nfunction PlagiarismPopover({ source, onClose }: PlagiarismPopoverProps) {\n    const { t } = useI18n();\n\n    return (\n        <PopoverContent className=\"w-auto max-w-sm p-3 shadow-xl\" side=\"top\" align=\"start\" onEscapeKeyDown={onClose}>\n            <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-2 font-semibold text-destructive\">\n                    <ShieldCheck className=\"h-5 w-5\" />\n                    {t('plagiarismReportLabel')}\n                </div>\n                <p className=\"text-sm text-muted-foreground\">{source.message}</p>\n                <div className=\"p-2 rounded-md bg-destructive/10 border border-destructive/20\">\n                    <p className=\"text-sm text-destructive\">\n                        <span className=\"font-semibold\">{t('originalSourceLabel')}:</span>{' '}\n                        <a href={source.suggestion} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline break-all\">\n                            {source.suggestion}\n                        </a>\n                    </p>\n                </div>\n                <div className=\"flex justify-end\">\n                    <Button size=\"sm\" variant=\"ghost\" onClick={onClose}>\n                        {t('dismissButton')}\n                    </Button>\n                </div>\n            </div>\n        </PopoverContent>\n    );\n}\n\n\nexport const EnhancedTextEditor = forwardRef<HTMLDivElement, EnhancedTextEditorProps>(\n  ({ value, onChange, writingMode, direction, suggestions, plagiarismSources, onApplySuggestion, onDismissSuggestion, onUndo, onRedo, canUndo, canRedo }, ref) => {\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const backdropRef = useRef<HTMLDivElement>(null);\n  const { t, getWritingLanguageBase } = useI18n();\n  const { toast } = useToast();\n  \n  const [selectedTextDetail, setSelectedTextDetail] = useState<SelectionDetail | null>(null);\n  const debouncedSelection = useDebounce(selectedTextDetail, 300);\n\n  const [isAiPopoverOpen, setIsAiPopoverOpen] = useState(false);\n  const [rephrasedText, setRephrasedText] = useState<string>(\"\");\n  const [isRephrasing, setIsRephrasing] = useState(false);\n  const [activeSuggestion, setActiveSuggestion] = useState<CombinedSuggestion | null>(null);\n  const [fontFamily, setFontFamily] = useState(fontOptions[0].value);\n  \n  const isSingleWordSelection = useMemo(() => \n      debouncedSelection ? !debouncedSelection.text.trim().includes(' ') && debouncedSelection.text.trim().length > 0 : false\n  , [debouncedSelection]);\n\n\n  const placeholder = t('startWritingPlaceholder');\n  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(event.target.value);\n  };\n  \n  const handleScroll = (event: UIEvent<HTMLTextAreaElement>) => {\n    if (backdropRef.current) {\n        const target = event.currentTarget;\n        backdropRef.current.scrollTop = target.scrollTop;\n        backdropRef.current.scrollLeft = target.scrollLeft;\n\n        // Force synchronization to prevent drift\n        requestAnimationFrame(() => {\n          if (backdropRef.current && target) {\n            backdropRef.current.scrollTop = target.scrollTop;\n            backdropRef.current.scrollLeft = target.scrollLeft;\n          }\n        });\n    }\n  };\n\n  const handleSelect = () => {\n    if (textareaRef.current) {\n      const { selectionStart, selectionEnd, value: editorValue } = textareaRef.current;\n      const selectedText = editorValue.substring(selectionStart, selectionEnd);\n\n      if (selectionStart !== selectionEnd && selectedText.trim().length > 0) {\n        setSelectedTextDetail({ text: selectedText, start: selectionStart, end: selectionEnd });\n      } else {\n        setSelectedTextDetail(null);\n        setIsAiPopoverOpen(false);\n      }\n    }\n  };\n\n  const handleRephrase = useCallback(async () => {\n    if (!debouncedSelection || !debouncedSelection.text.trim() || isSingleWordSelection) {\n      return;\n    }\n    setIsRephrasing(true);\n    setRephrasedText(\"\");\n    try {\n      const input: RephraseTextInput = {\n        selectedText: debouncedSelection.text,\n        contextText: value,\n        tone: writingMode,\n        style: writingMode,\n      };\n      const result = await rephraseText(input);\n      if (!result || typeof result.rephrasedText === 'undefined') {\n        throw new Error('AI model did not return the expected output format.');\n      }\n      setRephrasedText(result.rephrasedText);\n      toast({ titleKey: \"toastSuggestionReady\", descriptionKey: \"toastTextGeneratedSuccess\" });\n    } catch (error) {\n      console.error(\"Error rephrasing text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastRephraseError\", variant: \"destructive\" });\n      setIsAiPopoverOpen(false);\n    } finally {\n      setIsRephrasing(false);\n    }\n  }, [debouncedSelection, value, writingMode, toast, isSingleWordSelection]);\n  \n  useEffect(() => {\n    if (isAiPopoverOpen && debouncedSelection && !isSingleWordSelection && !rephrasedText && !isRephrasing) {\n        handleRephrase();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isAiPopoverOpen, debouncedSelection, rephrasedText, isRephrasing, handleRephrase, isSingleWordSelection]);\n\n\n  const applyRephrasedText = () => {\n    if (debouncedSelection && rephrasedText) {\n      const newValue =\n        value.substring(0, debouncedSelection.start) +\n        rephrasedText +\n        value.substring(debouncedSelection.end);\n      onChange(newValue); \n      setIsAiPopoverOpen(false);\n      setSelectedTextDetail(null);\n      setRephrasedText(\"\");\n      if (textareaRef.current) {\n        textareaRef.current.focus();\n        const newCursorPosition = debouncedSelection.start + rephrasedText.length;\n        setTimeout(() => {\n          if (textareaRef.current) {\n            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);\n          }\n        }, 0);\n      }\n    }\n  };\n  \n  const handleApplyInlineSuggestion = (suggestion: AnalysisSuggestion) => {\n    onApplySuggestion(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);\n  };\n  \n  const handleApplyWordReplacement = (replacement: string) => {\n    if (debouncedSelection) {\n      const newValue =\n        value.substring(0, debouncedSelection.start) +\n        replacement +\n        value.substring(debouncedSelection.end);\n      onChange(newValue);\n      \n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastWordReplacedSuccess\", descriptionParams: { word: replacement } });\n\n      setIsAiPopoverOpen(false);\n      setSelectedTextDetail(null);\n      \n      if (textareaRef.current) {\n        textareaRef.current.focus();\n        const newCursorPosition = debouncedSelection.start + replacement.length;\n        setTimeout(() => {\n          if(textareaRef.current) {\n            textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);\n          }\n        }, 0);\n      }\n    }\n  };\n\n  const handleFormat = (formatType: 'bold' | 'italic' | 'underline' | 'ordered-list' | 'unordered-list') => {\n    const textarea = textareaRef.current;\n    if (!textarea) return;\n\n    let { selectionStart, selectionEnd } = textarea;\n\n    // For lists, find the start and end of the line(s)\n    if (formatType === 'ordered-list' || formatType === 'unordered-list') {\n      let lineStartIndex = value.lastIndexOf('\\n', selectionStart - 1) + 1;\n      \n      // If no text is selected, operate on the current line\n      if (selectionStart === selectionEnd) {\n          selectionEnd = value.indexOf('\\n', selectionStart);\n          if (selectionEnd === -1) {\n              selectionEnd = value.length;\n          }\n      }\n\n      let lineEndIndex = value.indexOf('\\n', selectionEnd - 1);\n      if (lineEndIndex === -1 || lineEndIndex < selectionStart) {\n        lineEndIndex = value.length;\n      }\n      \n      const textToFormat = value.substring(lineStartIndex, lineEndIndex);\n      const lines = textToFormat.split('\\n');\n      let formattedLines;\n      let isList = false;\n\n      if (formatType === 'ordered-list') {\n          isList = lines.every(line => /^\\s*\\d+\\.\\s/.test(line.trim()) || line.trim() === '');\n          if (isList) {\n              formattedLines = lines.map(line => line.replace(/^\\s*\\d+\\.\\s*/, ''));\n          } else {\n              let counter = 1;\n              formattedLines = lines.map(line => {\n                  const trimmedLine = line.trim().replace(/^•\\s/, '');\n                  return trimmedLine ? `${counter++}. ${trimmedLine}` : '';\n              });\n          }\n      } else { // unordered-list\n          isList = lines.every(line => /^\\s*•\\s/.test(line.trim()) || line.trim() === '');\n          if (isList) {\n              formattedLines = lines.map(line => line.replace(/^\\s*•\\s*/, ''));\n          } else {\n              formattedLines = lines.map(line => {\n                  const trimmedLine = line.trim().replace(/^\\d+\\.\\s/, '');\n                  return trimmedLine ? `• ${trimmedLine}` : '';\n              });\n          }\n      }\n\n      const formattedBlock = formattedLines.join('\\n');\n      const newValue = value.substring(0, lineStartIndex) + formattedBlock + value.substring(lineEndIndex);\n      onChange(newValue);\n\n      setTimeout(() => {\n          textarea.focus();\n          textarea.setSelectionRange(lineStartIndex, lineStartIndex + formattedBlock.length);\n      }, 0);\n\n    } else { // Bold, Italic, Underline\n      const selectedText = value.substring(selectionStart, selectionEnd);\n      if (!selectedText) return;\n      \n      let formattedText;\n      switch (formatType) {\n        case 'bold': formattedText = `**${selectedText}**`; break;\n        case 'italic': formattedText = `*${selectedText}*`; break;\n        case 'underline': formattedText = `<u>${selectedText}</u>`; break;\n        default: return;\n      }\n\n      const newValue = value.substring(0, selectionStart) + formattedText + value.substring(selectionEnd);\n      onChange(newValue);\n\n      setTimeout(() => {\n          textarea.focus();\n          const newCursorPos = selectionStart + formattedText.length;\n          textarea.setSelectionRange(newCursorPos, newCursorPos);\n      }, 0);\n    }\n  };\n  \n  const renderFormattedText = (text: string): (string | JSX.Element)[] => {\n    if (!text) return [];\n    \n    let parts: (string | JSX.Element)[] = [text];\n\n    const processRegex = (regex: RegExp, tag: 'strong' | 'em' | 'u', keyPrefix: string) => {\n        parts = parts.flatMap((part, partIndex) => {\n            if (typeof part !== 'string') return part;\n\n            const splitParts = part.split(regex);\n            const processed: (string | JSX.Element)[] = [];\n\n            for (let i = 0; i < splitParts.length; i++) {\n                if (i % 2 === 0) {\n                    if(splitParts[i]) processed.push(splitParts[i]);\n                } else {\n                    const Tag = tag;\n                    processed.push(<Tag key={`${keyPrefix}-${partIndex}-${i}`}>{splitParts[i]}</Tag>);\n                }\n            }\n            return processed;\n        });\n    };\n    \n    processRegex(/\\*\\*([\\s\\S]*?)\\*\\*/g, 'strong', 'bold');\n    processRegex(/\\*([\\s\\S]*?)\\*/g, 'em', 'italic');\n    processRegex(/<u>([\\s\\S]*?)<\\/u>/g, 'u', 'underline');\n\n    return parts;\n};\n\n  const combinedSuggestions = useMemo(() => {\n    const allSuggestions = [...suggestions, ...plagiarismSources] as CombinedSuggestion[];\n    return allSuggestions.sort((a, b) => (a.startIndex || 0) - (b.startIndex || 0));\n  }, [suggestions, plagiarismSources]);\n  \n  const handleHighlightClick = (suggestion: CombinedSuggestion) => {\n      setActiveSuggestion(suggestion);\n  };\n  const closeSuggestionPopover = () => {\n      setActiveSuggestion(null);\n  }\n\n  const highlightedContent = useMemo(() => {\n    if (!value) return <p>&nbsp;</p>; // Render a non-breaking space to maintain height\n    \n    const sortedSuggestions = combinedSuggestions;\n\n    let lastIndex = 0;\n    const elements = [];\n\n    sortedSuggestions.forEach((suggestion) => {\n        const { startIndex, endIndex, originalSegment, id, type } = suggestion;\n\n        if (startIndex === undefined || endIndex === undefined || startIndex < lastIndex) {\n            return; \n        }\n\n        // Add the text before the current suggestion\n        if (startIndex > lastIndex) {\n            elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex, startIndex))}</React.Fragment>);\n        }\n\n        const highlightClass = cn('suggestion-highlight', {\n            'suggestion-spelling': type === 'spelling',\n            'suggestion-grammar': type === 'grammar',\n            'suggestion-rewrite': type === 'rewrite',\n            'suggestion-style': type === 'style',\n            'suggestion-plagiarism': type === 'plagiarism'\n        });\n\n        elements.push(\n            <Popover key={id} open={activeSuggestion?.id === id} onOpenChange={(open) => {if (!open) closeSuggestionPopover()}}>\n                <PopoverTrigger asChild>\n                    <span className={highlightClass} onClick={() => handleHighlightClick(suggestion)}>\n                        {renderFormattedText(originalSegment)}\n                    </span>\n                </PopoverTrigger>\n                {activeSuggestion?.id === id && (\n                    type === 'plagiarism' ? (\n                        <PlagiarismPopover \n                            source={suggestion as PlagiarismSource}\n                            onClose={closeSuggestionPopover}\n                        />\n                    ) : (\n                        <SuggestionPopover \n                            suggestion={suggestion as AnalysisSuggestion}\n                            onApply={handleApplyInlineSuggestion} \n                            onDismiss={onDismissSuggestion}\n                            onClose={closeSuggestionPopover}\n                        />\n                    )\n                )}\n            </Popover>\n        );\n\n        lastIndex = endIndex;\n    });\n\n    // Add any remaining text after the last suggestion\n    if (lastIndex < value.length) {\n        elements.push(<React.Fragment key={`text-${lastIndex}`}>{renderFormattedText(value.slice(lastIndex))}</React.Fragment>);\n    }\n\n    // Split the entire content by newlines to create <p> tags for each line\n    const finalElements = [];\n    let lineBuffer: (string|JSX.Element)[] = [];\n\n    elements.forEach((el) => {\n        if (typeof el.props.children === 'string') {\n            const text = el.props.children;\n            const lines = text.split('\\n');\n            lines.forEach((line: string, lineIndex: number) => {\n                if(line) lineBuffer.push(line);\n                if (lineIndex < lines.length - 1) {\n                    finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer.length > 0 ? lineBuffer : <>&nbsp;</>}</p>);\n                    lineBuffer = [];\n                }\n            });\n        } else {\n            lineBuffer.push(el);\n        }\n    });\n\n    if (lineBuffer.length > 0) {\n        finalElements.push(<p key={`line-${finalElements.length}`}>{lineBuffer}</p>);\n    }\n    \n    if (finalElements.length === 0 && value === \"\") {\n      return <p>&nbsp;</p>;\n    }\n\n    return <>{finalElements}</>;\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [value, combinedSuggestions, onDismissSuggestion, activeSuggestion]);\n\n  const handleClearText = () => {\n    onChange(\"\");\n    toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastEditorClearedSuccess\" });\n  };\n\n  const handleCopyText = async () => {\n    if (!value) {\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastEditorEmptyForCopyError\", variant: \"destructive\" });\n      return;\n    }\n    try {\n      await navigator.clipboard.writeText(value);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastEditorContentCopiedSuccess\" });\n    } catch (error) {\n      console.error(\"Failed to copy text from editor:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastEditorContentCopyError\", variant: \"destructive\" });\n    }\n  };\n  \n  const editorStyles: React.CSSProperties = {\n    fontFamily,\n    fontSize: 'inherit',\n    lineHeight: 'inherit',\n    letterSpacing: 'inherit',\n    padding: '1rem',\n    boxSizing: 'border-box',\n    width: '100%',\n    height: '100%',\n    resize: 'none',\n    border: 'none',\n    outline: 'none',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    wordBreak: 'break-word',\n    overflowWrap: 'break-word',\n  };\n\n  return (\n    <Card className=\"h-full flex flex-col\" ref={ref}>\n      <CardHeader className=\"p-4 border-b flex flex-row items-center justify-between gap-4 flex-wrap\">\n        <CardTitle className=\"flex items-center text-lg shrink-0\">\n          <FileText className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('editorTitle')}\n        </CardTitle>\n        <div className=\"flex items-center flex-nowrap gap-x-2 justify-end grow\">\n            <div className=\"flex items-center gap-1\">\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={onUndo} disabled={!canUndo} title={t('undoButton')}>\n                    <Undo2 className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={onRedo} disabled={!canRedo} title={t('redoButton')}>\n                    <Redo2 className=\"h-4 w-4\" />\n                </Button>\n            </div>\n\n            <Separator orientation=\"vertical\" className=\"h-6\" />\n\n            <Select value={fontFamily} onValueChange={setFontFamily}>\n                <SelectTrigger className=\"w-[140px] h-8\">\n                    <SelectValue placeholder=\"Select font\" />\n                </SelectTrigger>\n                <SelectContent>\n                    {fontOptions.map(font => (\n                        <SelectItem key={font.value} value={font.value} style={{fontFamily: font.value}}>\n                            {font.label}\n                        </SelectItem>\n                    ))}\n                </SelectContent>\n            </Select>\n\n            <Separator orientation=\"vertical\" className=\"h-6\" />\n\n            <div className=\"flex items-center gap-0.5\">\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('bold')} title=\"Bold\">\n                    <Bold className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('italic')} title=\"Italic\">\n                    <Italic className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('underline')} title=\"Underline\">\n                    <Underline className=\"h-4 w-4\" />\n                </Button>\n                 <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('ordered-list')} title=\"Numbered List\">\n                    <ListOrdered className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => handleFormat('unordered-list')} title=\"Bulleted List\">\n                    <List className=\"h-4 w-4\" />\n                </Button>\n            </div>\n            \n            <div className=\"flex items-center gap-0.5\">\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Left\">\n                  <AlignLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Center\">\n                  <AlignCenter className=\"h-4 w-4\" />\n              </Button>\n              <Button variant=\"outline\" size=\"icon\" className=\"h-8 w-8\" onClick={() => {}} title=\"Align Right\">\n                  <AlignRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"flex-grow p-0 relative\">\n          <div className=\"relative h-full\">\n            <div\n              ref={backdropRef}\n              className=\"editor-backdrop absolute inset-0 z-[1] overflow-auto pointer-events-none\"\n              style={{\n                ...editorStyles,\n                textAlign: direction === 'rtl' ? 'right' : 'left',\n                margin: 0,\n                border: 0,\n                background: 'transparent'\n              }}\n              dir={direction}\n            >\n              {highlightedContent}\n            </div>\n            <Textarea\n              ref={textareaRef}\n              value={value}\n              onChange={handleChange}\n              onSelect={handleSelect}\n              onScroll={handleScroll}\n              placeholder={placeholder}\n              className=\"editor-textarea absolute inset-0 z-0 rounded-none focus-visible:ring-0 focus-visible:ring-offset-0 min-h-[250px] max-h-[70vh] bg-transparent resize-none\"\n              style={{\n                ...editorStyles,\n                color: 'transparent',\n                caretColor: 'hsl(var(--foreground))',\n                textAlign: direction === 'rtl' ? 'right' : 'left',\n                margin: 0,\n                border: 0,\n                background: 'transparent'\n              }}\n              aria-label={t('editorTitle')}\n              dir={direction}\n            />\n          </div>\n      </CardContent>\n      <CardFooter className=\"p-4 border-t flex items-center justify-start gap-2 flex-wrap\">\n        <Popover open={isAiPopoverOpen} onOpenChange={setIsAiPopoverOpen}>\n          <PopoverTrigger asChild>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              disabled={!debouncedSelection || isRephrasing}\n            >\n              <Wand2 className=\"mr-2 h-4 w-4\" />\n              {t('aiToolsButton')}\n            </Button>\n          </PopoverTrigger>\n          {debouncedSelection && (\n            isSingleWordSelection ? (\n                <WordToolkitPopover \n                    selectedWord={debouncedSelection.text}\n                    contextText={value}\n                    language={getWritingLanguageBase()}\n                    onSynonymSelect={handleApplyWordReplacement}\n                />\n            ) : (\n                <PopoverContent className=\"w-80\" side=\"top\" align=\"start\">\n                <div className=\"grid gap-4\">\n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-medium leading-none\">{t('rephrasePopoverTitle')}</h4>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {t('rephrasePopoverDescription')}\n                    </p>\n                  </div>\n                  \n                    <div className=\"space-y-1\">\n                      <Label htmlFor=\"original-text-popover\">{t('originalTextLabel')}</Label>\n                      <Textarea\n                        id=\"original-text-popover\"\n                        value={debouncedSelection.text}\n                        readOnly\n                        className=\"h-20 text-xs bg-muted/50\"\n                      />\n                    </div>\n                  \n                  {isRephrasing && (\n                    <div className=\"flex items-center justify-center h-20\">\n                      <Loader2 className=\"h-5 w-5 animate-spin text-primary\" />\n                    </div>\n                  )}\n                  {!isRephrasing && rephrasedText && (\n                     <div className=\"space-y-1\">\n                      <Label htmlFor=\"rephrased-text-popover\">{t('suggestionTextLabel')}</Label>\n                      <Textarea\n                        id=\"rephrased-text-popover\"\n                        value={rephrasedText}\n                        readOnly\n                        className=\"h-20 text-xs bg-muted\"\n                      />\n                    </div>\n                  )}\n                  <div className=\"flex justify-end gap-2\">\n                    <Button variant=\"ghost\" size=\"sm\" onClick={() => setIsAiPopoverOpen(false)}>{t('cancelButton')}</Button>\n                    <Button\n                      size=\"sm\"\n                      onClick={applyRephrasedText}\n                      disabled={isRephrasing || !rephrasedText}\n                    >\n                      {t('applyButton')}\n                    </Button>\n                  </div>\n                </div>\n              </PopoverContent>\n            )\n          )}\n        </Popover>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleClearText}\n          disabled={value.length === 0}\n          aria-label={t('clearEditorButtonAriaLabel')}\n          title={t('clearEditorButtonAriaLabel')}\n        >\n          <Eraser className=\"mr-2 h-4 w-4\" />\n          {t('clearEditorButton')}\n        </Button>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleCopyText}\n          disabled={value.length === 0}\n          aria-label={t('copyEditorButtonAriaLabel')}\n          title={t('copyEditorButtonAriaLabel')}\n        >\n          <Copy className=\"mr-2 h-4 w-4\" />\n          {t('copyEditorButton')}\n        </Button>\n      </CardFooter>\n    </Card>\n  );\n});\n\nEnhancedTextEditor.displayName = \"EnhancedTextEditor\";\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AAyDA,MAAM,cAAc;IAChB;QAAE,OAAO;QAAuB,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAAgC,OAAO;IAAkB;IAClE;QAAE,OAAO;QAAoB,OAAO;IAAU;IAC9C;QAAE,OAAO;QAAmC,OAAO;IAAkB;CACxE;AAGD,0CAA0C;AAC1C,SAAS,kBAAkB,EACvB,UAAU,EACV,OAAO,EACP,SAAS,EACT,OAAO,EACc;;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,cAAc;QAChB,QAAQ;QACR;IACJ;IAEA,MAAM,gBAAgB;QAClB,UAAU,WAAW,EAAE;QACvB;IACJ;IAEA,qBACI,6LAAC,sIAAA,CAAA,iBAAc;QAAC,WAAU;QAAgC,MAAK;QAAM,OAAM;QAAQ,iBAAiB;kBAChG,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAE,WAAU;8BAAiC,WAAW,OAAO;;;;;;8BAChE,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAE,WAAU;sCAAyC,WAAW,eAAe;;;;;;sCAChF,6LAAC;4BAAE,WAAU;sCAAsC,WAAW,UAAU;;;;;;;;;;;;8BAE5E,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAQ;4BAAQ,SAAS;sCACtC,EAAE;;;;;;sCAEP,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAS;;8CACvB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAtCS;;QAMS,sIAAA,CAAA,UAAO;;;KANhB;AAwCT,SAAS,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAA0B;;IAClE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,qBACI,6LAAC,sIAAA,CAAA,iBAAc;QAAC,WAAU;QAAgC,MAAK;QAAM,OAAM;QAAQ,iBAAiB;kBAChG,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB,EAAE;;;;;;;8BAEP,6LAAC;oBAAE,WAAU;8BAAiC,OAAO,OAAO;;;;;;8BAC5D,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAE,WAAU;;0CACT,6LAAC;gCAAK,WAAU;;oCAAiB,EAAE;oCAAuB;;;;;;;4BAAS;0CACnE,6LAAC;gCAAE,MAAM,OAAO,UAAU;gCAAE,QAAO;gCAAS,KAAI;gCAAsB,WAAU;0CAC3E,OAAO,UAAU;;;;;;;;;;;;;;;;;8BAI9B,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAQ,SAAS;kCACtC,EAAE;;;;;;;;;;;;;;;;;;;;;;AAM3B;IA3BS;;QACS,sIAAA,CAAA,UAAO;;;MADhB;AA8BF,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,YACzC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;;IACxJ,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACrF,MAAM,qBAAqB,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB;IAE3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK;IAEjE,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6DAAE,IAClC,qBAAqB,CAAC,mBAAmB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,QAAQ,mBAAmB,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;4DACpH;QAAC;KAAmB;IAGtB,MAAM,cAAc,EAAE;IACtB,MAAM,eAAe,CAAC;QACpB,SAAS,MAAM,MAAM,CAAC,KAAK;IAC7B;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,YAAY,OAAO,EAAE;YACrB,MAAM,SAAS,MAAM,aAAa;YAClC,YAAY,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;YAChD,YAAY,OAAO,CAAC,UAAU,GAAG,OAAO,UAAU;YAElD,yCAAyC;YACzC,sBAAsB;gBACpB,IAAI,YAAY,OAAO,IAAI,QAAQ;oBACjC,YAAY,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;oBAChD,YAAY,OAAO,CAAC,UAAU,GAAG,OAAO,UAAU;gBACpD;YACF;QACJ;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,YAAY,OAAO;YAChF,MAAM,eAAe,YAAY,SAAS,CAAC,gBAAgB;YAE3D,IAAI,mBAAmB,gBAAgB,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;gBACrE,sBAAsB;oBAAE,MAAM;oBAAc,OAAO;oBAAgB,KAAK;gBAAa;YACvF,OAAO;gBACL,sBAAsB;gBACtB,mBAAmB;YACrB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACjC,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,IAAI,CAAC,IAAI,MAAM,uBAAuB;gBACnF;YACF;YACA,gBAAgB;YAChB,iBAAiB;YACjB,IAAI;gBACF,MAAM,QAA2B;oBAC/B,cAAc,mBAAmB,IAAI;oBACrC,aAAa;oBACb,MAAM;oBACN,OAAO;gBACT;gBACA,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE;gBAClC,IAAI,CAAC,UAAU,OAAO,OAAO,aAAa,KAAK,aAAa;oBAC1D,MAAM,IAAI,MAAM;gBAClB;gBACA,iBAAiB,OAAO,aAAa;gBACrC,MAAM;oBAAE,UAAU;oBAAwB,gBAAgB;gBAA4B;YACxF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAAsB,SAAS;gBAAc;gBAClG,mBAAmB;YACrB,SAAU;gBACR,gBAAgB;YAClB;QACF;yDAAG;QAAC;QAAoB;QAAO;QAAa;QAAO;KAAsB;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,mBAAmB,sBAAsB,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,cAAc;gBACpG;YACJ;QACF,uDAAuD;QACvD;uCAAG;QAAC;QAAiB;QAAoB;QAAe;QAAc;QAAgB;KAAsB;IAG5G,MAAM,qBAAqB;QACzB,IAAI,sBAAsB,eAAe;YACvC,MAAM,WACJ,MAAM,SAAS,CAAC,GAAG,mBAAmB,KAAK,IAC3C,gBACA,MAAM,SAAS,CAAC,mBAAmB,GAAG;YACxC,SAAS;YACT,mBAAmB;YACnB,sBAAsB;YACtB,iBAAiB;YACjB,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,MAAM,oBAAoB,mBAAmB,KAAK,GAAG,cAAc,MAAM;gBACzE,WAAW;oBACT,IAAI,YAAY,OAAO,EAAE;wBACvB,YAAY,OAAO,CAAC,iBAAiB,CAAC,mBAAmB;oBAC3D;gBACF,GAAG;YACL;QACF;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,kBAAkB,WAAW,UAAU,EAAE,WAAW,eAAe,EAAE,WAAW,UAAU,EAAE,WAAW,QAAQ;IACjH;IAEA,MAAM,6BAA6B,CAAC;QAClC,IAAI,oBAAoB;YACtB,MAAM,WACJ,MAAM,SAAS,CAAC,GAAG,mBAAmB,KAAK,IAC3C,cACA,MAAM,SAAS,CAAC,mBAAmB,GAAG;YACxC,SAAS;YAET,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;gBAA4B,mBAAmB;oBAAE,MAAM;gBAAY;YAAE;YAE5H,mBAAmB;YACnB,sBAAsB;YAEtB,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,MAAM,oBAAoB,mBAAmB,KAAK,GAAG,YAAY,MAAM;gBACvE,WAAW;oBACT,IAAG,YAAY,OAAO,EAAE;wBACtB,YAAY,OAAO,CAAC,iBAAiB,CAAC,mBAAmB;oBAC3D;gBACF,GAAG;YACL;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,YAAY,OAAO;QACpC,IAAI,CAAC,UAAU;QAEf,IAAI,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG;QAEvC,mDAAmD;QACnD,IAAI,eAAe,kBAAkB,eAAe,kBAAkB;YACpE,IAAI,iBAAiB,MAAM,WAAW,CAAC,MAAM,iBAAiB,KAAK;YAEnE,sDAAsD;YACtD,IAAI,mBAAmB,cAAc;gBACjC,eAAe,MAAM,OAAO,CAAC,MAAM;gBACnC,IAAI,iBAAiB,CAAC,GAAG;oBACrB,eAAe,MAAM,MAAM;gBAC/B;YACJ;YAEA,IAAI,eAAe,MAAM,OAAO,CAAC,MAAM,eAAe;YACtD,IAAI,iBAAiB,CAAC,KAAK,eAAe,gBAAgB;gBACxD,eAAe,MAAM,MAAM;YAC7B;YAEA,MAAM,eAAe,MAAM,SAAS,CAAC,gBAAgB;YACrD,MAAM,QAAQ,aAAa,KAAK,CAAC;YACjC,IAAI;YACJ,IAAI,SAAS;YAEb,IAAI,eAAe,gBAAgB;gBAC/B,SAAS,MAAM,KAAK,CAAC,CAAA,OAAQ,cAAc,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;gBAChF,IAAI,QAAQ;oBACR,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,gBAAgB;gBACpE,OAAO;oBACH,IAAI,UAAU;oBACd,iBAAiB,MAAM,GAAG,CAAC,CAAA;wBACvB,MAAM,cAAc,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ;wBAChD,OAAO,cAAc,GAAG,UAAU,EAAE,EAAE,aAAa,GAAG;oBAC1D;gBACJ;YACJ,OAAO;gBACH,SAAS,MAAM,KAAK,CAAC,CAAA,OAAQ,UAAU,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;gBAC5E,IAAI,QAAQ;oBACR,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,YAAY;gBAChE,OAAO;oBACH,iBAAiB,MAAM,GAAG,CAAC,CAAA;wBACvB,MAAM,cAAc,KAAK,IAAI,GAAG,OAAO,CAAC,YAAY;wBACpD,OAAO,cAAc,CAAC,EAAE,EAAE,aAAa,GAAG;oBAC9C;gBACJ;YACJ;YAEA,MAAM,iBAAiB,eAAe,IAAI,CAAC;YAC3C,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,kBAAkB,iBAAiB,MAAM,SAAS,CAAC;YACvF,SAAS;YAET,WAAW;gBACP,SAAS,KAAK;gBACd,SAAS,iBAAiB,CAAC,gBAAgB,iBAAiB,eAAe,MAAM;YACrF,GAAG;QAEL,OAAO;YACL,MAAM,eAAe,MAAM,SAAS,CAAC,gBAAgB;YACrD,IAAI,CAAC,cAAc;YAEnB,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAQ,gBAAgB,CAAC,EAAE,EAAE,aAAa,EAAE,CAAC;oBAAE;gBACpD,KAAK;oBAAU,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;oBAAE;gBACpD,KAAK;oBAAa,gBAAgB,CAAC,GAAG,EAAE,aAAa,IAAI,CAAC;oBAAE;gBAC5D;oBAAS;YACX;YAEA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,kBAAkB,gBAAgB,MAAM,SAAS,CAAC;YACtF,SAAS;YAET,WAAW;gBACP,SAAS,KAAK;gBACd,MAAM,eAAe,iBAAiB,cAAc,MAAM;gBAC1D,SAAS,iBAAiB,CAAC,cAAc;YAC7C,GAAG;QACL;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,QAAkC;YAAC;SAAK;QAE5C,MAAM,eAAe,CAAC,OAAe,KAA4B;YAC7D,QAAQ,MAAM,OAAO,CAAC,CAAC,MAAM;gBACzB,IAAI,OAAO,SAAS,UAAU,OAAO;gBAErC,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,YAAsC,EAAE;gBAE9C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBACxC,IAAI,IAAI,MAAM,GAAG;wBACb,IAAG,UAAU,CAAC,EAAE,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC,EAAE;oBAClD,OAAO;wBACH,MAAM,MAAM;wBACZ,UAAU,IAAI,eAAC,6LAAC;sCAA4C,UAAU,CAAC,EAAE;2BAAhD,GAAG,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG;;;;;oBAC7D;gBACJ;gBACA,OAAO;YACX;QACJ;QAEA,aAAa,uBAAuB,UAAU;QAC9C,aAAa,mBAAmB,MAAM;QACtC,aAAa,uBAAuB,KAAK;QAEzC,OAAO;IACX;IAEE,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2DAAE;YAClC,MAAM,iBAAiB;mBAAI;mBAAgB;aAAkB;YAC7D,OAAO,eAAe,IAAI;mEAAC,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;;QAC/E;0DAAG;QAAC;QAAa;KAAkB;IAEnC,MAAM,uBAAuB,CAAC;QAC1B,oBAAoB;IACxB;IACA,MAAM,yBAAyB;QAC3B,oBAAoB;IACxB;IAEA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YACjC,IAAI,CAAC,OAAO,qBAAO,6LAAC;0BAAE;;;;;sBAAY,iDAAiD;YAEnF,MAAM,oBAAoB;YAE1B,IAAI,YAAY;YAChB,MAAM,WAAW,EAAE;YAEnB,kBAAkB,OAAO;kEAAC,CAAC;oBACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG;oBAE5D,IAAI,eAAe,aAAa,aAAa,aAAa,aAAa,WAAW;wBAC9E;oBACJ;oBAEA,6CAA6C;oBAC7C,IAAI,aAAa,WAAW;wBACxB,SAAS,IAAI,eAAC,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sCAA4B,oBAAoB,MAAM,KAAK,CAAC,WAAW;2BAAjE,CAAC,KAAK,EAAE,WAAW;;;;;oBAC1D;oBAEA,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;wBAC9C,uBAAuB,SAAS;wBAChC,sBAAsB,SAAS;wBAC/B,sBAAsB,SAAS;wBAC/B,oBAAoB,SAAS;wBAC7B,yBAAyB,SAAS;oBACtC;oBAEA,SAAS,IAAI,eACT,6LAAC,sIAAA,CAAA,UAAO;wBAAU,MAAM,kBAAkB,OAAO;wBAAI,YAAY;8EAAE,CAAC;gCAAU,IAAI,CAAC,MAAM;4BAAwB;;;0CAC7G,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACnB,cAAA,6LAAC;oCAAK,WAAW;oCAAgB,OAAO;0FAAE,IAAM,qBAAqB;;8CAChE,oBAAoB;;;;;;;;;;;4BAG5B,kBAAkB,OAAO,MAAM,CAC5B,SAAS,6BACL,6LAAC;gCACG,QAAQ;gCACR,SAAS;;;;;qDAGb,6LAAC;gCACG,YAAY;gCACZ,SAAS;gCACT,WAAW;gCACX,SAAS;;;;;oCAGrB;;uBApBU;;;;;oBAwBlB,YAAY;gBAChB;;YAEA,mDAAmD;YACnD,IAAI,YAAY,MAAM,MAAM,EAAE;gBAC1B,SAAS,IAAI,eAAC,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;8BAA4B,oBAAoB,MAAM,KAAK,CAAC;mBAAtD,CAAC,KAAK,EAAE,WAAW;;;;;YAC1D;YAEA,wEAAwE;YACxE,MAAM,gBAAgB,EAAE;YACxB,IAAI,aAAqC,EAAE;YAE3C,SAAS,OAAO;kEAAC,CAAC;oBACd,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,KAAK,UAAU;wBACvC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ;wBAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;wBACzB,MAAM,OAAO;8EAAC,CAAC,MAAc;gCACzB,IAAG,MAAM,WAAW,IAAI,CAAC;gCACzB,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;oCAC9B,cAAc,IAAI,eAAC,6LAAC;kDAAwC,WAAW,MAAM,GAAG,IAAI,2BAAa;sDAAE;;uCAAxE,CAAC,KAAK,EAAE,cAAc,MAAM,EAAE;;;;;oCACzD,aAAa,EAAE;gCACnB;4BACJ;;oBACJ,OAAO;wBACH,WAAW,IAAI,CAAC;oBACpB;gBACJ;;YAEA,IAAI,WAAW,MAAM,GAAG,GAAG;gBACvB,cAAc,IAAI,eAAC,6LAAC;8BAAwC;mBAAjC,CAAC,KAAK,EAAE,cAAc,MAAM,EAAE;;;;;YAC7D;YAEA,IAAI,cAAc,MAAM,KAAK,KAAK,UAAU,IAAI;gBAC9C,qBAAO,6LAAC;8BAAE;;;;;;YACZ;YAEA,qBAAO;0BAAG;;QACZ,uDAAuD;QACvD;yDAAG;QAAC;QAAO;QAAqB;QAAqB;KAAiB;IAEtE,MAAM,kBAAkB;QACtB,SAAS;QACT,MAAM;YAAE,UAAU;YAAqB,gBAAgB;QAA4B;IACrF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO;YACV,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAgC,SAAS;YAAc;YAC5G;QACF;QACA,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAkC;QAC3F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA+B,SAAS;YAAc;QAC7G;IACF;IAEA,MAAM,eAAoC;QACxC;QACA,UAAU;QACV,YAAY;QACZ,eAAe;QACf,SAAS;QACT,WAAW;QACX,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,UAAU;QACV,WAAW;QACX,cAAc;IAChB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;QAAuB,KAAK;;0BAC1C,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEL,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS;wCAAQ,UAAU,CAAC;wCAAS,OAAO,EAAE;kDACpG,cAAA,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS;wCAAQ,UAAU,CAAC;wCAAS,OAAO,EAAE;kDACpG,cAAA,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAE5C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe;;kDACtC,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACrB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE7B,6LAAC,qIAAA,CAAA,gBAAa;kDACT,YAAY,GAAG,CAAC,CAAA,qBACb,6LAAC,qIAAA,CAAA,aAAU;gDAAkB,OAAO,KAAK,KAAK;gDAAE,OAAO;oDAAC,YAAY,KAAK,KAAK;gDAAA;0DACzE,KAAK,KAAK;+CADE,KAAK,KAAK;;;;;;;;;;;;;;;;0CAOvC,6LAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAE5C,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAS,OAAM;kDACjG,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAW,OAAM;kDACnG,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAc,OAAM;kDACtG,cAAA,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAiB,OAAM;kDAC1G,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,IAAM,aAAa;wCAAmB,OAAM;kDAC3G,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAO,WAAU;wCAAU,SAAS,KAAO;wCAAG,OAAM;kDAC/E,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKlC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACnB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCACL,GAAG,YAAY;gCACf,WAAW,cAAc,QAAQ,UAAU;gCAC3C,QAAQ;gCACR,QAAQ;gCACR,YAAY;4BACd;4BACA,KAAK;sCAEJ;;;;;;sCAEH,6LAAC,uIAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO;4BACP,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,aAAa;4BACb,WAAU;4BACV,OAAO;gCACL,GAAG,YAAY;gCACf,OAAO;gCACP,YAAY;gCACZ,WAAW,cAAc,QAAQ,UAAU;gCAC3C,QAAQ;gCACR,QAAQ;gCACR,YAAY;4BACd;4BACA,cAAY,EAAE;4BACd,KAAK;;;;;;;;;;;;;;;;;0BAIb,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,sIAAA,CAAA,UAAO;wBAAC,MAAM;wBAAiB,cAAc;;0CAC5C,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU,CAAC,sBAAsB;;sDAEjC,6LAAC,kNAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,EAAE;;;;;;;;;;;;4BAGN,sBAAsB,CACrB,sCACI,6LAAC,6JAAA,CAAA,qBAAkB;gCACf,cAAc,mBAAmB,IAAI;gCACrC,aAAa;gCACb,UAAU;gCACV,iBAAiB;;;;;qDAGrB,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAO,MAAK;gCAAM,OAAM;0CAClD,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4B,EAAE;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DACV,EAAE;;;;;;;;;;;;sDAIL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB,EAAE;;;;;;8DAC1C,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,mBAAmB,IAAI;oDAC9B,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAIf,8BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;wCAGtB,CAAC,gBAAgB,+BACf,6LAAC;4CAAI,WAAU;;8DACd,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA0B,EAAE;;;;;;8DAC3C,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS,IAAM,mBAAmB;8DAAS,EAAE;;;;;;8DAC/E,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,UAAU,gBAAgB,CAAC;8DAE1B,EAAE;;;;;;;;;;;;;;;;;;;;;;oCAMf;;;;;;;kCAGF,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,MAAM,MAAM,KAAK;wBAC3B,cAAY,EAAE;wBACd,OAAO,EAAE;;0CAET,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,EAAE;;;;;;;kCAGL,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,MAAM,MAAM,KAAK;wBAC3B,cAAY,EAAE;wBACd,OAAO,EAAE;;0CAET,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;;;;;;;;;;;;;AAKb;;QAplBwC,sIAAA,CAAA,UAAO;QAC3B,+HAAA,CAAA,WAAQ;QAGC,kIAAA,CAAA,cAAW;;;;QAJA,sIAAA,CAAA,UAAO;QAC3B,+HAAA,CAAA,WAAQ;QAGC,kIAAA,CAAA,cAAW;;;;AAklBxC,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 7280, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/suggestion-item.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport type { AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { Button } from '@/components/ui/button';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Lightbulb, PenTool, SpellCheck, Info, Check, X } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface SuggestionItemProps {\n  suggestion: AnalysisSuggestion;\n  onApply: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;\n  onDismiss: (suggestionId: string) => void;\n}\n\nconst getSuggestionTypeAppearance = (type: AnalysisSuggestion['type']): { color: string; icon: JSX.Element; labelKey: string } => {\n  switch (type) {\n    case 'spelling':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <SpellCheck className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeSpelling' };\n    case 'grammar':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeGrammar' };\n    case 'rewrite':\n      return { color: 'bg-blue-500 hover:bg-blue-600', icon: <Lightbulb className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeRewrite' };\n    case 'style':\n      return { color: 'bg-green-500 hover:bg-green-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeStyle' };\n    default:\n      return { color: 'bg-gray-500 hover:bg-gray-600', icon: <Info className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeUnknown' };\n  }\n};\n\n// This function renders a suggestion item with a given suggestion, onApply and onDismiss functions\nexport function SuggestionItem({ suggestion, onApply, onDismiss }: SuggestionItemProps) {\n  // Get the i18n instance\n  const { t } = useI18n();\n  // Get the appearance of the suggestion type\n  const { color, icon, labelKey } = getSuggestionTypeAppearance(suggestion.type);\n\n  // This function is called when the suggestion is applied\n  const handleApply = () => {\n    onApply(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);\n  };\n  \n  // This function is called when a suggestion is dismissed\n  const handleDismiss = () => {\n      // Call the onDismiss function passed as a prop, passing the suggestion id as an argument\n      onDismiss(suggestion.id);\n  };\n\n  return (\n    <div className=\"p-3 border-b last:border-b-0 bg-card hover:bg-muted/50 transition-colors\">\n      <div className=\"flex items-start justify-between gap-2\">\n        <div>\n          <div className=\"flex items-center mb-1\">\n            <Badge variant=\"default\" className={`text-xs text-white ${color} mr-2`}>\n              {icon}\n              {t(labelKey)}\n            </Badge>\n             <Popover>\n                <PopoverTrigger asChild>\n                  <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6 text-muted-foreground hover:text-foreground\">\n                    <Info className=\"h-3.5 w-3.5\" />\n                    <span className=\"sr-only\">{t('suggestionExplanationTooltip')}</span>\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-72 text-sm p-3\" side=\"top\" align=\"start\">\n                  {suggestion.message}\n                </PopoverContent>\n              </Popover>\n          </div>\n          <p className=\"text-sm text-muted-foreground mb-1\">\n            <span className=\"italic\">\"{suggestion.originalSegment}\"</span>\n          </p>\n          <p className=\"text-sm font-semibold text-primary\">\n            {t('suggestionLabel')}: <span className=\"font-normal text-foreground\">{suggestion.suggestion}</span>\n          </p>\n        </div>\n        <div className=\"flex items-center gap-1 mt-1 shrink-0\">\n            <Button onClick={handleApply} size=\"sm\" variant=\"outline\" title={t('correctButton')}>\n                <Check className=\"mr-1.5 h-3.5 w-3.5\" />\n                {t('correctButton')}\n            </Button>\n            <Button onClick={handleDismiss} size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" title={t('dismissButton')}>\n                <X className=\"h-4 w-4\" />\n            </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n    "], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;AAeA,MAAM,8BAA8B,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAyB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACrI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,6LAAC,+MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAmC,oBAAM,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAsB;QACvI;YACE,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;IACtI;AACF;AAGO,SAAS,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAuB;;IACpF,wBAAwB;IACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,4CAA4C;IAC5C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,4BAA4B,WAAW,IAAI;IAE7E,yDAAyD;IACzD,MAAM,cAAc;QAClB,QAAQ,WAAW,UAAU,EAAE,WAAW,eAAe,EAAE,WAAW,UAAU,EAAE,WAAW,QAAQ;IACvG;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QAClB,yFAAyF;QACzF,UAAU,WAAW,EAAE;IAC3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAW,CAAC,mBAAmB,EAAE,MAAM,KAAK,CAAC;;wCACnE;wCACA,EAAE;;;;;;;8CAEJ,6LAAC,sIAAA,CAAA,UAAO;;sDACL,6LAAC,sIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;;kEAC5C,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAW,EAAE;;;;;;;;;;;;;;;;;sDAGjC,6LAAC,sIAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAmB,MAAK;4CAAM,OAAM;sDAC3D,WAAW,OAAO;;;;;;;;;;;;;;;;;;sCAI3B,6LAAC;4BAAE,WAAU;sCACX,cAAA,6LAAC;gCAAK,WAAU;;oCAAS;oCAAE,WAAW,eAAe;oCAAC;;;;;;;;;;;;sCAExD,6LAAC;4BAAE,WAAU;;gCACV,EAAE;gCAAmB;8CAAE,6LAAC;oCAAK,WAAU;8CAA+B,WAAW,UAAU;;;;;;;;;;;;;;;;;;8BAGhG,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,MAAK;4BAAK,SAAQ;4BAAU,OAAO,EAAE;;8CAC/D,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,EAAE;;;;;;;sCAEP,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAe,MAAK;4BAAO,SAAQ;4BAAQ,WAAU;4BAAU,OAAO,EAAE;sCACrF,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B;GAzDgB;;QAEA,sIAAA,CAAA,UAAO;;;KAFP", "debugId": null}}, {"offset": {"line": 7580, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/writing-suggestions-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from \"@/components/ui/card\";\nimport { Loader2, <PERSON><PERSON>ircle, AlertTriangle } from \"lucide-react\";\nimport { type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { SuggestionItem } from './suggestion-item';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport React from 'react';\n\ninterface WritingSuggestionsPanelProps {\n  suggestions: AnalysisSuggestion[];\n  isAnalyzing: boolean;\n  onApplySuggestion: (\n    suggestionText: string,\n    originalSegment: string,\n    startIndex?: number,\n    endIndex?: number\n  ) => void;\n  onDismissSuggestion: (suggestionId: string) => void;\n}\n\nexport function WritingSuggestionsPanel({ \n  suggestions, \n  isAnalyzing, \n  onApplySuggestion, \n  onDismissSuggestion\n}: WritingSuggestionsPanelProps) {\n  const { t } = useI18n();\n\n  const hasSuggestions = suggestions.length > 0;\n  \n  const descriptionKey = isAnalyzing \n    ? 'analyzingTextDescription' \n    : hasSuggestions \n    ? 'suggestionsFoundDescription' \n    : 'startTypingForSuggestionsDescription';\n\n  return (\n    <Card className=\"h-full flex flex-col\">\n      <CardHeader className=\"p-3 border-b sticky top-0 bg-card z-10\">\n        <CardTitle className=\"text-base flex items-center\">\n            {isAnalyzing && <Loader2 className=\"h-3.5 w-3.5 animate-spin mr-2\" />}\n            {!isAnalyzing && hasSuggestions && <CheckCircle className=\"h-3.5 w-3.5 text-primary mr-2\" />}\n            {!isAnalyzing && !hasSuggestions && <AlertTriangle className=\"h-3.5 w-3.5 text-muted-foreground mr-2\" />}\n            {t('writingSuggestionsTitle')}\n        </CardTitle>\n        <CardDescription className=\"text-xs\">\n            {t(descriptionKey, { count: suggestions.length.toString() })}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"flex-grow p-0 relative\">\n        <div className=\"absolute inset-0\">\n          <ScrollArea className=\"h-full w-full\">\n            <div className=\"p-1\">\n              {hasSuggestions ? (\n                suggestions.map((suggestion) => (\n                  <SuggestionItem\n                    key={suggestion.id}\n                    suggestion={suggestion}\n                    onApply={onApplySuggestion}\n                    onDismiss={onDismissSuggestion}\n                  />\n                ))\n              ) : !isAnalyzing ? (\n                <div className=\"p-4 text-sm text-muted-foreground text-center\">{t('startTypingForSuggestionsDescription')}</div>\n              ) : null}\n            </div>\n          </ScrollArea>\n        </div>\n      </CardContent>\n\n      {hasSuggestions && (\n        <CardFooter className=\"p-2 border-t text-xs text-muted-foreground justify-center sticky bottom-0 bg-card z-10\">\n          {t('suggestionsFoundDescription', { count: suggestions.length.toString() })}\n        </CardFooter>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAEA;AACA;AACA;;;AAPA;;;;;;AAsBO,SAAS,wBAAwB,EACtC,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACU;;IAC7B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,iBAAiB,YAAY,MAAM,GAAG;IAE5C,MAAM,iBAAiB,cACnB,6BACA,iBACA,gCACA;IAEJ,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;4BAChB,6BAAe,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClC,CAAC,eAAe,gCAAkB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACzD,CAAC,eAAe,CAAC,gCAAkB,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAC5D,EAAE;;;;;;;kCAEP,6LAAC,mIAAA,CAAA,kBAAe;wBAAC,WAAU;kCACtB,EAAE,gBAAgB;4BAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;wBAAG;;;;;;;;;;;;0BAIhE,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;sCACZ,iBACC,YAAY,GAAG,CAAC,CAAC,2BACf,6LAAC,qJAAA,CAAA,iBAAc;oCAEb,YAAY;oCACZ,SAAS;oCACT,WAAW;mCAHN,WAAW,EAAE;;;;4CAMpB,CAAC,4BACH,6LAAC;gCAAI,WAAU;0CAAiD,EAAE;;;;;uCAChE;;;;;;;;;;;;;;;;;;;;;YAMX,gCACC,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,EAAE,+BAA+B;oBAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;gBAAG;;;;;;;;;;;;AAKnF;GA1DgB;;QAMA,sIAAA,CAAA,UAAO;;;KANP", "debugId": null}}, {"offset": {"line": 7735, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/stats/writing-statistics.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from \"@/components/ui/card\";\nimport { Activity, ListOrdered, BarChartHorizontalBig } from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\ninterface WritingStatisticsProps {\n  wordCount: number;\n  charCount: number;\n  writingScore: number;\n}\n\nexport function WritingStatistics({ wordCount, charCount, writingScore }: WritingStatisticsProps) {\n  const { t } = useI18n();\n  return (\n    <Card>\n      <CardHeader className=\"p-3 border-b\">\n          <CardTitle className=\"flex items-center text-base\">\n          <Activity className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('writingStatsTitle')}\n          </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-2 p-3\">\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <ListOrdered className=\"mr-2 h-3.5 w-3.5\" /> {t('wordCountLabel')}\n          </span>\n          <span className=\"font-medium\">{wordCount}</span>\n          </div>\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <BarChartHorizontalBig className=\"mr-2 h-3.5 w-3.5\" /> {t('charCountLabel')}\n          </span>\n          <span className=\"font-medium\">{charCount}</span>\n          </div>\n          <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-muted-foreground flex items-center\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"mr-2 h-3.5 w-3.5 lucide lucide-award\"><circle cx=\"12\" cy=\"8\" r=\"6\"/><path d=\"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\"/></svg>\n              {t('writingScoreLabel')}\n          </span>\n          <span className=\"font-medium\">{writingScore} {t('writingScoreUnit')}</span>\n          </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAYO,SAAS,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAA0B;;IAC9F,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACpB,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BAClB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACrB,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,EAAE;;;;;;;;;;;;0BAGP,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACnB,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAK,WAAU;;kDACZ,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAqB;oCAAE,EAAE;;;;;;;0CAEpD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAK,WAAU;;kDACZ,6LAAC,qOAAA,CAAA,wBAAqB;wCAAC,WAAU;;;;;;oCAAqB;oCAAE,EAAE;;;;;;;0CAE9D,6LAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAK,WAAU;;kDACZ,6LAAC;wCAAI,OAAM;wCAA6B,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAO,QAAO;wCAAe,aAAY;wCAAI,eAAc;wCAAQ,gBAAe;wCAAQ,WAAU;;0DAAuC,6LAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAI,GAAE;;;;;;0DAAK,6LAAC;gDAAK,GAAE;;;;;;;;;;;;oCACxQ,EAAE;;;;;;;0CAEP,6LAAC;gCAAK,WAAU;;oCAAe;oCAAa;oCAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAK1D;GAjCgB;;QACA,sIAAA,CAAA,UAAO;;;KADP", "debugId": null}}, {"offset": {"line": 7947, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/shared/document-dropzone.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport type { DragEvent, ChangeEvent } from 'react';\nimport { useState, useRef, useCallback } from 'react';\nimport { UploadCloud } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface DocumentDropzoneProps {\n  onTextExtracted: (textContent: string) => void;\n  className?: string;\n}\n\nexport function DocumentDropzone({ onTextExtracted, className }: DocumentDropzoneProps) {\n  const [isDragActive, setIsDragActive] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const processFile = useCallback((file: File | null) => {\n    if (!file) return;\n\n    if (file.type !== \"text/plain\" && !file.name.endsWith(\".txt\")) {\n      toast({\n        titleKey: \"toastErrorTitle\",\n        descriptionKey: \"toastInvalidFileTypeMessage\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const textContent = e.target?.result;\n      if (typeof textContent === 'string') {\n        onTextExtracted(textContent);\n        toast({\n          titleKey: \"toastFileImportSuccessTitle\",\n          descriptionKey: \"toastFileImportSuccessMessage\",\n        });\n      } else {\n        toast({\n          titleKey: \"toastFileImportErrorTitle\",\n          descriptionKey: \"toastFileImportErrorMessage\",\n          variant: \"destructive\",\n        });\n      }\n    };\n    reader.onerror = () => {\n      toast({\n        titleKey: \"toastFileImportErrorTitle\",\n        descriptionKey: \"toastFileImportErrorMessage\",\n        variant: \"destructive\",\n      });\n    };\n    reader.readAsText(file);\n  }, [onTextExtracted, toast, t]);\n\n  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(true);\n  };\n\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(false);\n  };\n\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!isDragActive) setIsDragActive(true); // Ensure active state on drag over\n  };\n\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      processFile(e.dataTransfer.files[0]);\n    }\n  };\n\n  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      processFile(e.target.files[0]);\n      e.target.value = \"\"; // Reset file input\n    }\n  };\n\n  const openFileDialog = () => {\n    inputRef.current?.click();\n  };\n\n  return (\n    <div\n      onClick={openFileDialog}\n      onDrop={handleDrop}\n      onDragOver={handleDragOver}\n      onDragEnter={handleDragEnter}\n      onDragLeave={handleDragLeave}\n      className={cn(\n        \"border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 ease-in-out\",\n        isDragActive ? \"border-primary bg-primary/10\" : \"border-border hover:border-primary/70 hover:bg-muted/50\",\n        className\n      )}\n      role=\"button\"\n      tabIndex={0}\n      onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') openFileDialog();}}\n      aria-label={t('dropzoneAriaLabel')}\n    >\n      <input\n        ref={inputRef}\n        type=\"file\"\n        accept=\".txt,text/plain\"\n        className=\"hidden\"\n        onChange={handleFileInputChange}\n        data-testid=\"file-upload-input-dropzone\"\n      />\n      <UploadCloud className={cn(\"mx-auto h-8 w-8 mb-3\", isDragActive ? \"text-primary\" : \"text-muted-foreground\")} />\n      <p className=\"text-sm text-muted-foreground\">\n        {t('dropzoneInstruction')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AAcO,SAAS,iBAAiB,EAAE,eAAe,EAAE,SAAS,EAAyB;;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC/B,IAAI,CAAC,MAAM;YAEX,IAAI,KAAK,IAAI,KAAK,gBAAgB,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC7D,MAAM;oBACJ,UAAU;oBACV,gBAAgB;oBAChB,SAAS;gBACX;gBACA;YACF;YAEA,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM;6DAAG,CAAC;oBACf,MAAM,cAAc,EAAE,MAAM,EAAE;oBAC9B,IAAI,OAAO,gBAAgB,UAAU;wBACnC,gBAAgB;wBAChB,MAAM;4BACJ,UAAU;4BACV,gBAAgB;wBAClB;oBACF,OAAO;wBACL,MAAM;4BACJ,UAAU;4BACV,gBAAgB;4BAChB,SAAS;wBACX;oBACF;gBACF;;YACA,OAAO,OAAO;6DAAG;oBACf,MAAM;wBACJ,UAAU;wBACV,gBAAgB;wBAChB,SAAS;oBACX;gBACF;;YACA,OAAO,UAAU,CAAC;QACpB;oDAAG;QAAC;QAAiB;QAAO;KAAE;IAE9B,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,CAAC,cAAc,gBAAgB,OAAO,mCAAmC;IAC/E;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB;QAChB,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QACrC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC7B,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,mBAAmB;QAC1C;IACF;IAEA,MAAM,iBAAiB;QACrB,SAAS,OAAO,EAAE;IACpB;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+GACA,eAAe,iCAAiC,2DAChD;QAEF,MAAK;QACL,UAAU;QACV,WAAW,CAAC;YAAQ,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;QAAiB;QAC7E,cAAY,EAAE;;0BAEd,6LAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,WAAU;gBACV,UAAU;gBACV,eAAY;;;;;;0BAEd,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,eAAe,iBAAiB;;;;;;0BACnF,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX;GAlHgB;;QAGI,+HAAA,CAAA,WAAQ;QACZ,sIAAA,CAAA,UAAO;;;KAJP", "debugId": null}}, {"offset": {"line": 8114, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/language-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect the language of a given text.\n *\n * - detectLanguage - A function that handles the language detection process.\n * - DetectLanguageInput - The input type for the detectLanguage function.\n * - DetectLanguageOutput - The return type for the detectLanguage function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport {z} from 'genkit';\n\nconst DetectLanguageInputSchema = z.object({\n  text: z.string().describe('The text to analyze for language detection.'),\n});\nexport type DetectLanguageInput = z.infer<typeof DetectLanguageInputSchema>;\n\nconst DetectLanguageOutputSchema = z.object({\n  languageCode: z\n    .string()\n    .describe(\n      \"The detected ISO 639-1 language code (e.g., 'en', 'es', 'fr'). Should be 'unknown' if not confident.\"\n    ),\n});\nexport type DetectLanguageOutput = z.infer<typeof DetectLanguageOutputSchema>;\n\nexport async function detectLanguage(input: DetectLanguageInput): Promise<DetectLanguageOutput> {\n  return languageDetectionFlow(input);\n}\n\nconst supportedLanguageCodes = APP_WRITING_LANGUAGES.map(lang => lang.value).join(', ');\n\nconst languageDetectionPrompt = ai.definePrompt({\n  name: 'languageDetectionPrompt',\n  input: {schema: DetectLanguageInputSchema},\n  output: {schema: DetectLanguageOutputSchema},\n  prompt: `Analyze the following text and determine its primary language. Respond with the ISO 639-1 code for the detected language (e.g., 'en', 'es', 'fr'). If the language is not clear or the text is too short, respond with the string 'unknown'. Do not provide any explanation, only the language code or 'unknown'. Supported language codes are: ${supportedLanguageCodes}.\n\nText to analyze:\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n`,\n});\n\nconst languageDetectionFlow = ai.defineFlow(\n  {\n    name: 'languageDetectionFlow',\n    inputSchema: DetectLanguageInputSchema,\n    outputSchema: DetectLanguageOutputSchema,\n  },\n  async (input: DetectLanguageInput): Promise<DetectLanguageOutput> => {\n    try {\n      const {output} = await languageDetectionPrompt(input);\n      if (!output || typeof output.languageCode !== 'string') {\n        const errorMessage = 'AI model did not return the expected languageCode string.';\n        console.error(`[languageDetectionFlow] - ${errorMessage} For input:`, input, 'Output received:', output);\n        return { languageCode: 'unknown' };\n      }\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[languageDetectionFlow] - Error during flow execution for input:`, input, error);\n      return { languageCode: 'unknown' };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IA4BsB", "debugId": null}}, {"offset": {"line": 8126, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-analysis-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent that analyzes text for grammar, spelling, punctuation, style, and clarity, providing suggestions.\n *\n * - analyzeText - A function that handles the text analysis process.\n * - TextAnalysisInput - The input type for the analyzeText function.\n * - TextAnalysisOutput - The return type for the analyzeText function.\n * - AnalysisSuggestion - The structure for individual suggestions.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst AnalysisSuggestionSchema = z.object({\n  id: z.string().describe('A unique identifier for the suggestion.'),\n  type: z\n    .enum(['spelling', 'grammar', 'rewrite', 'style'])\n    .describe(\n      \"The type of issue: 'spelling', 'grammar' (including punctuation), 'rewrite' (for clarity/flow), or 'style'.\"\n    ),\n  message: z.string().describe('A brief, user-friendly explanation of the issue and why the suggestion improves the text.'),\n  suggestion: z.string().describe('The corrected text segment.'),\n  originalSegment: z.string().describe('The original text segment that the suggestion refers to. This helps the user locate the issue if start/end indices are not perfectly accurate or for display purposes.'),\n  startIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based starting character index of the problematic segment in the original text. This can be approximate if the exact segment is hard to determine precisely.'\n    ),\n  endIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based ending character index (exclusive) of the problematic segment in the original text. This can be approximate.'\n    ),\n});\nexport type AnalysisSuggestion = z.infer<typeof AnalysisSuggestionSchema>;\n\nconst TextAnalysisInputSchema = z.object({\n  text: z.string().describe('The text to analyze.'),\n  language: z.string().describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\", \"ar\"). This helps tailor the analysis to the specific language.'),\n});\nexport type TextAnalysisInput = z.infer<typeof TextAnalysisInputSchema>;\n\nconst TextAnalysisOutputSchema = z.object({\n  suggestions: z\n    .array(AnalysisSuggestionSchema)\n    .describe('A list of suggestions for the input text.'),\n});\nexport type TextAnalysisOutput = z.infer<typeof TextAnalysisOutputSchema>;\n\nexport async function analyzeText(\n  input: TextAnalysisInput\n): Promise<TextAnalysisOutput> {\n  return textAnalysisFlow(input);\n}\n\nconst textAnalysisPrompt = ai.definePrompt({\n  name: 'textAnalysisPrompt',\n  input: {schema: TextAnalysisInputSchema},\n  output: {schema: TextAnalysisOutputSchema},\n  prompt: `You are an expert writing assistant. Your goal is to help users make their writing clear, natural, and human-sounding. The text you will analyze is in the language specified by the 'language' input field (e.g., 'en' for English, 'es' for Spanish, 'ar' for Arabic).\nYour task is to identify issues and provide suggestions for correction tailored to the grammar, spelling, punctuation, style, and clarity rules of that specific language ({{language}}).\n\nFor each issue you find, you MUST provide:\n1.  \\`id\\`: A unique string identifier for this specific suggestion (e.g., \"suggestion-1\", \"suggestion-2\").\n2.  \\`type\\`: The category of the issue. Must be one of:\n    *   'spelling': For misspelled words.\n    *   'grammar': For grammatical and punctuation errors (e.g., subject-verb agreement, tense, articles, comma usage, apostrophes).\n    *   'rewrite': For sentences that are grammatically correct but could be rewritten for better flow and impact. Focus on varying sentence structure, improving transitions, and ensuring the new phrasing connects logically with the surrounding text. Only suggest a rewrite if it provides a significant improvement; do not suggest rewrites for sentences that are already well-structured.\n    *   'style': Your primary focus for style is to enhance vocabulary by replacing weak, generic, or repeated verbs with more dynamic, vivid, and engaging alternatives. Introduce natural-sounding phrasal verbs where appropriate to make the text less robotic. Only provide 'style' suggestions for verbs. For example, instead of \"walked quickly,\" suggest \"dashed\"; instead of repeating \"said,\" suggest \"murmured\" or \"exclaimed.\"\n3.  \\`message\\`: A brief, user-friendly explanation of the issue and why your suggestion improves the text.\n4.  \\`suggestion\\`: The corrected or improved text segment.\n5.  \\`originalSegment\\`: The exact original text segment that this suggestion pertains to. This is crucial for the user to understand the context.\n6.  \\`startIndex\\` (optional): The 0-based starting character index of the 'originalSegment' in the *entire* provided text. This can be approximate if the exact segment is hard to determine precisely.\n7.  \\`endIndex\\` (optional): The 0-based ending character index (exclusive) of the 'originalSegment' in the *entire* provided text. This can be approximate.\n\nImportant Guidelines:\n- Focus on providing actionable and clear suggestions that make the writing feel more natural and human.\n- The 'style' and 'rewrite' suggestions are very important. Actively look for opportunities to make the language more powerful, engaging, and less robotic.\n- For 'rewrite' and 'style' suggestions, ensure the \\`originalSegment\\` captures enough context (e.g., a full sentence for rewrites, or a specific word/phrase for style).\n- Be conservative with \\`startIndex\\` and \\`endIndex\\`. If you cannot determine them with high confidence, it's better to rely on \\`originalSegment\\`.\n- If the text is perfect and has no issues, return an empty array for 'suggestions'.\n\nAnalyze the following text (language: {{language}}):\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n\nRespond with a JSON object containing a 'suggestions' array.\nExample for a single suggestion:\n{\n  \"suggestions\": [\n    {\n      \"id\": \"s1\",\n      \"type\": \"spelling\",\n      \"message\": \"'Helo' appears to be a misspelling of 'Hello'.\",\n      \"suggestion\": \"Hello\",\n      \"originalSegment\": \"Helo\",\n      \"startIndex\": 0,\n      \"endIndex\": 4\n    }\n  ]\n}\nIf multiple issues, add more objects to the 'suggestions' array.\n`,\n});\n\nconst textAnalysisFlow = ai.defineFlow(\n  {\n    name: 'textAnalysisFlow',\n    inputSchema: TextAnalysisInputSchema,\n    outputSchema: TextAnalysisOutputSchema,\n  },\n  async (input: TextAnalysisInput): Promise<TextAnalysisOutput> => {\n    if (!input.text.trim()) {\n      return {suggestions: []};\n    }\n    try {\n      const {output} = await textAnalysisPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      // We still handle the case where the model returns an empty suggestions array.\n      const suggestionsWithUniqueIds = (output?.suggestions || []).map((s, index) => ({\n        ...s,\n        id: s.id || `suggestion-${Date.now()}-${index}`, // Ensure unique ID\n      }));\n      return { suggestions: suggestionsWithUniqueIds };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[textAnalysisFlow] - Error: ${errorMessage}`, {input});\n       // For background tasks like real-time analysis, it's better to fail gracefully\n      // and return an empty result than to crash the UI with an error for a transient issue.\n      return { suggestions: [] };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;IAoDsB", "debugId": null}}, {"offset": {"line": 8138, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/lib/analysis/local-verb-repetition.ts"], "sourcesContent": ["import type { AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\n\n// Very lightweight English-only verb repetition detector with simple lemmatization\n// Emits green (style) suggestions for repeated common verbs and proposes dynamic alternatives\n\nconst COMMON_VERBS: Record<string, string[]> = {\n  say: [\"remark\", \"state\", \"mention\", \"note\", \"add\", \"point out\"],\n  go: [\"head\", \"make your way\", \"set off\", \"move\", \"venture\"],\n  get: [\"obtain\", \"secure\", \"grab\", \"pick up\", \"come by\"],\n  make: [\"create\", \"craft\", \"produce\", \"put together\", \"come up with\"],\n  take: [\"grab\", \"pick up\", \"seize\", \"adopt\", \"take on\"],\n  look: [\"glance\", \"peer\", \"gaze\", \"take a look\", \"scan\"],\n  use: [\"employ\", \"utilize\", \"leverage\", \"make use of\"],\n  find: [\"discover\", \"uncover\", \"come across\", \"identify\"],\n  tell: [\"inform\", \"notify\", \"advise\", \"let know\"],\n  work: [\"operate\", \"function\", \"collaborate\", \"work on\"],\n  try: [\"attempt\", \"endeavor\", \"have a go\", \"try out\"],\n  ask: [\"inquire\", \"question\", \"request\", \"reach out to\"],\n  need: [\"require\", \"must\", \"have to\", \"be in need of\"],\n  give: [\"provide\", \"offer\", \"grant\", \"hand over\"],\n  think: [\"consider\", \"reckon\", \"believe\", \"reflect\"],\n  goon: [\"continue\", \"carry on\", \"keep going\"],\n};\n\n// Heuristic lemmatizer for English verbs (very limited but fast and dependency-free)\nfunction lemmatize(word: string): string {\n  const w = word.toLowerCase();\n  if (w === \"was\" || w === \"were\" || w === \"am\" || w === \"is\" || w === \"are\" || w === \"been\" || w === \"being\") return \"be\";\n  if (w === \"did\" || w === \"done\" || w === \"does\" || w === \"do\") return \"do\";\n  if (w === \"had\" || w === \"has\" || w === \"have\") return \"have\";\n  // basic -ing, -ed, -s stripping\n  if (w.endsWith(\"ing\") && w.length > 5) return w.slice(0, -3);\n  if (w.endsWith(\"ed\") && w.length > 4) return w.slice(0, -2);\n  if (w.endsWith(\"es\") && w.length > 4) return w.slice(0, -2);\n  if (w.endsWith(\"s\") && w.length > 3) return w.slice(0, -1);\n  return w;\n}\n\ninterface Token {\n  text: string;\n  start: number;\n  end: number;\n}\n\nfunction tokenizeEnglish(text: string): Token[] {\n  const tokens: Token[] = [];\n  const regex = /[A-Za-z]+'?[A-Za-z]+|[A-Za-z]+/g; // words with optional apostrophes\n  let match: RegExpExecArray | null;\n  while ((match = regex.exec(text)) !== null) {\n    tokens.push({ text: match[0], start: match.index, end: match.index + match[0].length });\n  }\n  return tokens;\n}\n\nfunction chooseAlternative(lemma: string, original: string): string | null {\n  const alts = COMMON_VERBS[lemma];\n  if (!alts || alts.length === 0) return null;\n  // pick one that differs from the original ignoring case\n  const origLower = original.toLowerCase();\n  const pick = alts.find(a => a.toLowerCase() !== origLower) || alts[0];\n  return pick;\n}\n\nexport function generateRepeatedVerbSuggestions(text: string, languageBase: string): AnalysisSuggestion[] {\n  // Only run for English; for other languages defer to AI analysis\n  if (!text || languageBase !== 'en') return [];\n\n  const tokens = tokenizeEnglish(text);\n  if (tokens.length === 0) return [];\n\n  // Count occurrences by lemma for candidate verbs in our list\n  const counts: Record<string, number> = {};\n  const occurrences: Record<string, Token[]> = {};\n\n  for (const tok of tokens) {\n    const lemma = lemmatize(tok.text);\n    if (!COMMON_VERBS[lemma]) continue; // only track verbs we can suggest for\n    counts[lemma] = (counts[lemma] || 0) + 1;\n    (occurrences[lemma] ||= []).push(tok);\n  }\n\n  const suggestions: AnalysisSuggestion[] = [];\n\n  // Threshold: flag if a lemma appears 3+ times in the document\n  for (const [lemma, count] of Object.entries(counts)) {\n    if (count < 3) continue;\n    const occs = occurrences[lemma] || [];\n\n    // Suggest for latter occurrences to avoid over-highlighting; cap total suggestions per lemma\n    const maxPerLemma = 3;\n    let emitted = 0;\n    for (let i = 1; i < occs.length && emitted < maxPerLemma; i++) {\n      const tok = occs[i];\n      const alt = chooseAlternative(lemma, tok.text);\n      if (!alt) continue;\n\n      const id = `local-style-${lemma}-${tok.start}-${tok.end}`;\n      const message = `This verb appears repeatedly (\"${lemma}\"). Consider a more dynamic alternative.`;\n      suggestions.push({\n        id,\n        type: 'style',\n        message,\n        suggestion: alt,\n        originalSegment: text.slice(tok.start, tok.end),\n        startIndex: tok.start,\n        endIndex: tok.end,\n      });\n      emitted++;\n    }\n  }\n\n  return suggestions;\n}\n\n"], "names": [], "mappings": ";;;AAEA,mFAAmF;AACnF,8FAA8F;AAE9F,MAAM,eAAyC;IAC7C,KAAK;QAAC;QAAU;QAAS;QAAW;QAAQ;QAAO;KAAY;IAC/D,IAAI;QAAC;QAAQ;QAAiB;QAAW;QAAQ;KAAU;IAC3D,KAAK;QAAC;QAAU;QAAU;QAAQ;QAAW;KAAU;IACvD,MAAM;QAAC;QAAU;QAAS;QAAW;QAAgB;KAAe;IACpE,MAAM;QAAC;QAAQ;QAAW;QAAS;QAAS;KAAU;IACtD,MAAM;QAAC;QAAU;QAAQ;QAAQ;QAAe;KAAO;IACvD,KAAK;QAAC;QAAU;QAAW;QAAY;KAAc;IACrD,MAAM;QAAC;QAAY;QAAW;QAAe;KAAW;IACxD,MAAM;QAAC;QAAU;QAAU;QAAU;KAAW;IAChD,MAAM;QAAC;QAAW;QAAY;QAAe;KAAU;IACvD,KAAK;QAAC;QAAW;QAAY;QAAa;KAAU;IACpD,KAAK;QAAC;QAAW;QAAY;QAAW;KAAe;IACvD,MAAM;QAAC;QAAW;QAAQ;QAAW;KAAgB;IACrD,MAAM;QAAC;QAAW;QAAS;QAAS;KAAY;IAChD,OAAO;QAAC;QAAY;QAAU;QAAW;KAAU;IACnD,MAAM;QAAC;QAAY;QAAY;KAAa;AAC9C;AAEA,qFAAqF;AACrF,SAAS,UAAU,IAAY;IAC7B,MAAM,IAAI,KAAK,WAAW;IAC1B,IAAI,MAAM,SAAS,MAAM,UAAU,MAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS,MAAM,UAAU,MAAM,SAAS,OAAO;IACpH,IAAI,MAAM,SAAS,MAAM,UAAU,MAAM,UAAU,MAAM,MAAM,OAAO;IACtE,IAAI,MAAM,SAAS,MAAM,SAAS,MAAM,QAAQ,OAAO;IACvD,gCAAgC;IAChC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IAC1D,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACzD,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;IACxD,OAAO;AACT;AAQA,SAAS,gBAAgB,IAAY;IACnC,MAAM,SAAkB,EAAE;IAC1B,MAAM,QAAQ,mCAAmC,kCAAkC;IACnF,IAAI;IACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,KAAM;QAC1C,OAAO,IAAI,CAAC;YAAE,MAAM,KAAK,CAAC,EAAE;YAAE,OAAO,MAAM,KAAK;YAAE,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QAAC;IACvF;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,KAAa,EAAE,QAAgB;IACxD,MAAM,OAAO,YAAY,CAAC,MAAM;IAChC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG,OAAO;IACvC,wDAAwD;IACxD,MAAM,YAAY,SAAS,WAAW;IACtC,MAAM,OAAO,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,OAAO,cAAc,IAAI,CAAC,EAAE;IACrE,OAAO;AACT;AAEO,SAAS,gCAAgC,IAAY,EAAE,YAAoB;IAChF,iEAAiE;IACjE,IAAI,CAAC,QAAQ,iBAAiB,MAAM,OAAO,EAAE;IAE7C,MAAM,SAAS,gBAAgB;IAC/B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,EAAE;IAElC,6DAA6D;IAC7D,MAAM,SAAiC,CAAC;IACxC,MAAM,cAAuC,CAAC;IAE9C,KAAK,MAAM,OAAO,OAAQ;QACxB,MAAM,QAAQ,UAAU,IAAI,IAAI;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,sCAAsC;QAC1E,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI;QACvC,CAAC,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE,IAAI,CAAC;IACnC;IAEA,MAAM,cAAoC,EAAE;IAE5C,8DAA8D;IAC9D,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACnD,IAAI,QAAQ,GAAG;QACf,MAAM,OAAO,WAAW,CAAC,MAAM,IAAI,EAAE;QAErC,6FAA6F;QAC7F,MAAM,cAAc;QACpB,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,UAAU,aAAa,IAAK;YAC7D,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,MAAM,kBAAkB,OAAO,IAAI,IAAI;YAC7C,IAAI,CAAC,KAAK;YAEV,MAAM,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE;YACzD,MAAM,UAAU,CAAC,+BAA+B,EAAE,MAAM,wCAAwC,CAAC;YACjG,YAAY,IAAI,CAAC;gBACf;gBACA,MAAM;gBACN;gBACA,YAAY;gBACZ,iBAAiB,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;gBAC9C,YAAY,IAAI,KAAK;gBACrB,UAAU,IAAI,GAAG;YACnB;YACA;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 8332, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { AppShell } from '@/components/layout/app-shell';\nimport { EnhancedTextEditor, type PlagiarismSource } from '@/components/editor/enhanced-text-editor';\nimport { WritingSuggestionsPanel } from '@/components/editor/writing-suggestions-panel';\nimport { WritingStatistics } from '@/components/stats/writing-statistics';\nimport { DocumentDropzone } from '@/components/shared/document-dropzone';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { AiToneAnalyzer } from '@/components/ai/ai-tone-analyzer';\nimport { AiTextGenerator } from '@/components/ai/ai-text-generator';\nimport { AiRewriter } from '@/components/ai/ai-rewriter';\nimport { PlagiarismDetector } from '@/components/ai/plagiarism-detector';\nimport { AiWritingDetector } from '@/components/ai/ai-writing-detector';\nimport { HumanizeAiText } from '@/components/ai/humanize-ai-text';\nimport { Edit3, Wand2, ShieldCheck, BrainCircuit, UserCheck, Gauge } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\nimport { detectLanguage } from '@/ai/flows/language-detection-flow';\nimport { analyzeText, type TextAnalysisInput, type AnalysisSuggestion } from '@/ai/flows/text-analysis-flow';\nimport { generateRepeatedVerbSuggestions } from '@/lib/analysis/local-verb-repetition';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\n\nimport type { PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';\nimport { useDebounce } from '@/hooks/use-debounce';\n\ninterface LinguaFlowPageProps {\n  writingMode: string;\n}\n\nfunction LinguaFlowPage({ writingMode }: LinguaFlowPageProps) {\n  const { t, getWritingLanguageBase, setWritingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n\n  const [editorValue, setEditorValue] = useState(\"\");\n  const history = useRef<{ past: string[]; present: string; future: string[] }>({ past: [], present: \"\", future: [] });\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const isUndoRedoAction = useRef(false);\n\n  const [writingDirection, setWritingDirection] = useState<'ltr' | 'rtl'>('ltr');\n  \n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [writingScore, setWritingScore] = useState(0);\n  \n  const [analysisSuggestions, setAnalysisSuggestions] = useState<AnalysisSuggestion[]>([]);\n  const [isAnalyzingText, setIsAnalyzingText] = useState(false);\n  const [plagiarismResult, setPlagiarismResult] = useState<PlagiarismDetectionOutput | null>(null);\n\n  const debouncedEditorValueForHistory = useDebounce(editorValue, 800);\n  const debouncedEditorValueForAnalysis = useDebounce(editorValue, 1500);\n  const debouncedEditorValueForLangDetection = useDebounce(editorValue, 2000);\n\n  const plagiarismSuggestions = useMemo((): PlagiarismSource[] => {\n    if (!plagiarismResult || !plagiarismResult.detectedSources) return [];\n    \n    return plagiarismResult.detectedSources.map((source, index) => ({\n      id: `plagiarism-${index}-${source.startIndex}`,\n      type: 'plagiarism',\n      originalSegment: source.plagiarizedSegment,\n      message: `Potential plagiarism detected. Source: ${source.originalSource} (Similarity: ${source.similarityScore}%)`,\n      suggestion: source.originalSource, // Store source in suggestion field\n      startIndex: source.startIndex,\n      endIndex: source.endIndex,\n    }));\n  }, [plagiarismResult]);\n  \n  // History and Undo/Redo Logic\n  const updateHistory = useCallback((value: string) => {\n    const { past, present } = history.current;\n    if (value === present) return;\n    \n    history.current = {\n        past: [...past, present],\n        present: value,\n        future: [],\n    };\n    setCanUndo(history.current.past.length > 0);\n    setCanRedo(history.current.future.length > 0);\n  }, []);\n\n  useEffect(() => {\n    if (isUndoRedoAction.current) {\n        isUndoRedoAction.current = false;\n        return;\n    }\n    updateHistory(debouncedEditorValueForHistory);\n  }, [debouncedEditorValueForHistory, updateHistory]);\n\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    const { past, present, future } = history.current;\n    if (past.length === 0) return;\n    isUndoRedoAction.current = true;\n    const newPresent = past[past.length - 1];\n    const newPast = past.slice(0, past.length - 1);\n    history.current = {\n        past: newPast,\n        present: newPresent,\n        future: [present, ...future],\n    };\n    setEditorValue(newPresent);\n    setCanUndo(newPast.length > 0);\n    setCanRedo(true);\n  }, []);\n\n  const handleRedo = useCallback(() => {\n      const { past, present, future } = history.current;\n      if (future.length === 0) return;\n      isUndoRedoAction.current = true;\n      const newPresent = future[0];\n      const newFuture = future.slice(1);\n      history.current = {\n          past: [...past, present],\n          present: newPresent,\n          future: newFuture,\n      };\n      setEditorValue(newPresent);\n      setCanUndo(true);\n      setCanRedo(newFuture.length > 0);\n  }, []);\n\n  // Text Analysis Logic\n  const requestTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {\n    if (!textToAnalyze.trim()) {\n      setAnalysisSuggestions([]);\n      setIsAnalyzingText(false);\n      return;\n    }\n    setIsAnalyzingText(true);\n    try {\n      const analysisInput: TextAnalysisInput = {\n        text: textToAnalyze,\n        language: lang,\n      };\n      const result = await analyzeText(analysisInput);\n      const aiSuggestions = result.suggestions || [];\n\n      // Merge in local repeated-verb suggestions (green underline), avoiding duplicates by id range overlap\n      const localVerbSuggestions = settings.enableLocalVerbRepetitionDetection\n        ? generateRepeatedVerbSuggestions(textToAnalyze, lang)\n        : [];\n\n      // Deduplicate: avoid overlapping same-span style suggestions\n      const merged = [...aiSuggestions];\n      for (const ls of localVerbSuggestions) {\n        const overlaps = merged.some(ms => {\n          if (ms.type !== 'style' || ls.startIndex == null || ls.endIndex == null || ms.startIndex == null || ms.endIndex == null) return false;\n          return Math.max(ls.startIndex, ms.startIndex) < Math.min(ls.endIndex, ms.endIndex);\n        });\n        if (!overlaps) merged.push(ls);\n      }\n\n      setAnalysisSuggestions(merged);\n    } catch (error) {\n      console.error(\"Error analyzing text for suggestions:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextAnalysisError\", variant: \"destructive\" });\n      setAnalysisSuggestions([]);\n    } finally {\n      setIsAnalyzingText(false);\n    }\n  }, [toast]);\n  \n  useEffect(() => {\n    requestTextAnalysis(debouncedEditorValueForAnalysis, getWritingLanguageBase());\n  }, [debouncedEditorValueForAnalysis, getWritingLanguageBase, requestTextAnalysis]);\n\n  // Language Detection Logic\n  const handleLanguageDetection = useCallback(async (text: string) => {\n    if (!text || typeof text !== 'string' || text.trim().length < 50) return; \n\n    try {\n        const result = await detectLanguage({ text });\n        const detectedLangCode = result.languageCode;\n        const currentBaseLang = getWritingLanguageBase();\n\n        if (detectedLangCode && detectedLangCode !== 'unknown' && detectedLangCode !== currentBaseLang) {\n            const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === detectedLangCode);\n            if (langInfo) {\n                const newDialect = langInfo.dialects && langInfo.dialects.length > 0\n                    ? langInfo.dialects[0].value\n                    : langInfo.value;\n                \n                setWritingLanguageDialect(newDialect);\n                \n                toast({\n                    titleKey: \"toastInfoTitle\",\n                    descriptionKey: \"toastLanguageSwitched\",\n                    descriptionParams: { language: t(langInfo.labelKey) }\n                });\n            }\n        }\n    } catch (error) {\n        console.error(\"Automatic language detection failed:\", error);\n    }\n  }, [getWritingLanguageBase, setWritingLanguageDialect, toast, t]);\n\n  useEffect(() => {\n    const currentBaseLang = getWritingLanguageBase();\n    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === currentBaseLang);\n    if (langInfo) {\n        setWritingDirection(langInfo.dir || 'ltr');\n    }\n\n    if (settings.enableAutomaticLanguageDetection) {\n      handleLanguageDetection(debouncedEditorValueForLangDetection);\n    }\n  }, [debouncedEditorValueForLangDetection, settings.enableAutomaticLanguageDetection, getWritingLanguageBase, handleLanguageDetection]);\n\n  // Editor stats\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(Boolean);\n    const currentWordCount = words.length === 1 && words[0] === \"\" ? 0 : words.length;\n    setWordCount(currentWordCount);\n    setCharCount(editorValue.length);\n    // A more balanced scoring system\n    const baseScore = Math.max(0, 100 - (analysisSuggestions.length * 5) - (plagiarismSuggestions.length * 10));\n    const lengthBonus = Math.min(20, Math.floor(currentWordCount / 25));\n    setWritingScore(Math.max(0, Math.min(100, Math.round(baseScore + lengthBonus))));\n  }, [editorValue, analysisSuggestions, plagiarismSuggestions]);\n\n  const handleApplySuggestion = useCallback((suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => {\n    let newValue = editorValue;\n    let applied = false;\n    if (typeof startIndex === 'number' && typeof endIndex === 'number' && endIndex >= startIndex) {\n        const textBefore = editorValue.substring(0, startIndex);\n        const textAfter = editorValue.substring(endIndex);\n        if (editorValue.substring(startIndex, endIndex) === originalSegment) {\n            newValue = textBefore + suggestionText + textAfter;\n            applied = true;\n        }\n    }\n    if (!applied) {\n        // Fallback for when indices are not perfect\n        const firstOccurrenceIndex = editorValue.indexOf(originalSegment);\n        if (firstOccurrenceIndex !== -1) {\n            newValue = editorValue.substring(0, firstOccurrenceIndex) + suggestionText + editorValue.substring(firstOccurrenceIndex + originalSegment.length);\n            applied = true;\n        }\n    }\n    if (applied) {\n        setEditorValue(newValue); // Directly set value, history will update via effect\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastSuggestionAppliedSuccess\" });\n    } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastSuggestionApplyError\", variant: \"destructive\" });\n    }\n  }, [editorValue, toast]);\n\n  const handleDismissSuggestion = useCallback((suggestionId: string) => {\n    setAnalysisSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n    toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastSuggestionDismissed\" });\n  }, [toast]);\n  \n  const handleInsertGeneratedText = useCallback((textToInsert: string) => {\n    const newText = editorValue.trim() === \"\" ? textToInsert : `${editorValue}\\n\\n${textToInsert}`;\n    setEditorValue(newText);\n  }, [editorValue]);\n\n  const handleApplyRewrite = useCallback((newText: string) => {\n    setEditorValue(newText);\n  }, []);\n\n  return (\n      <div className=\"flex-1 flex flex-col gap-6 p-4 md:p-6\">\n        {/* Top Section: Editor and Suggestions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow\">\n          <div className=\"lg:col-span-2 h-full min-h-[400px]\">\n            <EnhancedTextEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              writingMode={writingMode}\n              direction={writingDirection}\n              suggestions={analysisSuggestions}\n              plagiarismSources={plagiarismSuggestions}\n              onApplySuggestion={handleApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n            />\n          </div>\n          <div className=\"h-full flex flex-col gap-6\">\n            <WritingSuggestionsPanel\n              suggestions={analysisSuggestions}\n              isAnalyzing={isAnalyzingText}\n              onApplySuggestion={handleApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n            />\n            <WritingStatistics\n              wordCount={wordCount}\n              charCount={charCount}\n              writingScore={writingScore}\n            />\n\n            {/* Smaller Dropzone positioned below Writing Statistics */}\n            <DocumentDropzone onTextExtracted={setEditorValue} className=\"p-3 text-xs [&>svg]:h-5 [&>svg]:w-5 [&>svg]:mb-2\" />\n\n            {/* AI Tools Panel with collapsible sections */}\n            <AiToolsPanel\n              editorValue={editorValue}\n              writingMode={writingMode}\n              writingDirection={writingDirection}\n              onApplyRewrite={handleApplyRewrite}\n              onInsertGeneratedText={handleInsertGeneratedText}\n              onPlagiarismResult={setPlagiarismResult}\n            />\n          </div>\n        </div>\n      </div>\n  );\n}\n\nexport default function LinguaFlowPageContainer() {\n  return (\n    <AppShell>\n      {(props) => <LinguaFlowPage {...props} />}\n    </AppShell>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;;AAzBA;;;;;;;;;;;;;;;AA+BA,SAAS,eAAe,EAAE,WAAW,EAAuB;;IAC1D,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IAEtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyD;QAAE,MAAM,EAAE;QAAE,SAAS;QAAI,QAAQ,EAAE;IAAC;IAClH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IAE3F,MAAM,iCAAiC,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IAChE,MAAM,kCAAkC,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IACjE,MAAM,uCAAuC,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IAEtE,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YACpC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,eAAe,EAAE,OAAO,EAAE;YAErE,OAAO,iBAAiB,eAAe,CAAC,GAAG;iEAAC,CAAC,QAAQ,QAAU,CAAC;wBAC9D,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,OAAO,UAAU,EAAE;wBAC9C,MAAM;wBACN,iBAAiB,OAAO,kBAAkB;wBAC1C,SAAS,CAAC,uCAAuC,EAAE,OAAO,cAAc,CAAC,cAAc,EAAE,OAAO,eAAe,CAAC,EAAE,CAAC;wBACnH,YAAY,OAAO,cAAc;wBACjC,YAAY,OAAO,UAAU;wBAC7B,UAAU,OAAO,QAAQ;oBAC3B,CAAC;;QACH;wDAAG;QAAC;KAAiB;IAErB,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,QAAQ,OAAO;YACzC,IAAI,UAAU,SAAS;YAEvB,QAAQ,OAAO,GAAG;gBACd,MAAM;uBAAI;oBAAM;iBAAQ;gBACxB,SAAS;gBACT,QAAQ,EAAE;YACd;YACA,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;YACzC,WAAW,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QAC7C;oDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,iBAAiB,OAAO,EAAE;gBAC1B,iBAAiB,OAAO,GAAG;gBAC3B;YACJ;YACA,cAAc;QAChB;mCAAG;QAAC;QAAgC;KAAc;IAElD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACtC,eAAe;QACjB;yDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;YACjD,IAAI,KAAK,MAAM,KAAK,GAAG;YACvB,iBAAiB,OAAO,GAAG;YAC3B,MAAM,aAAa,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;YACxC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;YAC5C,QAAQ,OAAO,GAAG;gBACd,MAAM;gBACN,SAAS;gBACT,QAAQ;oBAAC;uBAAY;iBAAO;YAChC;YACA,eAAe;YACf,WAAW,QAAQ,MAAM,GAAG;YAC5B,WAAW;QACb;iDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;YACjD,IAAI,OAAO,MAAM,KAAK,GAAG;YACzB,iBAAiB,OAAO,GAAG;YAC3B,MAAM,aAAa,MAAM,CAAC,EAAE;YAC5B,MAAM,YAAY,OAAO,KAAK,CAAC;YAC/B,QAAQ,OAAO,GAAG;gBACd,MAAM;uBAAI;oBAAM;iBAAQ;gBACxB,SAAS;gBACT,QAAQ;YACZ;YACA,eAAe;YACf,WAAW;YACX,WAAW,UAAU,MAAM,GAAG;QAClC;iDAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO,eAAuB;YACpE,IAAI,CAAC,cAAc,IAAI,IAAI;gBACzB,uBAAuB,EAAE;gBACzB,mBAAmB;gBACnB;YACF;YACA,mBAAmB;YACnB,IAAI;gBACF,MAAM,gBAAmC;oBACvC,MAAM;oBACN,UAAU;gBACZ;gBACA,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,cAAW,AAAD,EAAE;gBACjC,MAAM,gBAAgB,OAAO,WAAW,IAAI,EAAE;gBAE9C,sGAAsG;gBACtG,MAAM,uBAAuB,SAAS,kCAAkC,GACpE,CAAA,GAAA,wJAAA,CAAA,kCAA+B,AAAD,EAAE,eAAe,QAC/C,EAAE;gBAEN,6DAA6D;gBAC7D,MAAM,SAAS;uBAAI;iBAAc;gBACjC,KAAK,MAAM,MAAM,qBAAsB;oBACrC,MAAM,WAAW,OAAO,IAAI;oFAAC,CAAA;4BAC3B,IAAI,GAAG,IAAI,KAAK,WAAW,GAAG,UAAU,IAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,GAAG,UAAU,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,OAAO;4BAChI,OAAO,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,UAAU,IAAI,KAAK,GAAG,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ;wBACnF;;oBACA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC;gBAC7B;gBAEA,uBAAuB;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAA0B,SAAS;gBAAc;gBACtG,uBAAuB,EAAE;YAC3B,SAAU;gBACR,mBAAmB;YACrB;QACF;0DAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,oBAAoB,iCAAiC;QACvD;mCAAG;QAAC;QAAiC;QAAwB;KAAoB;IAEjF,2BAA2B;IAC3B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,OAAO;YACjD,IAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI;YAElE,IAAI;gBACA,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;oBAAE;gBAAK;gBAC3C,MAAM,mBAAmB,OAAO,YAAY;gBAC5C,MAAM,kBAAkB;gBAExB,IAAI,oBAAoB,qBAAqB,aAAa,qBAAqB,iBAAiB;oBAC5F,MAAM,WAAW,6HAAA,CAAA,wBAAqB,CAAC,IAAI;wFAAC,CAAA,IAAK,EAAE,KAAK,KAAK;;oBAC7D,IAAI,UAAU;wBACV,MAAM,aAAa,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,IAC7D,SAAS,QAAQ,CAAC,EAAE,CAAC,KAAK,GAC1B,SAAS,KAAK;wBAEpB,0BAA0B;wBAE1B,MAAM;4BACF,UAAU;4BACV,gBAAgB;4BAChB,mBAAmB;gCAAE,UAAU,EAAE,SAAS,QAAQ;4BAAE;wBACxD;oBACJ;gBACJ;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;YAC1D;QACF;8DAAG;QAAC;QAAwB;QAA2B;QAAO;KAAE;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,kBAAkB;YACxB,MAAM,WAAW,6HAAA,CAAA,wBAAqB,CAAC,IAAI;qDAAC,CAAA,IAAK,EAAE,KAAK,KAAK;;YAC7D,IAAI,UAAU;gBACV,oBAAoB,SAAS,GAAG,IAAI;YACxC;YAEA,IAAI,SAAS,gCAAgC,EAAE;gBAC7C,wBAAwB;YAC1B;QACF;mCAAG;QAAC;QAAsC,SAAS,gCAAgC;QAAE;QAAwB;KAAwB;IAErI,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC;YACrD,MAAM,mBAAmB,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,MAAM,MAAM;YACjF,aAAa;YACb,aAAa,YAAY,MAAM;YAC/B,iCAAiC;YACjC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,MAAO,oBAAoB,MAAM,GAAG,IAAM,sBAAsB,MAAM,GAAG;YACvG,MAAM,cAAc,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,mBAAmB;YAC/D,gBAAgB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY;QACnE;mCAAG;QAAC;QAAa;QAAqB;KAAsB;IAE5D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC,gBAAwB,iBAAyB,YAAqB;YAC/G,IAAI,WAAW;YACf,IAAI,UAAU;YACd,IAAI,OAAO,eAAe,YAAY,OAAO,aAAa,YAAY,YAAY,YAAY;gBAC1F,MAAM,aAAa,YAAY,SAAS,CAAC,GAAG;gBAC5C,MAAM,YAAY,YAAY,SAAS,CAAC;gBACxC,IAAI,YAAY,SAAS,CAAC,YAAY,cAAc,iBAAiB;oBACjE,WAAW,aAAa,iBAAiB;oBACzC,UAAU;gBACd;YACJ;YACA,IAAI,CAAC,SAAS;gBACV,4CAA4C;gBAC5C,MAAM,uBAAuB,YAAY,OAAO,CAAC;gBACjD,IAAI,yBAAyB,CAAC,GAAG;oBAC7B,WAAW,YAAY,SAAS,CAAC,GAAG,wBAAwB,iBAAiB,YAAY,SAAS,CAAC,uBAAuB,gBAAgB,MAAM;oBAChJ,UAAU;gBACd;YACJ;YACA,IAAI,SAAS;gBACT,eAAe,WAAW,qDAAqD;gBAC/E,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAgC;YAC3F,OAAO;gBACH,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAA6B,SAAS;gBAAc;YAC7G;QACF;4DAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YAC3C;uEAAuB,CAAA,OAAQ,KAAK,MAAM;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;YACzD,MAAM;gBAAE,UAAU;gBAAkB,gBAAgB;YAA2B;QACjF;8DAAG;QAAC;KAAM;IAEV,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YAC7C,MAAM,UAAU,YAAY,IAAI,OAAO,KAAK,eAAe,GAAG,YAAY,IAAI,EAAE,cAAc;YAC9F,eAAe;QACjB;gEAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACtC,eAAe;QACjB;yDAAG,EAAE;IAEL,qBACI,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6JAAA,CAAA,qBAAkB;wBACjB,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,mBAAmB;wBACnB,mBAAmB;wBACnB,qBAAqB;wBACrB,QAAQ;wBACR,QAAQ;wBACR,SAAS;wBACT,SAAS;;;;;;;;;;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,kKAAA,CAAA,0BAAuB;4BACtB,aAAa;4BACb,aAAa;4BACb,mBAAmB;4BACnB,qBAAqB;;;;;;sCAEvB,6LAAC,uJAAA,CAAA,oBAAiB;4BAChB,WAAW;4BACX,WAAW;4BACX,cAAc;;;;;;sCAIhB,6LAAC,uJAAA,CAAA,mBAAgB;4BAAC,iBAAiB;4BAAgB,WAAU;;;;;;sCAG7D,6LAAC;4BACC,aAAa;4BACb,aAAa;4BACb,kBAAkB;4BAClB,gBAAgB;4BAChB,uBAAuB;4BACvB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GA9RS;;QAC0D,sIAAA,CAAA,UAAO;QACtD,+HAAA,CAAA,WAAQ;QACL,qJAAA,CAAA,qBAAkB;QAkBA,kIAAA,CAAA,cAAW;QACV,kIAAA,CAAA,cAAW;QACN,kIAAA,CAAA,cAAW;;;KAvBjD;AAgSM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,WAAQ;kBACN,CAAC,sBAAU,6LAAC;gBAAgB,GAAG,KAAK;;;;;;;;;;;AAG3C;MANwB", "debugId": null}}]}