'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Undo2,
  Redo2,
  Wand2,
  Circle,
  X,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Copy,
  Sparkles
} from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';

interface LinguaFlowEditorProps {
  value: string;
  onChange: (value: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  isAutoCorrectionEnabled: boolean;
  onToggleAutoCorrection: () => void;
  suggestions: any[];
  onApplySuggestion: (suggestionId: string) => void;
  direction: 'ltr' | 'rtl';
  currentLanguage: string;
  lastSaved?: Date;
  cursorPosition?: { line: number; col: number };
  selectionInfo?: string;
}

export function LinguaFlowEditor({
  value,
  onChange,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  isAutoCorrectionEnabled,
  onToggleAutoCorrection,
  suggestions,
  onApplySuggestion,
  direction,
  currentLanguage,
  lastSaved,
  cursorPosition = { line: 1, col: 1 },
  selectionInfo = ''
}: LinguaFlowEditorProps) {
  const { t } = useI18n();
  const editorRef = useRef<HTMLDivElement>(null);
  const [selectedText, setSelectedText] = useState('');
  const [showAiActions, setShowAiActions] = useState(false);
  const [aiActionPosition, setAiActionPosition] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);
  const [fontFamily, setFontFamily] = useState('inter');
  const [fontSize, setFontSize] = useState('14');

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim();
      setSelectedText(selectedText);
      
      // Get selection position for AI actions panel
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      setAiActionPosition({ x: rect.left, y: rect.bottom + 10 });
      setShowAiActions(true);
    } else {
      setShowAiActions(false);
      setSelectedText('');
    }
  }, []);

  const handleEditorChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const newValue = e.currentTarget.textContent || '';
    onChange(newValue);
  }, [onChange]);

  const aiActions = [
    { id: 'rewrite-formal', label: 'Formalize', action: () => console.log('Formalize') },
    { id: 'rewrite-casual', label: 'Casualize', action: () => console.log('Casualize') },
    { id: 'rewrite-shorter', label: 'Shorten', action: () => console.log('Shorten') },
    { id: 'rewrite-longer', label: 'Lengthen', action: () => console.log('Lengthen') },
    { id: 'summarize', label: 'Summarize', action: () => console.log('Summarize') },
    { id: 'explain', label: 'Explain', action: () => console.log('Explain') },
  ];

  return (
    <section className="space-y-4">
      {/* Main Editor Card */}
      <Card className="linguaflow-card shadow-lg">
        <CardHeader className="border-b px-4 py-3" style={{ borderColor: 'var(--border-color)' }}>
          {/* Top Row - Title and Controls */}
          <div className="flex flex-row items-center justify-between mb-3">
            <h3 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
              Document Editor
            </h3>
            <div className="flex items-center space-x-2">
              {/* Status Indicator */}
              <div className="flex items-center space-x-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                <Circle
                  className="h-3 w-3 fill-current"
                  style={{ color: 'var(--success-color)' }}
                />
                <span>Ready</span>
              </div>

              {/* Auto-correction Toggle */}
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleAutoCorrection}
                className="p-1 rounded"
                style={{
                  color: isAutoCorrectionEnabled ? 'var(--primary-color)' : 'var(--text-secondary)',
                  backgroundColor: 'var(--bg-alt)'
                }}
                title={isAutoCorrectionEnabled ? 'Auto-correction enabled' : 'Auto-correction disabled'}
              >
                <Wand2 className="h-4 w-4" />
              </Button>

              {/* Undo/Redo */}
              <Button
                variant="ghost"
                size="icon"
                onClick={onUndo}
                disabled={!canUndo}
                className="p-1 rounded"
                style={{
                  color: 'var(--text-secondary)',
                  backgroundColor: 'var(--bg-alt)'
                }}
                title="Undo (Ctrl+Z)"
              >
                <Undo2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={onRedo}
                disabled={!canRedo}
                className="p-1 rounded"
                style={{
                  color: 'var(--text-secondary)',
                  backgroundColor: 'var(--bg-alt)'
                }}
                title="Redo (Ctrl+Y)"
              >
                <Redo2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Formatting Toolbar */}
          <div className="flex items-center space-x-1 border-t pt-3" style={{ borderColor: 'var(--border-color)' }}>
            {/* Font Formatting */}
            <div className="flex items-center space-x-1 border-r pr-2" style={{ borderColor: 'var(--border-color)' }}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 font-bold"
                style={{ color: 'var(--text-secondary)' }}
                title="Bold (Ctrl+B)"
              >
                <Bold className="h-4 w-4 mr-1" />
                B
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 italic"
                style={{ color: 'var(--text-secondary)' }}
                title="Italic (Ctrl+I)"
              >
                <Italic className="h-4 w-4 mr-1" />
                I
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 underline"
                style={{ color: 'var(--text-secondary)' }}
                title="Underline (Ctrl+U)"
              >
                <Underline className="h-4 w-4 mr-1" />
                U
              </Button>
            </div>

            {/* Text Alignment */}
            <div className="flex items-center space-x-1 border-r pr-2" style={{ borderColor: 'var(--border-color)' }}>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                style={{ color: 'var(--text-secondary)' }}
                title="Align Left"
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                style={{ color: 'var(--text-secondary)' }}
                title="Align Center"
              >
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                style={{ color: 'var(--text-secondary)' }}
                title="Align Right"
              >
                <AlignRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Font Selection */}
            <select
              className="h-8 px-2 text-sm border rounded"
              style={{
                backgroundColor: 'var(--bg-primary)',
                borderColor: 'var(--border-color)',
                color: 'var(--text-primary)'
              }}
              title="Font Family"
              value={fontFamily}
              onChange={(e) => setFontFamily(e.target.value)}
            >
              <option value="inter">Inter</option>
              <option value="arial">Arial</option>
              <option value="helvetica">Helvetica</option>
              <option value="times">Times New Roman</option>
              <option value="georgia">Georgia</option>
              <option value="courier">Courier New</option>
            </select>

            <select
              className="h-8 px-2 text-sm border rounded"
              style={{
                backgroundColor: 'var(--bg-primary)',
                borderColor: 'var(--border-color)',
                color: 'var(--text-primary)'
              }}
              title="Font Size"
              value={fontSize}
              onChange={(e) => setFontSize(e.target.value)}
            >
              <option value="12">12px</option>
              <option value="14">14px</option>
              <option value="16">16px</option>
              <option value="18">18px</option>
              <option value="20">20px</option>
              <option value="24">24px</option>
            </select>
          </div>
        </CardHeader>

        {/* Editor Content */}
        <div className="linguaflow-editor linguaflow-scrollbar">
          <div
            ref={editorRef}
            contentEditable
            suppressContentEditableWarning
            onInput={handleEditorChange}
            onMouseUp={handleTextSelection}
            onKeyUp={handleTextSelection}
            className="p-6 min-h-[380px] outline-none"
            style={{
              color: 'var(--text-primary)',
              lineHeight: '1.75',
              direction: direction,
              textAlign: direction === 'rtl' ? 'right' : 'left'
            }}
            data-placeholder={t('editorPlaceholder')}
            dangerouslySetInnerHTML={{ __html: value }}
          />
        </div>

        {/* Editor Footer */}
        <div
          className="border-t px-4 py-2 flex items-center justify-between text-xs"
          style={{
            borderColor: 'var(--border-color)',
            backgroundColor: 'var(--bg-alt)',
            color: 'var(--text-secondary)'
          }}
        >
          <div className="flex items-center space-x-3">
            <span>
              Last saved: <span className="font-medium">
                {isClient && lastSaved ? lastSaved.toLocaleTimeString() : 'Never'}
              </span>
            </span>
            <span>
              Language: <span className="font-medium">{currentLanguage}</span>
            </span>

            {/* Smart Synonyms and Copy Text Buttons */}
            <div className="flex items-center space-x-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                style={{ color: 'var(--primary-color)' }}
                title="Smart Synonyms"
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Smart Synonyms
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                style={{ color: 'var(--text-secondary)' }}
                title="Copy Text"
              >
                <Copy className="h-3 w-3 mr-1" />
                Copy Text
              </Button>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {selectionInfo && <span>{selectionInfo}</span>}
            <span>Line {cursorPosition.line}, Col {cursorPosition.col}</span>
          </div>
        </div>
      </Card>

      {/* AI Context Panel */}
      {showAiActions && selectedText && (
        <Card 
          className="linguaflow-card animate-in fade-in-0 zoom-in-95"
          style={{
            position: 'fixed',
            top: aiActionPosition.y,
            left: aiActionPosition.x,
            zIndex: 1000,
            maxWidth: '400px'
          }}
        >
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold flex items-center text-sm" style={{ color: 'var(--primary-color)' }}>
                <Wand2 className="mr-2 h-4 w-4" />
                AI Actions for Selection ✨
              </h4>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowAiActions(false)}
                className="h-6 w-6 p-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <p 
              className="text-sm italic p-2 rounded"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'var(--bg-alt)'
              }}
            >
              "{selectedText}"
            </p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {aiActions.map((action) => (
                <Button
                  key={action.id}
                  variant="secondary"
                  size="sm"
                  onClick={action.action}
                  className="linguaflow-button-secondary text-xs"
                >
                  {action.label}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </section>
  );
}
