import { Flag } from "lucide-react";

export type ProficiencyLevel = 'native' | 'advanced' | 'intermediate' | 'beginner' | 'none';

export interface WritingLanguageInfo {
  value: string; // Base language code e.g., "en", "es"
  labelKey: string; // e.g., "languageEnglishGeneral"
  dir: 'ltr' | 'rtl';
  dialects?: Array<{ value: string; labelKey: string }>; // Full locale code e.g., "en-US"
  supportsProficiency: boolean;
}

export const APP_SUPPORTED_UI_LANGUAGES = [
  { value: "en", labelKey: "englishLanguage", dir: "ltr" },
  { value: "ar", labelKey: "arabicLanguage", dir: "rtl" },
  { value: "tr", labelKey: "turkishLanguage", dir: "ltr" },
  { value: "es", labelKey: "spanishLanguage", dir: "ltr" },
  { value: "de", labelKey: "germanLanguage", dir: "ltr" },
  { value: "fr", labelKey: "frenchLanguage", dir: "ltr" },
  { value: "nl", labelKey: "dutchLanguage", dir: "ltr" },
  { value: "it", labelKey: "italianLanguage", dir: "ltr" },
];

export const APP_WRITING_LANGUAGES: WritingLanguageInfo[] = [
  {
    value: "en", labelKey: "languageEnglishGeneral", dir: "ltr",
    dialects: [
      { value: "en-US", labelKey: "englishUSDialect" },
      { value: "en-GB", labelKey: "englishUKDialect" },
      { value: "en-CA", labelKey: "englishCanadianDialect" }
    ],
    supportsProficiency: true,
  },
  {
    value: "es", labelKey: "languageSpanishGeneral", dir: "ltr",
    dialects: [ { value: "es-ES", labelKey: "spanishSpainLanguage" }, { value: "es-MX", labelKey: "spanishMexicoLanguage" } ],
    supportsProficiency: true,
  },
  { value: "fr", labelKey: "languageFrenchGeneral", dir: "ltr", supportsProficiency: true },
  { value: "de", labelKey: "languageGermanGeneral", dir: "ltr", supportsProficiency: true },
  { value: "it", labelKey: "languageItalianGeneral", dir: "ltr", supportsProficiency: true },
  { value: "nl", labelKey: "languageDutchGeneral", dir: "ltr", supportsProficiency: true },
  {
    value: "ar", labelKey: "languageArabicGeneral", dir: "rtl",
    dialects: [
        { value: "ar-SY", labelKey: "arabicSyriaLanguage" },
        { value: "ar-SA", labelKey: "arabicSaudiArabiaLanguage" },
        { value: "ar-EG", labelKey: "arabicEgyptLanguage" },
    ],
    supportsProficiency: true,
  },
  { value: "tr", labelKey: "languageTurkishGeneral", dir: "ltr", supportsProficiency: true },
];

export const PROFICIENCY_LEVELS: Array<{value: ProficiencyLevel, labelKey: string}> = [
    {value: 'native', labelKey: 'proficiencyNative'},
    {value: 'advanced', labelKey: 'proficiencyAdvanced'},
    {value: 'intermediate', labelKey: 'proficiencyIntermediate'},
    {value: 'beginner', labelKey: 'proficiencyBeginner'},
];

    