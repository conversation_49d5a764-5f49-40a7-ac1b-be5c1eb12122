'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Lightbulb, 
  MessageCircle, 
  History, 
  Search,
  Sliders
} from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';

interface Suggestion {
  id: string;
  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';
  text: string;
  suggestion: string;
  explanation?: string;
}

interface ToneAnalysis {
  overall: string;
  formality: string;
  confidence: string;
}

interface RecentPrompt {
  id: string;
  prompt: string;
  timestamp: Date;
}

interface AnalysisSidebarProps {
  suggestions: Suggestion[];
  toneAnalysis: ToneAnalysis;
  recentPrompts: RecentPrompt[];
  onApplySuggestion: (suggestionId: string) => void;
  onAdjustTone: () => void;
  onUsePrompt: (prompt: string) => void;
}

export function AnalysisSidebar({
  suggestions,
  toneAnalysis,
  recentPrompts,
  onApplySuggestion,
  onAdjustTone,
  onUsePrompt
}: AnalysisSidebarProps) {
  const { t } = useI18n();

  const getSuggestionTypeColor = (type: string) => {
    switch (type) {
      case 'grammar':
      case 'spelling':
        return 'var(--error-color)';
      case 'style':
        return 'var(--success-color)';
      case 'clarity':
        return 'var(--info-color)';
      case 'tone':
        return 'var(--warning-color)';
      default:
        return 'var(--text-secondary)';
    }
  };

  const getSuggestionTypeIcon = (type: string) => {
    const color = getSuggestionTypeColor(type);
    return (
      <div 
        className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
        style={{ backgroundColor: color }}
      />
    );
  };

  return (
    <aside className="space-y-6">
      {/* Suggestions Card */}
      <Card className="linguaflow-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-base" style={{ color: 'var(--text-primary)' }}>
            <Lightbulb className="mr-2 h-5 w-5" style={{ color: 'var(--warning-color)' }} />
            {t('writingSuggestionsTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-60 linguaflow-scrollbar">
            {suggestions.length > 0 ? (
              <div className="space-y-3">
                {suggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className="p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    style={{ 
                      borderColor: 'var(--border-color)',
                      backgroundColor: 'var(--bg-primary)'
                    }}
                    onClick={() => onApplySuggestion(suggestion.id)}
                  >
                    <div className="flex items-start">
                      {getSuggestionTypeIcon(suggestion.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                          {suggestion.type.charAt(0).toUpperCase() + suggestion.type.slice(1)}
                        </p>
                        <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                          "{suggestion.text}" → "{suggestion.suggestion}"
                        </p>
                        {suggestion.explanation && (
                          <p className="text-xs mt-1 italic" style={{ color: 'var(--text-secondary)' }}>
                            {suggestion.explanation}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8" style={{ color: 'var(--text-secondary)' }}>
                <Search className="mx-auto h-8 w-8 mb-2" />
                <p className="text-sm">
                  {t('startTypingForSuggestionsDescription')}
                </p>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Tone Analysis Card */}
      <Card className="linguaflow-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-base" style={{ color: 'var(--text-primary)' }}>
            <MessageCircle className="mr-2 h-5 w-5" style={{ color: 'var(--info-color)' }} />
            {t('aiToneAnalysisTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>Overall Tone:</span>
              <span className="font-medium" style={{ color: 'var(--text-accent)' }}>
                {toneAnalysis.overall}
              </span>
            </div>
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>{t('formalityLabel')}:</span>
              <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                {toneAnalysis.formality}
              </span>
            </div>
            <div className="flex justify-between">
              <span style={{ color: 'var(--text-secondary)' }}>{t('confidenceLabel')}:</span>
              <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                {toneAnalysis.confidence}
              </span>
            </div>
          </div>
          <Button
            onClick={onAdjustTone}
            variant="secondary"
            size="sm"
            className="w-full linguaflow-button-secondary"
          >
            <Sliders className="mr-1 h-4 w-4" />
            Adjust Tone ✨
          </Button>
        </CardContent>
      </Card>

      {/* Recent AI Prompts Card */}
      <Card className="linguaflow-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-base" style={{ color: 'var(--text-primary)' }}>
            <History className="mr-2 h-5 w-5" style={{ color: 'var(--primary-color)' }} />
            {t('generationHistoryTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-40 linguaflow-scrollbar">
            {recentPrompts.length > 0 ? (
              <div className="space-y-2">
                {recentPrompts.map((prompt) => (
                  <div
                    key={prompt.id}
                    className="p-2 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    style={{ 
                      borderColor: 'var(--border-color)',
                      backgroundColor: 'var(--bg-primary)'
                    }}
                    onClick={() => onUsePrompt(prompt.prompt)}
                  >
                    <p className="text-xs font-medium truncate" style={{ color: 'var(--text-primary)' }}>
                      {prompt.prompt}
                    </p>
                    <p className="text-xs mt-1" style={{ color: 'var(--text-secondary)' }}>
                      {prompt.timestamp.toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4" style={{ color: 'var(--text-secondary)' }}>
                <p className="text-sm">{t('noGenerationsYetPlaceholder')}</p>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </aside>
  );
}
