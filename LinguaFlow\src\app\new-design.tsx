'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { LinguaFlowHeader } from '@/components/layout/linguaflow-header';
import { WritingToolsSidebar } from '@/components/layout/writing-tools-sidebar';
import { AnalysisSidebar } from '@/components/layout/analysis-sidebar';
import { LinguaFlowEditor } from '@/components/editor/linguaflow-editor';
import { SettingsModal } from '@/components/modals/settings-modal';
import { HelpModal } from '@/components/modals/help-modal';
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { useAppearance } from '@/contexts/theme-context';
import { useFeatureSettings } from '@/contexts/feature-settings-context';

interface Suggestion {
  id: string;
  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';
  text: string;
  suggestion: string;
  explanation?: string;
}

interface ToneAnalysis {
  overall: string;
  formality: string;
  confidence: string;
}

interface RecentPrompt {
  id: string;
  prompt: string;
  timestamp: Date;
}

function NewDesignPage() {
  const { t, uiLanguage, writingLanguageDialect } = useI18n();
  const { toast } = useToast();
  const { settings } = useFeatureSettings();
  const { effectiveTheme } = useAppearance();
  const isRTL = uiLanguage === 'ar';

  // Editor state
  const [editorValue, setEditorValue] = useState('');
  const [writingMode, setWritingMode] = useState('formal');
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [isAutoCorrectionEnabled, setIsAutoCorrectionEnabled] = useState(true);
  const [aiPrompt, setAiPrompt] = useState('');

  // Statistics
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [qualityScore, setQualityScore] = useState(100);

  // Analysis data
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [toneAnalysis, setToneAnalysis] = useState<ToneAnalysis>({
    overall: 'Neutral',
    formality: 'Professional (75%)',
    confidence: 'Confident (80%)'
  });
  const [recentPrompts, setRecentPrompts] = useState<RecentPrompt[]>([]);

  // Modal states
  const [showSettings, setShowSettings] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | undefined>(undefined);

  // Set lastSaved on client side only
  useEffect(() => {
    setLastSaved(new Date());
  }, []);

  // Update statistics when editor value changes
  useEffect(() => {
    const words = editorValue.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(editorValue.length);
    
    // Mock error count and quality score
    const errors = Math.max(0, Math.floor(words.length * 0.05) - Math.floor(Math.random() * 3));
    setErrorCount(errors);
    setQualityScore(Math.max(60, 100 - errors * 5));
  }, [editorValue]);

  // Handlers
  const handleEditorChange = useCallback((value: string) => {
    setEditorValue(value);
    setLastSaved(new Date());
  }, []);

  const handleUndo = useCallback(() => {
    // Implement undo logic
    console.log('Undo');
  }, []);

  const handleRedo = useCallback(() => {
    // Implement redo logic
    console.log('Redo');
  }, []);

  const handleFileImport = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setEditorValue(content);
      toast({
        title: t('toastFileImportSuccessTitle'),
        description: t('toastFileImportSuccessMessage'),
      });
    };
    reader.readAsText(file);
  }, [toast, t]);

  const handleCheckText = useCallback(() => {
    // Mock suggestions
    const mockSuggestions: Suggestion[] = [
      {
        id: '1',
        type: 'grammar',
        text: 'are',
        suggestion: 'is',
        explanation: 'Subject-verb agreement error'
      },
      {
        id: '2',
        type: 'style',
        text: 'very good',
        suggestion: 'excellent',
        explanation: 'More precise word choice'
      }
    ];
    setSuggestions(mockSuggestions);
    toast({
      title: 'Text Analysis Complete',
      description: `Found ${mockSuggestions.length} suggestions`,
    });
  }, [toast]);

  const handleAiRewrite = useCallback(() => {
    toast({
      title: 'AI Rewrite',
      description: 'AI rewrite feature activated',
    });
  }, [toast]);

  const handleBrainstorm = useCallback(() => {
    toast({
      title: 'Brainstorm',
      description: 'Brainstorm feature activated',
    });
  }, [toast]);

  const handleExport = useCallback(() => {
    const blob = new Blob([editorValue], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.txt';
    a.click();
    URL.revokeObjectURL(url);
    toast({
      title: 'Export Complete',
      description: 'Document exported successfully',
    });
  }, [editorValue, toast]);

  const handleClear = useCallback(() => {
    setEditorValue('');
    setSuggestions([]);
    toast({
      title: t('toastEditorClearedSuccess'),
    });
  }, [toast, t]);

  const handleGenerateText = useCallback(() => {
    if (!aiPrompt.trim()) {
      toast({
        title: t('toastInputRequiredTitle'),
        description: t('toastPromptRequiredError'),
        variant: 'destructive',
      });
      return;
    }

    // Mock text generation
    const generatedText = `Generated text based on: "${aiPrompt}"`;
    setEditorValue(prev => prev + (prev ? '\n\n' : '') + generatedText);
    
    // Add to recent prompts
    const newPrompt: RecentPrompt = {
      id: Date.now().toString(),
      prompt: aiPrompt,
      timestamp: new Date()
    };
    setRecentPrompts(prev => [newPrompt, ...prev.slice(0, 4)]);
    setAiPrompt('');
    
    toast({
      title: t('toastTextGeneratedSuccess'),
    });
  }, [aiPrompt, toast, t]);

  const handleApplySuggestion = useCallback((suggestionId: string) => {
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      const newValue = editorValue.replace(suggestion.text, suggestion.suggestion);
      setEditorValue(newValue);
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      toast({
        title: t('toastSuggestionAppliedSuccess'),
      });
    }
  }, [suggestions, editorValue, toast, t]);

  const handleAdjustTone = useCallback(() => {
    toast({
      title: 'Tone Adjustment',
      description: 'Tone adjustment feature activated',
    });
  }, [toast]);

  const handleUsePrompt = useCallback((prompt: string) => {
    setAiPrompt(prompt);
  }, []);

  const handlePlagiarismCheck = useCallback(() => {
    toast({
      title: 'Plagiarism Detection',
      description: 'Plagiarism detection feature activated',
    });
  }, [toast]);

  const handleAiWritingDetection = useCallback(() => {
    toast({
      title: 'AI Writing Detection',
      description: 'AI writing detection feature activated',
    });
  }, [toast]);

  const handleHumanizeText = useCallback(() => {
    toast({
      title: 'Humanize Text',
      description: 'Text humanization feature activated',
    });
  }, [toast]);

  const writingDirection = writingLanguageDialect?.startsWith('ar') ? 'rtl' : 'ltr';
  const currentLanguage = writingLanguageDialect || 'English';

  return (
    <div
      className="min-h-screen transition-all duration-300"
      data-theme={effectiveTheme}
      style={{
        backgroundColor: 'var(--bg-primary)',
        color: 'var(--text-primary)'
      }}
    >
      {/* Header */}
      <LinguaFlowHeader
        onSettingsClick={() => setShowSettings(true)}
        onHelpClick={() => setShowHelp(true)}
      />

      {/* Main Content */}
      <main className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className={`grid grid-cols-1 lg:grid-cols-12 gap-6 ${isRTL ? 'direction-rtl' : ''}`}>
          {/* Left Sidebar - Writing Tools */}
          <div className="lg:col-span-3">
            <WritingToolsSidebar
              writingMode={writingMode}
              onWritingModeChange={setWritingMode}
              onFileImport={handleFileImport}
              onCheckText={handleCheckText}
              onAiRewrite={handleAiRewrite}
              onBrainstorm={handleBrainstorm}
              onExport={handleExport}
              onClear={handleClear}
              onPlagiarismCheck={handlePlagiarismCheck}
              onAiWritingDetection={handleAiWritingDetection}
              onHumanizeText={handleHumanizeText}
              aiPrompt={aiPrompt}
              onAiPromptChange={setAiPrompt}
              onGenerateText={handleGenerateText}
              wordCount={wordCount}
              charCount={charCount}
              errorCount={errorCount}
              qualityScore={qualityScore}
            />
          </div>

          {/* Center - Editor */}
          <div className="lg:col-span-6">
            <LinguaFlowEditor
              value={editorValue}
              onChange={handleEditorChange}
              onUndo={handleUndo}
              onRedo={handleRedo}
              canUndo={canUndo}
              canRedo={canRedo}
              isAutoCorrectionEnabled={isAutoCorrectionEnabled}
              onToggleAutoCorrection={() => setIsAutoCorrectionEnabled(!isAutoCorrectionEnabled)}
              suggestions={suggestions}
              onApplySuggestion={handleApplySuggestion}
              direction={writingDirection}
              currentLanguage={currentLanguage}
              lastSaved={lastSaved}
            />
          </div>

          {/* Right Sidebar - Analysis */}
          <div className="lg:col-span-3">
            <AnalysisSidebar
              suggestions={suggestions}
              toneAnalysis={toneAnalysis}
              recentPrompts={recentPrompts}
              onApplySuggestion={handleApplySuggestion}
              onAdjustTone={handleAdjustTone}
              onUsePrompt={handleUsePrompt}
            />
          </div>
        </div>
      </main>

      {/* Modals */}
      <SettingsModal open={showSettings} onOpenChange={setShowSettings} />
      <HelpModal open={showHelp} onOpenChange={setShowHelp} />
    </div>
  );
}

export default NewDesignPage;
