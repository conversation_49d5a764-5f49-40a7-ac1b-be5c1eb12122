{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/config/languages.ts"], "sourcesContent": ["import { Flag } from \"lucide-react\";\n\nexport type ProficiencyLevel = 'native' | 'advanced' | 'intermediate' | 'beginner' | 'none';\n\nexport interface WritingLanguageInfo {\n  value: string; // Base language code e.g., \"en\", \"es\"\n  labelKey: string; // e.g., \"languageEnglishGeneral\"\n  dir: 'ltr' | 'rtl';\n  dialects?: Array<{ value: string; labelKey: string }>; // Full locale code e.g., \"en-US\"\n  supportsProficiency: boolean;\n}\n\nexport const APP_SUPPORTED_UI_LANGUAGES = [\n  { value: \"en\", labelKey: \"englishLanguage\", dir: \"ltr\" },\n  { value: \"ar\", labelKey: \"arabicLanguage\", dir: \"rtl\" },\n  { value: \"tr\", labelKey: \"turkishLanguage\", dir: \"ltr\" },\n  { value: \"es\", labelKey: \"spanishLanguage\", dir: \"ltr\" },\n  { value: \"de\", labelKey: \"germanLanguage\", dir: \"ltr\" },\n  { value: \"fr\", labelKey: \"frenchLanguage\", dir: \"ltr\" },\n  { value: \"nl\", labelKey: \"dutchLanguage\", dir: \"ltr\" },\n  { value: \"it\", labelKey: \"italianLanguage\", dir: \"ltr\" },\n];\n\nexport const APP_WRITING_LANGUAGES: WritingLanguageInfo[] = [\n  {\n    value: \"en\", labelKey: \"languageEnglishGeneral\", dir: \"ltr\",\n    dialects: [\n      { value: \"en-US\", labelKey: \"englishUSDialect\" },\n      { value: \"en-GB\", labelKey: \"englishUKDialect\" },\n      { value: \"en-CA\", labelKey: \"englishCanadianDialect\" }\n    ],\n    supportsProficiency: true,\n  },\n  {\n    value: \"es\", labelKey: \"languageSpanishGeneral\", dir: \"ltr\",\n    dialects: [ { value: \"es-ES\", labelKey: \"spanishSpainLanguage\" }, { value: \"es-MX\", labelKey: \"spanishMexicoLanguage\" } ],\n    supportsProficiency: true,\n  },\n  { value: \"fr\", labelKey: \"languageFrenchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"de\", labelKey: \"languageGermanGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"it\", labelKey: \"languageItalianGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"nl\", labelKey: \"languageDutchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  {\n    value: \"ar\", labelKey: \"languageArabicGeneral\", dir: \"rtl\",\n    dialects: [\n        { value: \"ar-SY\", labelKey: \"arabicSyriaLanguage\" },\n        { value: \"ar-SA\", labelKey: \"arabicSaudiArabiaLanguage\" },\n        { value: \"ar-EG\", labelKey: \"arabicEgyptLanguage\" },\n    ],\n    supportsProficiency: true,\n  },\n  { value: \"tr\", labelKey: \"languageTurkishGeneral\", dir: \"ltr\", supportsProficiency: true },\n];\n\nexport const PROFICIENCY_LEVELS: Array<{value: ProficiencyLevel, labelKey: string}> = [\n    {value: 'native', labelKey: 'proficiencyNative'},\n    {value: 'advanced', labelKey: 'proficiencyAdvanced'},\n    {value: 'intermediate', labelKey: 'proficiencyIntermediate'},\n    {value: 'beginner', labelKey: 'proficiencyBeginner'},\n];\n\n    "], "names": [], "mappings": ";;;;;AAYO,MAAM,6BAA6B;IACxC;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAiB,KAAK;IAAM;IACrD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;CACxD;AAEM,MAAM,wBAA+C;IAC1D;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YACR;gBAAE,OAAO;gBAAS,UAAU;YAAmB;YAC/C;gBAAE,OAAO;gBAAS,UAAU;YAAmB;YAC/C;gBAAE,OAAO;gBAAS,UAAU;YAAyB;SACtD;QACD,qBAAqB;IACvB;IACA;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YAAE;gBAAE,OAAO;gBAAS,UAAU;YAAuB;YAAG;gBAAE,OAAO;gBAAS,UAAU;YAAwB;SAAG;QACzH,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;IACzF;QAAE,OAAO;QAAM,UAAU;QAAwB,KAAK;QAAO,qBAAqB;IAAK;IACvF;QACE,OAAO;QAAM,UAAU;QAAyB,KAAK;QACrD,UAAU;YACN;gBAAE,OAAO;gBAAS,UAAU;YAAsB;YAClD;gBAAE,OAAO;gBAAS,UAAU;YAA4B;YACxD;gBAAE,OAAO;gBAAS,UAAU;YAAsB;SACrD;QACD,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;CAC1F;AAEM,MAAM,qBAAyE;IAClF;QAAC,OAAO;QAAU,UAAU;IAAmB;IAC/C;QAAC,OAAO;QAAY,UAAU;IAAqB;IACnD;QAAC,OAAO;QAAgB,UAAU;IAAyB;IAC3D;QAAC,OAAO;QAAY,UAAU;IAAqB;CACtD", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/i18n-context.tsx"], "sourcesContent": ["import { useState, useEffect, useContext, useCallback } from 'react';\n\n'use client';\n\nimport type { ReactNode } from 'react';\nimport React, { createContext, useState, useEffect, useCallback, useContext } from 'react';\nimport { APP_SUPPORTED_UI_LANGUAGES, APP_WRITING_LANGUAGES, type ProficiencyLevel } from '@/config/languages';\n\n\nimport enTranslationsJson from '@/locales/en.json';\nimport arTranslationsJson from '@/locales/ar.json';\nimport trTranslationsJson from '@/locales/tr.json';\nimport esTranslationsJson from '@/locales/es.json';\nimport deTranslationsJson from '@/locales/de.json';\nimport frTranslationsJson from '@/locales/fr.json';\nimport nlTranslationsJson from '@/locales/nl.json';\nimport itTranslationsJson from '@/locales/it.json';\n\ntype Translations = Record<string, string>;\n\n\nconst getSafeTranslations = (json: any): Translations => {\n  if (json && typeof json === 'object' && Object.keys(json).length > 0) {\n    return json as Translations;\n  }\n  return {}; \n};\n\nconst allTranslationsData: Record<string, Translations> = {\n  'en-US': getSafeTranslations(enUSTranslationsJson),\n  'en-GB': getSafeTranslations(enGBTranslationsJson),\n  'ar': getSafeTranslations(arTranslationsJson),\n  'tr': getSafeTranslations(trTranslationsJson),\n  'es': getSafeTranslations(esTranslationsJson),\n  'de': getSafeTranslations(deTranslationsJson),\n  'fr': getSafeTranslations(frTranslationsJson),\n  'nl': getSafeTranslations(nlTranslationsJson),\n  'it': getSafeTranslations(itTranslationsJson),\n};\n\nconst DEFAULT_UI_LANGUAGE = 'en-US';\nconst DEFAULT_WRITING_LANGUAGE_DIALECT = 'en-US'; // Store full dialect\nconst DEFAULT_PROFICIENCY: ProficiencyLevel = 'native';\n\nexport interface I18nContextType {\n  uiLanguage: string;\n  setUiLanguage: (lang: string) => void;\n  writingLanguageDialect: string; // Stores full dialect e.g. \"en-US\", \"es-ES\"\n  setWritingLanguageDialect: (dialect: string) => void;\n  getWritingLanguageBase: () => string; // Helper to get \"en\" from \"en-US\"\n  languageProficiency: Record<string, ProficiencyLevel>; // Keyed by base language e.g. \"en\"\n  setLanguageProficiency: (baseLang: string, level: ProficiencyLevel) => void;\n  t: (key: string, params?: Record<string, string | number>) => string;\n}\n\nconst I18nContext = createContext<I18nContextType | undefined>(undefined);\n\nexport function I18nProvider({ children }: { children: ReactNode }) {\n  const [uiLanguage, setUiLanguageState] = useState<string>(DEFAULT_UI_LANGUAGE);\n  const [translations, setTranslations] = useState<Translations>(allTranslationsData[DEFAULT_UI_LANGUAGE]);\n  const [writingLanguageDialect, setWritingLanguageDialectState] = useState<string>(DEFAULT_WRITING_LANGUAGE_DIALECT);\n  const [languageProficiency, setLanguageProficiencyState] = useState<Record<string, ProficiencyLevel>>({});\n\n  const updateDocumentAttributes = (langCode: string) => {\n    const langInfo = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === langCode) || APP_SUPPORTED_UI_LANGUAGES[0];\n    document.documentElement.lang = langCode;\n    document.documentElement.dir = langInfo?.dir || 'ltr';\n  };\n\n  useEffect(() => {\n    // Load UI Language from localStorage or browser settings\n    const storedUiLanguage = localStorage.getItem('lingua-flow-ui-language');\n    let initialLang = storedUiLanguage;\n\n    if (!initialLang) {\n        const browserLang = (navigator.languages ? navigator.languages[0] : navigator.language) || DEFAULT_UI_LANGUAGE;\n        const matchingSupportedLang = APP_SUPPORTED_UI_LANGUAGES.find(lang => lang.value === browserLang || browserLang.startsWith(lang.value));\n        initialLang = matchingSupportedLang ? matchingSupportedLang.value : DEFAULT_UI_LANGUAGE;\n    }\n    \n    // Set initial language state\n    setUiLanguage(initialLang);\n\n    // Load Writing Language Dialect\n    const storedWritingLangDialect = localStorage.getItem('lingua-flow-writing-language-dialect');\n    setWritingLanguageDialectState(storedWritingLangDialect || DEFAULT_WRITING_LANGUAGE_DIALECT);\n\n    // Load Language Proficiency\n    const storedProficiency = localStorage.getItem('lingua-flow-language-proficiency');\n    if (storedProficiency) {\n      try {\n        setLanguageProficiencyState(JSON.parse(storedProficiency));\n      } catch (e) {\n        localStorage.removeItem('lingua-flow-language-proficiency'); // Clear if invalid\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const setUiLanguage = useCallback((lang: string) => {\n    const validLang = allTranslationsData[lang] ? lang : DEFAULT_UI_LANGUAGE;\n    \n    setUiLanguageState(validLang);\n    setTranslations(allTranslationsData[validLang]);\n    localStorage.setItem('lingua-flow-ui-language', validLang);\n    updateDocumentAttributes(validLang);\n  }, []);\n\n  const setWritingLanguageDialect = useCallback((dialect: string) => {\n    setWritingLanguageDialectState(dialect);\n    localStorage.setItem('lingua-flow-writing-language-dialect', dialect);\n  }, []);\n\n  const getWritingLanguageBase = useCallback((): string => {\n    return writingLanguageDialect.split('-')[0];\n  }, [writingLanguageDialect]);\n\n  const setLanguageProficiency = useCallback((baseLang: string, level: ProficiencyLevel) => {\n    setLanguageProficiencyState(prev => {\n      const newProficiency = { ...prev, [baseLang]: level };\n      localStorage.setItem('lingua-flow-language-proficiency', JSON.stringify(newProficiency));\n      return newProficiency;\n    });\n  }, []);\n\n  const t = useCallback((key: string, params?: Record<string, string | number>): string => {\n    let mappedString = translations[key] !== undefined ? translations[key] : key;\n    if (params) {\n      Object.keys(params).forEach(paramKey => {\n        mappedString = mappedString.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(params[paramKey]));\n      });\n    }\n    return mappedString;\n  }, [translations]);\n\n  return (\n    <I18nContext.Provider value={{ uiLanguage, setUiLanguage, writingLanguageDialect, setWritingLanguageDialect, getWritingLanguageBase, languageProficiency, setLanguageProficiency, t }}>\n      {children}\n    </I18nContext.Provider>\n  );\n}\n\nexport function useI18n(): I18nContextType {\n  const context = useContext(I18nContext);\n  if (context === undefined) {\n    throw new Error('useI18n must be used within an I18nProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;AAmBA,MAAM,sBAAsB,CAAC;IAC3B,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;QACpE,OAAO;IACT;IACA,OAAO,CAAC;AACV;AAEA,MAAM,sBAAoD;IACxD,SAAS,oBAAoB;IAC7B,SAAS,oBAAoB;IAC7B,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;IAC5C,MAAM,oBAAoB,4FAAA,CAAA,UAAkB;AAC9C;AAEA,MAAM,sBAAsB;AAC5B,MAAM,mCAAmC,SAAS,qBAAqB;AACvE,MAAM,sBAAwC;AAa9C,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,YAAY,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,mBAAmB,CAAC,oBAAoB;IACvG,MAAM,CAAC,wBAAwB,+BAA+B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAClF,MAAM,CAAC,qBAAqB,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IAEvG,MAAM,2BAA2B,CAAC;QAChC,MAAM,WAAW,0HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,0HAAA,CAAA,6BAA0B,CAAC,EAAE;QAC5G,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,UAAU,OAAO;IAClD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,IAAI,cAAc;QAElB,IAAI,CAAC,aAAa;YACd,MAAM,cAAc,CAAC,UAAU,SAAS,GAAG,UAAU,SAAS,CAAC,EAAE,GAAG,UAAU,QAAQ,KAAK;YAC3F,MAAM,wBAAwB,0HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,eAAe,YAAY,UAAU,CAAC,KAAK,KAAK;YACrI,cAAc,wBAAwB,sBAAsB,KAAK,GAAG;QACxE;QAEA,6BAA6B;QAC7B,cAAc;QAEd,gCAAgC;QAChC,MAAM,2BAA2B,aAAa,OAAO,CAAC;QACtD,+BAA+B,4BAA4B;QAE3D,4BAA4B;QAC5B,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,IAAI,mBAAmB;YACrB,IAAI;gBACF,4BAA4B,KAAK,KAAK,CAAC;YACzC,EAAE,OAAO,GAAG;gBACV,aAAa,UAAU,CAAC,qCAAqC,mBAAmB;YAClF;QACF;IACF,uDAAuD;IACvD,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,YAAY,mBAAmB,CAAC,KAAK,GAAG,OAAO;QAErD,mBAAmB;QACnB,gBAAgB,mBAAmB,CAAC,UAAU;QAC9C,aAAa,OAAO,CAAC,2BAA2B;QAChD,yBAAyB;IAC3B,GAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,+BAA+B;QAC/B,aAAa,OAAO,CAAC,wCAAwC;IAC/D,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,OAAO,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,GAAG;QAAC;KAAuB;IAE3B,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QAC5D,4BAA4B,CAAA;YAC1B,MAAM,iBAAiB;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAM;YACpD,aAAa,OAAO,CAAC,oCAAoC,KAAK,SAAS,CAAC;YACxE,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,IAAI,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,KAAa;QAClC,IAAI,eAAe,YAAY,CAAC,IAAI,KAAK,YAAY,YAAY,CAAC,IAAI,GAAG;QACzE,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBAC1B,eAAe,aAAa,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,OAAO,MAAM,CAAC,SAAS;YACjG;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAY;YAAe;YAAwB;YAA2B;YAAwB;YAAqB;YAAwB;QAAE;kBACjL;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/theme-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppearanceProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppearanceProvider() from the server but AppearanceProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx <module evaluation>\",\n    \"AppearanceProvider\",\n);\nexport const useAppearance = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppearance() from the server but useAppearance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx <module evaluation>\",\n    \"useAppearance\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gEACA", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/theme-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppearanceProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppearanceProvider() from the server but AppearanceProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx\",\n    \"AppearanceProvider\",\n);\nexport const useAppearance = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppearance() from the server but useAppearance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/theme-context.tsx\",\n    \"useAppearance\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4CACA", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/dictionary-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DictionaryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DictionaryProvider() from the server but DictionaryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx <module evaluation>\",\n    \"DictionaryProvider\",\n);\nexport const useDictionary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDictionary() from the server but useDictionary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx <module evaluation>\",\n    \"useDictionary\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/dictionary-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DictionaryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call DictionaryProvider() from the server but DictionaryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx\",\n    \"DictionaryProvider\",\n);\nexport const useDictionary = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDictionary() from the server but useDictionary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/dictionary-context.tsx\",\n    \"useDictionary\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/feature-settings-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureSettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureSettingsProvider() from the server but FeatureSettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"FeatureSettingsProvider\",\n);\nexport const defaultFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultFeatureSettings() from the server but defaultFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"defaultFeatureSettings\",\n);\nexport const useFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureSettings() from the server but useFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx <module evaluation>\",\n    \"useFeatureSettings\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,2EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,2EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2EACA", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/contexts/feature-settings-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeatureSettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeatureSettingsProvider() from the server but FeatureSettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"FeatureSettingsProvider\",\n);\nexport const defaultFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultFeatureSettings() from the server but defaultFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"defaultFeatureSettings\",\n);\nexport const useFeatureSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useFeatureSettings() from the server but useFeatureSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/feature-settings-context.tsx\",\n    \"useFeatureSettings\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,uDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,uDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,uDACA", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/layout.tsx"], "sourcesContent": ["\nimport type {Metadata} from 'next';\nimport './globals.css';\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { I18nProvider } from '@/contexts/i18n-context';\nimport { AppearanceProvider } from '@/contexts/theme-context'; // Renamed to AppearanceProvider\nimport { DictionaryProvider } from '@/contexts/dictionary-context';\nimport { FeatureSettingsProvider } from '@/contexts/feature-settings-context';\n\nexport const metadata: Metadata = {\n  title: 'LinguaFlow', \n  description: 'Grammar Correction & Writing Assistant', \n  manifest: '/manifest.json',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    // Class and dir will be set by providers\n    <html lang=\"en\" dir=\"ltr\" suppressHydrationWarning>\n      <head>\n        {/* Favicon and App Icons */}\n        <link rel=\"icon\" href=\"/favicon.ico\" sizes=\"any\" />\n        <link rel=\"icon\" href=\"/favicon.svg\" type=\"image/svg+xml\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n\n        {/* Meta tags for PWA */}\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"LinguaFlow\" />\n\n        {/* Fonts */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap\" rel=\"stylesheet\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;500&display=swap\" rel=\"stylesheet\" />\n        <link href=\"https://fonts.googleapis.com/css2?family=Cormorant+SC:wght@500&display=swap\" rel=\"stylesheet\" />\n      </head>\n      <body className=\"font-body antialiased min-h-screen flex flex-col bg-background text-foreground\">\n        <AppearanceProvider>\n          <I18nProvider>\n            <DictionaryProvider>\n              <FeatureSettingsProvider>\n                {children}\n                <Toaster />\n              </FeatureSettingsProvider>\n            </DictionaryProvider>\n          </I18nProvider>\n        </AppearanceProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA,uOAA+D,gCAAgC;AAC/F;AACA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,OACE,yCAAyC;kBACzC,8OAAC;QAAK,MAAK;QAAK,KAAI;QAAM,wBAAwB;;0BAChD,8OAAC;;kCAEC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,OAAM;;;;;;kCAC3C,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,MAAK;;;;;;kCAC1C,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAG1B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAGhD,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,MAAK;wBAAmF,KAAI;;;;;;kCAClG,8OAAC;wBAAK,MAAK;wBAAqF,KAAI;;;;;;kCACpG,8OAAC;wBAAK,MAAK;wBAA8E,KAAI;;;;;;;;;;;;0BAE/F,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,oIAAA,CAAA,qBAAkB;8BACjB,cAAA,8OAAC,mIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,yIAAA,CAAA,qBAAkB;sCACjB,cAAA,8OAAC,kJAAA,CAAA,0BAAuB;;oCACrB;kDACD,8OAAC,mIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB", "debugId": null}}]}