'use client';

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useI18n } from '@/contexts/i18n-context';
import { useAppearance } from '@/contexts/theme-context';
import { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';

interface SettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  const { t, uiLanguage, setUiLanguage } = useI18n();
  const { theme, setTheme, fontSize, setFontSize, highContrastMode, setHighContrastMode } = useAppearance();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('settingsTitle')}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="language" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="language">{t('languageLabel')}</TabsTrigger>
            <TabsTrigger value="appearance">{t('appearanceLabel')}</TabsTrigger>
            <TabsTrigger value="features">{t('featuresLabel')}</TabsTrigger>
            <TabsTrigger value="advanced">{t('advancedSettingsLabel')}</TabsTrigger>
          </TabsList>

          <TabsContent value="language" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('languageLabel')}</CardTitle>
                <CardDescription>{t('uiLanguageDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ui-language">{t('uiLanguageLabel')}</Label>
                  <Select value={uiLanguage} onValueChange={setUiLanguage}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('selectUiLanguagePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (
                        <SelectItem key={lang.value} value={lang.value}>
                          {t(lang.labelKey)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('appearanceLabel')}</CardTitle>
                <CardDescription>{t('themeDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">{t('themeLabel')}</Label>
                  <Select value={theme} onValueChange={setTheme}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('selectThemePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">{t('themeLight')}</SelectItem>
                      <SelectItem value="dark">{t('themeDark')}</SelectItem>
                      <SelectItem value="system">{t('themeSystem')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="font-size">{t('fontSizeLabel')}</Label>
                  <Select value={fontSize} onValueChange={setFontSize}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('selectFontSizePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">{t('fontSizeSmall')}</SelectItem>
                      <SelectItem value="medium">{t('fontSizeMedium')}</SelectItem>
                      <SelectItem value="large">{t('fontSizeLarge')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="high-contrast"
                    checked={highContrastMode}
                    onCheckedChange={setHighContrastMode}
                  />
                  <Label htmlFor="high-contrast">{t('highContrastModeLabel')}</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('featuresLabel')}</CardTitle>
                <CardDescription>{t('featureSettingsDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch id="auto-correction" defaultChecked />
                  <Label htmlFor="auto-correction">{t('automaticTextCorrectionLabel')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="real-time-grammar" defaultChecked />
                  <Label htmlFor="real-time-grammar">{t('realTimeGrammarCheckingLabel')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="real-time-spell" defaultChecked />
                  <Label htmlFor="real-time-spell">{t('realTimeSpellCheckingLabel')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="style-suggestions" defaultChecked />
                  <Label htmlFor="style-suggestions">{t('styleSuggestionsLabel')}</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('advancedSettingsLabel')}</CardTitle>
                <CardDescription>{t('advancedSettingsDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch id="offline-mode" />
                  <Label htmlFor="offline-mode">{t('enableOfflineFunctionalityLabel')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="auto-language-detection" defaultChecked />
                  <Label htmlFor="auto-language-detection">{t('enableAutomaticLanguageDetectionLabel')}</Label>
                </div>
                <div className="pt-4">
                  <Button variant="destructive" size="sm">
                    {t('resetAllSettingsLabel')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('cancelButton')}
          </Button>
          <Button onClick={() => onOpenChange(false)}>
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
