"use client";

import React, { useState } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Wand2, Edit3, Gauge, ShieldCheck, BrainCircuit, UserCheck } from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';
import { useFeatureSettings } from '@/contexts/feature-settings-context';
import { AiRewriter } from '@/components/ai/ai-rewriter';
import { AiTextGenerator } from '@/components/ai/ai-text-generator';
import { AiToneAnalyzer } from '@/components/ai/ai-tone-analyzer';
import { PlagiarismDetector } from '@/components/ai/plagiarism-detector';
import { AiWritingDetector } from '@/components/ai/ai-writing-detector';
import { HumanizeAiText } from '@/components/ai/humanize-ai-text';
import { cn } from '@/lib/utils';
import type { PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';

interface AiToolsPanelProps {
  editorValue: string;
  writingMode: string;
  writingDirection: 'ltr' | 'rtl';
  onApplyRewrite: (newText: string) => void;
  onInsertGeneratedText: (textToInsert: string) => void;
  onPlagiarismResult: (result: PlagiarismDetectionOutput | null) => void;
}

interface AiTool {
  id: string;
  titleKey: string;
  icon: React.ComponentType<{ className?: string }>;
  component: React.ReactNode;
  enabled?: boolean;
}

export function AiToolsPanel({
  editorValue,
  writingMode,
  writingDirection,
  onApplyRewrite,
  onInsertGeneratedText,
  onPlagiarismResult,
}: AiToolsPanelProps) {
  const { t } = useI18n();
  const { settings } = useFeatureSettings();
  const [activeToolId, setActiveToolId] = useState<string | null>(null);

  const aiTools: AiTool[] = [
    {
      id: 'ai-rewriter',
      titleKey: 'aiRewriteTitle',
      icon: Wand2,
      component: (
        <AiRewriter
          currentText={editorValue}
          onApplyRewrite={onApplyRewrite}
          writingMode={writingMode}
          direction={writingDirection}
        />
      ),
    },
    {
      id: 'content-generator',
      titleKey: 'aiTextGenerationTitle',
      icon: Edit3,
      component: <AiTextGenerator onInsertText={onInsertGeneratedText} />,
    },
    {
      id: 'tone-analyzer',
      titleKey: 'aiToneAnalysisTitle',
      icon: Gauge,
      component: settings.enableToneDetection ? <AiToneAnalyzer currentText={editorValue} /> : null,
      enabled: settings.enableToneDetection,
    },
    {
      id: 'plagiarism-detector',
      titleKey: 'plagiarismDetectionTitle',
      icon: ShieldCheck,
      component: settings.enablePlagiarismDetection ? (
        <PlagiarismDetector currentText={editorValue} onResult={onPlagiarismResult} />
      ) : null,
      enabled: settings.enablePlagiarismDetection,
    },
    {
      id: 'ai-writing-detector',
      titleKey: 'aiWritingDetectionTitle',
      icon: BrainCircuit,
      component: <AiWritingDetector currentText={editorValue} />,
    },
    {
      id: 'humanize-text',
      titleKey: 'humanizeAiTextTitle',
      icon: UserCheck,
      component: <HumanizeAiText currentText={editorValue} onInsertText={onInsertGeneratedText} />,
    },
  ];

  const enabledTools = aiTools.filter(tool => tool.enabled !== false);

  const handleToolToggle = (toolId: string) => {
    setActiveToolId(activeToolId === toolId ? null : toolId);
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">{t('aiToolsTitle')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {enabledTools.map((tool) => {
          const isActive = activeToolId === tool.id;
          const Icon = tool.icon;

          return (
            <Collapsible key={tool.id} open={isActive} onOpenChange={() => handleToolToggle(tool.id)}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-between h-auto py-3 px-4",
                    isActive && "bg-muted"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="h-4 w-4" />
                    <span className="font-medium">{t(tool.titleKey)}</span>
                  </div>
                  {isActive ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-3">
                <div className="border rounded-lg p-4 bg-muted/30">
                  {tool.component}
                </div>
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </CardContent>
    </Card>
  );
}
