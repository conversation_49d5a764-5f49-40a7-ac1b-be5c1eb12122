globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/src/components/ui/toaster.tsx <module evaluation>":{"id":"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/components/ui/toaster.tsx":{"id":"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/i18n-context.tsx <module evaluation>":{"id":"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/i18n-context.tsx":{"id":"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/theme-context.tsx <module evaluation>":{"id":"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/theme-context.tsx":{"id":"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/dictionary-context.tsx <module evaluation>":{"id":"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/dictionary-context.tsx":{"id":"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/feature-settings-context.tsx <module evaluation>":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/contexts/feature-settings-context.tsx":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/app/page.tsx <module evaluation>":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/src_00364e35._.js","static/chunks/node_modules_5ae4ec28._.js","static/chunks/src_app_page_tsx_fac01a29._.js"],"async":false},"[project]/src/app/page.tsx":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/src_00364e35._.js","static/chunks/node_modules_5ae4ec28._.js","static/chunks/src_app_page_tsx_fac01a29._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/toaster.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/i18n-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/theme-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/dictionary-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/feature-settings-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__18fc8499._.js","server/chunks/ssr/node_modules_f5754146._.js","server/chunks/ssr/src_cf23be14._.js","server/chunks/ssr/node_modules_1821de9f._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/toaster.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/i18n-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/i18n-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/theme-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/theme-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/dictionary-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/dictionary-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/contexts/feature-settings-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/feature-settings-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/src_app_globals_b805903d.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/src_app_globals_b805903d.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"[project]/src/app/layout":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"[project]/src/app/page":["static/chunks/src_e217a5cd._.js","static/chunks/node_modules_a31e2e15._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/src_00364e35._.js","static/chunks/node_modules_5ae4ec28._.js","static/chunks/src_app_page_tsx_fac01a29._.js"]}}
