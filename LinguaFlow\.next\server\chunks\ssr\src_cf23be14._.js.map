{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/icons/logo.tsx"], "sourcesContent": ["\"use client\";\nimport type { SVGProps } from 'react';\nimport { useId } from 'react';\n\ninterface LogoProps extends SVGProps<SVGSVGElement> {\n  size?: number | string;\n  title?: string;\n  decorative?: boolean;\n}\n\n// LinguaFlow brand logo component generated from public/LinguaFlow-logo.svg\nexport function Logo({\n  size = 32,\n  title = 'LinguaFlow',\n  decorative = false,\n  ...props\n}: LogoProps) {\n  // Ensure unique gradient IDs when multiple instances are rendered\n  const reactId = useId();\n  const gradientId = `lf-logo-gradient-${reactId.replace(/:/g, '')}`;\n\n  const ariaProps = decorative && !title\n    ? { 'aria-hidden': true }\n    : { role: 'img', 'aria-label': title } as const;\n\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 100 100\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      focusable=\"false\"\n      {...ariaProps}\n      {...props}\n    >\n      {title ? <title>{title}</title> : null}\n      <defs>\n        <linearGradient id={gradientId} gradientUnits=\"userSpaceOnUse\" x1=\"228.26253\" y1=\"474.16306\" x2=\"253.2735\" y2=\"46.282173\">\n          <stop offset=\"0\" stopColor=\"#3485C0\" />\n          <stop offset=\"1\" stopColor=\"#5BBCEB\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"50\" cy=\"50\" r=\"50\" fill=\"#FFFFFF\" />\n\n      <path fill={`url(#${gradientId})`} transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M51.176 364.081C33.2039 330.028 23.9599 296.118 24.1262 257.566C24.1931 238.367 26.8956 219.267 32.1579 200.802C48.4007 146.151 85.496 100.086 135.427 72.5623C174.083 51.3524 218.238 42.2968 262.122 46.579C268.282 47.1509 274.342 48.11 280.439 49.1269C283.542 49.6445 286.737 50.03 289.756 50.9172L290.028 50.9416C291.361 51.0761 292.551 51.1576 293.588 52.0723C294.923 54.6511 293.757 59.1065 293.389 61.9008C291.504 76.2266 288.172 90.0415 283.791 103.804C280.839 113.078 277.423 122.685 273.264 131.495C270.282 137.811 266.611 143.876 263.804 150.253C268.864 144.594 273.534 138.868 279.377 133.953C296.189 119.808 319.807 114.493 341.251 113.32C347.826 112.96 354.499 113.18 361.085 113.183C375.993 113.189 390.937 113.417 405.838 113.03C416.792 112.744 427.658 112.297 438.365 109.745C445.403 108.067 451.949 105.278 458.616 102.541C461.909 104.38 464.236 107.071 466.245 110.241C466.395 125.668 453.929 145.562 443.434 156.298C439.819 159.996 435.676 163.189 431.395 166.076C414.561 177.431 398.453 179.513 378.581 179.645C371.724 179.691 364.86 179.492 358.002 179.447C343.98 179.355 332.014 180.14 321.546 190.752C314.912 197.477 313.415 205.98 311.127 214.777C311.563 215.001 321.647 214.53 323.36 214.535C343.265 214.713 363.17 214.713 383.075 214.535C382.093 230.526 376.191 245.933 363.945 256.688C349.401 269.463 329.18 271.579 310.573 270.759C311.346 276.332 311.067 282.125 311.062 287.744C311.055 294.512 311.028 301.174 310.066 307.89C308.201 320.904 302.079 334.292 294.18 344.771C283.989 358.291 269.536 367.97 254.678 375.722C250.886 377.7 246.926 379.88 242.815 381.09C252.207 381.117 261.5 382.126 270.875 382.467C282.748 382.785 294.625 382.871 306.501 382.725C319.681 382.626 332.913 382.445 345.992 380.655C375.298 376.643 403.507 363.776 424.601 342.827C434.41 333.102 442.802 322.043 449.527 309.978C452.006 305.489 453.491 300.556 456.378 296.285C454.582 317.355 445.431 338.977 435.906 357.61C430.781 367.637 425.48 377.948 416.738 385.341C406.876 393.681 395.651 399.462 382.957 402.021C370.357 404.561 357.353 403.473 344.584 403.48L288.702 403.493L235.779 403.539L219.158 403.56C215.881 403.562 212.512 403.717 209.25 403.432C208.66 403.381 208.075 403.296 207.495 403.178C201.87 415.074 185.422 428.245 174.339 435.311C170.507 437.754 166.494 439.854 162.473 441.96C179.818 441.189 197.256 441.355 214.613 441.475L255.279 441.568L322.011 441.609C333.594 441.616 345.244 441.37 356.819 441.67C348.94 446.839 340.733 451.124 332.208 455.1C289.986 474.721 242.527 480.069 196.997 470.338C176.63 466.116 156.746 458.311 138.48 448.447C135.274 446.716 131.257 445.026 128.729 442.398C125.671 439.838 122.297 437.955 119.024 435.71C112.83 434.737 100.502 423.229 95.8622 418.823C93.4331 416.516 90.8907 413.478 88.0738 411.69L88.1191 411.507C86.9167 409.519 84.4893 407.599 82.8344 405.924C82.5902 406.651 82.917 407.325 82.6435 407.659L81.7785 407.738L81.5892 408.032C82.0739 409.208 83.9708 412.202 85.1482 412.654C85.2151 412.679 85.2879 412.685 85.3578 412.701C85.7692 412.352 86.1124 411.999 86.4632 411.59C86.9501 411.693 87.2218 411.886 87.4857 412.34C87.7752 412.838 87.6724 413.463 87.4269 413.953C87.1053 414.596 86.5988 415.116 85.9844 415.475C85.5255 415.505 85.0782 415.532 84.6181 415.528C84.5381 415.46 84.4573 415.392 84.3781 415.323C79.9609 411.445 77.1017 406.21 73.5107 401.651C67.3013 393.768 50.5644 376.563 50.1845 366.975C50.1339 365.699 50.3025 365.023 51.176 364.081Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M289.756 50.9172L290.028 50.9416C289.259 57.1266 287.266 62.9096 285.285 68.7903C279.759 85.1666 272.908 101.065 264.799 116.328C262.151 121.42 259.621 128.003 254.956 131.603C245.652 138.783 227.823 145.484 216.49 149.514C213.191 150.687 209.777 151.648 206.524 152.927C221.223 153.396 236.544 150.1 249.913 144.094L202.567 203.74C199.337 207.784 196.168 211.877 193.063 216.019C188.092 222.65 183.543 229.161 176.97 234.364C163.662 244.898 148.173 253.522 132.823 260.676C126.951 263.412 120.86 265.703 115.046 268.558C136.132 265.058 150.644 263.197 168.879 250.929C164.431 257.858 160.719 265.226 156.179 272.09C145.732 287.884 130.449 299.118 113.426 306.968C110.662 308.272 107.838 309.443 104.963 310.479C102.447 311.38 99.8021 312.14 97.3704 313.238C113.353 312.082 122.664 310.69 137.573 304.525C128.821 311.259 119.181 316.752 108.926 320.848C99.2487 324.81 89.426 328.022 80.0306 332.689C85.3872 311.764 91.3078 291.172 99.8823 271.289C123.724 216.006 163.554 168.023 207.744 127.715C212.927 122.987 218.003 117.991 223.382 113.492L222.96 112.753C200.889 126.248 180.06 141.676 160.719 158.855C119.524 195.338 87.1925 238.862 66.9172 290.207C63.496 298.87 58.1623 311.173 57.6073 320.318C55.6107 315.012 52.6968 309.976 50.392 304.786C44.4753 291.461 39.8733 277.503 37.8603 263.028C40.0414 266.601 41.0377 270.995 42.7085 274.838C46.4576 283.46 52.0541 290.809 58.1245 297.904C49.0177 267.242 48.1239 249.522 55.6109 218.463C56.4491 229.285 57.5776 239.35 61.9223 249.45C63.0333 252.033 64.2775 254.701 65.9249 256.991C60.031 238.735 61.6644 213.965 69.7227 196.623C70.2245 198.013 69.9002 203.126 69.9462 204.882C70.0574 209.124 71.4483 223.142 73.5628 226.364C74.0766 223.684 73.9212 220.654 74.1255 217.914C74.5509 212.209 75.3301 206.539 76.7474 200.99C82.2319 179.517 95.0083 163.05 110.272 147.474C116.792 140.727 123.633 134.298 130.773 128.21C137.278 122.641 144.256 117.495 150.539 111.688C147.518 123.91 148.006 140.933 150.483 153.21C152.037 148.192 152.158 142.302 152.954 137.089C154.751 125.329 158.21 107.968 167.025 99.5818C171.715 95.1198 177.398 91.9439 182.588 88.1279C178.323 97.1155 173.986 106.127 171.616 115.836C170.404 120.797 169.722 125.898 168.991 130.947C172.553 124.616 175.006 117.729 178.526 111.362C183.977 101.499 191.709 89.2841 200.879 82.6601C205.557 79.2812 211.261 77.2579 216.569 75.0831C226.181 71.1449 236.078 67.6417 245.51 63.3018C238.335 70.4083 232.903 78.0964 229.03 87.4629C241.532 73.3625 253.335 65.2729 270.906 58.3559C274.74 56.8465 278.699 55.696 282.526 54.2109C284.99 53.2546 287.313 51.8292 289.756 50.9172Z\"/>\n      <path fill=\"#4EA8D8\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M222.96 112.753C224.558 111.5 226.309 110.56 228.153 109.72C226.565 110.979 224.988 112.257 223.382 113.492L222.96 112.753Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M205.821 225.402C205.372 240.368 205.746 255.411 205.736 270.39L205.526 346.42C205.497 351.135 205.526 355.85 205.613 360.564C205.76 368.287 206.646 384.231 204.487 391.107C202.336 397.957 192.012 407.784 186.707 412.776C171.807 426.793 158.556 435.53 138.453 440.524C135.23 441.325 131.971 441.737 128.729 442.398C125.671 439.838 122.297 437.955 119.024 435.71C119.115 435.686 119.206 435.66 119.298 435.636C122.793 434.729 126.472 434.593 129.998 433.784C140.96 431.27 150.019 424.13 155.75 414.534C158.947 409.156 161.105 403.226 162.113 397.051C164.404 383.314 163.287 368.002 163.171 354.102L163.186 306.302C163.155 300.345 161.964 288.834 163.765 283.671C165.489 278.726 169.983 273.083 172.977 268.75C183.198 253.96 193.956 238.907 205.821 225.402Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M182.69 145.534L183.109 146.291C179.22 150.014 175.179 153.633 171.411 157.477C117.28 212.684 79.2571 279.278 69.3628 356.824C68.1239 366.534 66.7236 376.326 66.4861 386.124C71.5567 393.028 77.0149 399.638 82.8344 405.924C82.5902 406.651 82.917 407.325 82.6435 407.659L81.7785 407.738L81.5892 408.032C82.0739 409.208 83.9708 412.202 85.1482 412.654C85.2151 412.679 85.2879 412.685 85.3578 412.701C85.7692 412.352 86.1124 411.999 86.4632 411.59C86.9501 411.693 87.2218 411.886 87.4857 412.34C87.7752 412.838 87.6724 413.463 87.4269 413.953C87.1053 414.596 86.5988 415.116 85.9844 415.475C85.5255 415.505 85.0782 415.532 84.6181 415.528C84.5381 415.46 84.4573 415.392 84.3781 415.323C79.9609 411.445 77.1017 406.21 73.5107 401.651C67.3013 393.768 50.5644 376.563 50.1845 366.975C50.1339 365.699 50.3025 365.023 51.176 364.081C52.3821 365.102 53.4113 366.336 54.4741 367.503C54.4819 365.811 54.661 364.176 54.9157 362.505C56.0667 354.953 57.7857 347.406 59.4327 339.947C61.487 330.245 63.9702 320.64 66.875 311.158C84.3157 255.113 117.34 205.679 160.298 165.933C164.244 162.312 168.171 158.671 172.078 155.009C175.513 151.769 178.883 148.339 182.69 145.534Z\"/>\n      <path fill=\"#D8F6FE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M182.69 145.534C183.516 144.762 184.487 144.24 185.455 143.668C184.695 144.561 183.957 145.481 183.109 146.291L182.69 145.534Z\"/>\n      <path fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M447.576 122.082L447.933 122.245C447.943 122.323 447.962 122.4 447.964 122.479C447.992 123.482 447.354 124.889 446.974 125.827C443.934 133.337 438.465 140.936 432.783 146.629C410.823 168.629 386.3 165.139 358.328 165.177C341.352 165.201 324.158 167.556 311.612 180.215C298.964 192.977 296.944 207.057 297.016 224.251C321.839 224.244 346.708 224.743 371.523 224.23C369.17 232.977 365.543 241.044 359.042 247.504C347.979 258.498 335.262 260.245 320.378 260.83C312.498 261.14 304.593 261.03 296.707 261.028C296.877 280.904 298.281 301.484 291.257 320.429C286.191 332.759 277.818 343.449 267.062 351.322C263.135 354.151 258.832 356.508 254.66 358.958C258.518 345.004 257.502 329.851 257.502 315.531L257.586 246.608C257.533 229.175 256.204 211.452 259.223 194.19C260.881 184.741 264.28 175.683 269.247 167.476C283.632 144.02 308.07 130.549 335.114 127.815C349.639 126.347 366.122 127.347 380.875 127.467C396.2 127.592 411.767 127.875 426.999 125.998C433.964 125.14 440.761 123.734 447.576 122.082Z\"/>\n      <path fill=\"#4EA8D8\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M268.431 271.343C267.611 262.633 267.751 253.819 267.529 245.077C267.287 235.573 266.759 226.079 266.724 216.571C266.644 194.603 272.133 174.968 288.128 158.997C304.66 142.492 327.827 137.167 350.461 137.237L350.768 138.053C337.076 139.768 323.46 143.003 311.327 149.769C303.549 154.203 296.599 159.952 290.785 166.76C275.557 184.577 272.748 202.824 271.282 225.196L269.86 248.739C269.503 255.395 270.317 264.889 268.522 271.044C268.493 271.144 268.461 271.244 268.431 271.343Z\"/>\n      <path fill=\"#83A9C0\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M350.461 137.237C351.282 137.241 352.417 137.1 353.151 137.493C352.398 137.863 351.588 137.931 350.768 138.053L350.461 137.237Z\"/>\n      <path fillOpacity=\"0.89803922\" fill=\"#FEFEFE\" transform=\"matrix(0.195312 0 0 0.195312 0.00012207 0)\" d=\"M82.8344 405.924C84.4893 407.599 86.9167 409.519 88.1191 411.507L88.0738 411.69C87.9639 413.239 87.9654 414.724 86.8582 415.934C86.296 416.142 85.9657 416.321 85.366 416.052C85.1523 415.956 84.8106 415.677 84.6181 415.528C85.0782 415.532 85.5255 415.505 85.9844 415.475C86.5988 415.116 87.1053 414.596 87.4269 413.953C87.6724 413.463 87.7752 412.838 87.4857 412.34C87.2218 411.886 86.9501 411.693 86.4632 411.59C86.1124 411.999 85.7692 412.352 85.3578 412.701C85.2879 412.685 85.2151 412.679 85.1482 412.654C83.9708 412.202 82.0739 409.208 81.5892 408.032L81.7785 407.738L82.6435 407.659C82.917 407.325 82.5902 406.651 82.8344 405.924Z\"/>\n    </svg>\n  );\n}\n\nexport function FaviconLogo(props: SVGProps<SVGSVGElement>) {\n  return (\n    <Logo\n      {...props}\n      size={16}\n      decorative\n      className=\"favicon-logo\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWO,SAAS,KAAK,EACnB,OAAO,EAAE,EACT,QAAQ,YAAY,EACpB,aAAa,KAAK,EAClB,GAAG,OACO;IACV,kEAAkE;IAClE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACpB,MAAM,aAAa,CAAC,iBAAiB,EAAE,QAAQ,OAAO,CAAC,MAAM,KAAK;IAElE,MAAM,YAAY,cAAc,CAAC,QAC7B;QAAE,eAAe;IAAK,IACtB;QAAE,MAAM;QAAO,cAAc;IAAM;IAEvC,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,OAAM;QACN,WAAU;QACT,GAAG,SAAS;QACZ,GAAG,KAAK;;YAER,sBAAQ,8OAAC;0BAAO;;;;;uBAAiB;0BAClC,8OAAC;0BACC,cAAA,8OAAC;oBAAe,IAAI;oBAAY,eAAc;oBAAiB,IAAG;oBAAY,IAAG;oBAAY,IAAG;oBAAW,IAAG;;sCAC5G,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;sCAC3B,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAG/B,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BAEpC,8OAAC;gBAAK,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;gBAAE,WAAU;gBAA6C,GAAE;;;;;;0BAC5F,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;0BAC9E,8OAAC;gBAAK,aAAY;gBAAa,MAAK;gBAAU,WAAU;gBAA6C,GAAE;;;;;;;;;;;;AAG7G;AAEO,SAAS,YAAY,KAA8B;IACxD,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,MAAM;QACN,UAAU;QACV,WAAU;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/linguaflow-header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Setting<PERSON>, Moon, Sun, HelpCircle } from 'lucide-react';\nimport { Logo } from '@/components/icons/logo';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\n\ninterface LinguaFlowHeaderProps {\n  onSettingsClick: () => void;\n  onHelpClick: () => void;\n}\n\nexport function LinguaFlowHeader({ onSettingsClick, onHelpClick }: LinguaFlowHeaderProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const isRTL = uiLanguage === 'ar';\n\n  return (\n    <header className=\"sticky top-0 z-50 linguaflow-header shadow-sm\">\n      <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`flex justify-between items-center h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>\n          {/* Logo and Title */}\n          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <Logo className=\"h-8 w-8\" />\n              <h1 \n                className=\"text-xl font-bold\" \n                style={{ color: 'var(--text-primary)' }}\n              >\n                {t('appName')}\n              </h1>\n            </div>\n            \n            {/* Language Selector - Hidden on mobile */}\n            <div className={`hidden md:flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>\n              <span \n                className=\"text-sm\" \n                style={{ color: 'var(--text-secondary)' }}\n              >\n                {t('languageLabel')}:\n              </span>\n              <Select value={uiLanguage} onValueChange={setUiLanguage}>\n                <SelectTrigger \n                  className=\"w-auto min-w-[120px] h-8 text-sm linguaflow-input\"\n                  style={{\n                    backgroundColor: 'var(--bg-secondary)',\n                    borderColor: 'var(--border-color)',\n                    color: 'var(--text-primary)'\n                  }}\n                >\n                  <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                </SelectTrigger>\n                <SelectContent>\n                  {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                    <SelectItem key={lang.value} value={lang.value}>\n                      {t(lang.labelKey)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className={`flex items-center space-x-2 sm:space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onSettingsClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('settingsTitle')}\n            >\n              <Settings className=\"h-5 w-5\" />\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={effectiveTheme === 'dark' ? t('switchToLightMode') : t('switchToDarkMode')}\n            >\n              {effectiveTheme === 'dark' ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onHelpClick}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n              aria-label={t('helpTitle')}\n            >\n              <HelpCircle className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAgBO,SAAS,iBAAiB,EAAE,eAAe,EAAE,WAAW,EAAyB;IACtF,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,QAAQ,eAAe;IAE7B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,qBAAqB,IAAI;;kCAEzF,8OAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;0CAC7E,8OAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,oBAAoB,IAAI;;kDAC7E,8OAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAsB;kDAErC,EAAE;;;;;;;;;;;;0CAKP,8OAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,oBAAoB,IAAI;;kDACvF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAwB;;4CAEvC,EAAE;4CAAiB;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,8OAAC,kIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,OAAO;gDACT;0DAEA,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;kEAC3C,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,8OAAC;wBAAI,WAAW,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,IAAI;;0CAC1F,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,mBAAmB,SAAS,UAAU;gCAC9D,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,mBAAmB,SAAS,EAAE,uBAAuB,EAAE;0CAElE,mBAAmB,uBAClB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;gCACA,cAAY,EAAE;0CAEd,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/writing-tools-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport {\n  Wrench,\n  FileText,\n  Zap,\n  CheckCircle,\n  Wand2,\n  Lightbulb,\n  Download,\n  Trash2,\n  Bot,\n  Shield,\n  Search,\n  UserCheck,\n  PenLine\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface WritingToolsSidebarProps {\n  writingMode: string;\n  onWritingModeChange: (mode: string) => void;\n  onFileImport: (file: File) => void;\n  onCheckText: () => void;\n  onAiRewrite: () => void;\n  onBrainstorm: () => void;\n  onExport: () => void;\n  onClear: () => void;\n  onPlagiarismCheck: () => void;\n  onAiWritingDetection: () => void;\n  onHumanizeText: () => void;\n  aiPrompt: string;\n  onAiPromptChange: (prompt: string) => void;\n  onGenerateText: () => void;\n  wordCount: number;\n  charCount: number;\n  errorCount: number;\n  qualityScore: number;\n}\n\nexport function WritingToolsSidebar({\n  writingMode,\n  onWritingModeChange,\n  onFileImport,\n  onCheckText,\n  onAiRewrite,\n  onBrainstorm,\n  onExport,\n  onClear,\n  onPlagiarismCheck,\n  onAiWritingDetection,\n  onHumanizeText,\n  aiPrompt,\n  onAiPromptChange,\n  onGenerateText,\n  wordCount,\n  charCount,\n  errorCount,\n  qualityScore\n}: WritingToolsSidebarProps) {\n  const { t } = useI18n();\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onFileImport(file);\n    }\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Writing Tools Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <PenLine className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('writeToolsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Writing Mode */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('writingModeLabel')}\n            </label>\n            <Select value={writingMode} onValueChange={onWritingModeChange}>\n              <SelectTrigger className=\"linguaflow-input\">\n                <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"casual\">📝 {t('casualWritingMode')}</SelectItem>\n                <SelectItem value=\"formal\">👔 {t('formalWritingMode')}</SelectItem>\n                <SelectItem value=\"academic\">🎓 {t('academicWritingMode')}</SelectItem>\n                <SelectItem value=\"creative\">🎨 {t('creativeWritingMode')}</SelectItem>\n                <SelectItem value=\"business\">💼 {t('businessWritingMode')}</SelectItem>\n                <SelectItem value=\"technical\">⚙️ {t('technicalWritingMode')}</SelectItem>\n                <SelectItem value=\"professional\">💼 {t('professionalWritingMode')}</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Import Document */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('importDocumentTitle')}\n            </label>\n            <div \n              className=\"border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400\"\n              style={{ borderColor: 'var(--border-color)' }}\n              onClick={() => document.getElementById('fileInput')?.click()}\n            >\n              <FileText className=\"mx-auto h-8 w-8 mb-2\" style={{ color: 'var(--text-secondary)' }} />\n              <p className=\"text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                {t('dropzoneInstruction')}\n              </p>\n              <input\n                type=\"file\"\n                id=\"fileInput\"\n                className=\"hidden\"\n                accept=\".txt,.md,.docx\"\n                onChange={handleFileChange}\n              />\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              {t('quickActionTitle')}\n            </label>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onCheckText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <CheckCircle className=\"mr-1 h-4 w-4\" />\n                Check Text\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiRewrite}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Wand2 className=\"mr-1 h-4 w-4\" />\n                AI Rewrite ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onBrainstorm}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Lightbulb className=\"mr-1 h-4 w-4\" />\n                Brainstorm ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onPlagiarismCheck}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Shield className=\"mr-1 h-4 w-4\" />\n                Plagiarism\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onAiWritingDetection}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Search className=\"mr-1 h-4 w-4\" />\n                AI Detection\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onHumanizeText}\n                className=\"linguaflow-button-secondary\"\n              >\n                <UserCheck className=\"mr-1 h-4 w-4\" />\n                Humanize ✨\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onExport}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Download className=\"mr-1 h-4 w-4\" />\n                Export\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onClear}\n                className=\"linguaflow-button-secondary\"\n              >\n                <Trash2 className=\"mr-1 h-4 w-4\" />\n                {t('clearEditorButton')}\n              </Button>\n            </div>\n          </div>\n\n          {/* AI Text Generation */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\" style={{ color: 'var(--text-secondary)' }}>\n              AI Text Generation ✨\n            </label>\n            <Textarea\n              value={aiPrompt}\n              onChange={(e) => onAiPromptChange(e.target.value)}\n              rows={2}\n              className=\"linguaflow-input\"\n              placeholder={t('promptPlaceholder')}\n            />\n            <Button\n              onClick={onGenerateText}\n              className=\"linguaflow-button w-full\"\n              size=\"sm\"\n            >\n              <Bot className=\"mr-1 h-4 w-4\" />\n              Generate with AI ✨\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Writing Statistics Card */}\n      <Card className=\"linguaflow-stats\">\n        <CardContent className=\"p-4\">\n          <h4 className=\"font-semibold text-center mb-3 text-white\">\n            {t('writingStatsTitle')}\n          </h4>\n          <div className=\"space-y-2 text-sm text-white\">\n            <div className=\"flex justify-between items-center\">\n              <span>{t('wordCountLabel')}:</span>\n              <span className=\"font-medium\">{wordCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>{t('charCountLabel')}:</span>\n              <span className=\"font-medium\">{charCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Issues:</span>\n              <span className=\"font-medium\">{errorCount}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span>Score:</span>\n              <span className=\"font-medium text-lg\">{qualityScore}%</span>\n            </div>\n          </div>\n          <div className=\"mt-3 linguaflow-progress\">\n            <div \n              className=\"linguaflow-progress-bar\"\n              style={{ width: `${qualityScore}%` }}\n            />\n          </div>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAtBA;;;;;;;;AA6CO,SAAS,oBAAoB,EAClC,WAAW,EACX,mBAAmB,EACnB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,QAAQ,EACR,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACa;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACxE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAa,eAAe;;0DACzC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAS;4DAAI,EAAE;;;;;;;kEACjC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAW;4DAAI,EAAE;;;;;;;kEACnC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAY;4DAAI,EAAE;;;;;;;kEACpC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;;4DAAe;4DAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,aAAa;wCAAsB;wCAC5C,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc;;0DAErD,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,OAAO;oDAAE,OAAO;gDAAwB;;;;;;0DACnF,8OAAC;gDAAE,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAwB;0DAC5D,EAAE;;;;;;0DAEL,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,QAAO;gDACP,UAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAC5E,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;wCAAsB,OAAO;4CAAE,OAAO;wCAAwB;kDAAG;;;;;;kDAGlF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,MAAM;wCACN,WAAU;wCACV,aAAa,EAAE;;;;;;kDAEjB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;;0DAEL,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAM,EAAE;gDAAkB;;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAK,WAAU;;gDAAuB;gDAAa;;;;;;;;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/analysis-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { \n  Lightbulb, \n  MessageCircle, \n  History, \n  Search,\n  Sliders\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\ninterface AnalysisSidebarProps {\n  suggestions: Suggestion[];\n  toneAnalysis: ToneAnalysis;\n  recentPrompts: RecentPrompt[];\n  onApplySuggestion: (suggestionId: string) => void;\n  onAdjustTone: () => void;\n  onUsePrompt: (prompt: string) => void;\n}\n\nexport function AnalysisSidebar({\n  suggestions,\n  toneAnalysis,\n  recentPrompts,\n  onApplySuggestion,\n  onAdjustTone,\n  onUsePrompt\n}: AnalysisSidebarProps) {\n  const { t } = useI18n();\n\n  const getSuggestionTypeColor = (type: string) => {\n    switch (type) {\n      case 'grammar':\n      case 'spelling':\n        return 'var(--error-color)';\n      case 'style':\n        return 'var(--success-color)';\n      case 'clarity':\n        return 'var(--info-color)';\n      case 'tone':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n\n  const getSuggestionTypeIcon = (type: string) => {\n    const color = getSuggestionTypeColor(type);\n    return (\n      <div \n        className=\"w-3 h-3 rounded-full mr-2 flex-shrink-0\"\n        style={{ backgroundColor: color }}\n      />\n    );\n  };\n\n  return (\n    <aside className=\"space-y-6\">\n      {/* Suggestions Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <Lightbulb className=\"mr-2 h-5 w-5\" style={{ color: 'var(--warning-color)' }} />\n            {t('writingSuggestionsTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-60 linguaflow-scrollbar\">\n            {suggestions.length > 0 ? (\n              <div className=\"space-y-3\">\n                {suggestions.map((suggestion) => (\n                  <div\n                    key={suggestion.id}\n                    className=\"p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onApplySuggestion(suggestion.id)}\n                  >\n                    <div className=\"flex items-start\">\n                      {getSuggestionTypeIcon(suggestion.type)}\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium\" style={{ color: 'var(--text-primary)' }}>\n                          {suggestion.type.charAt(0).toUpperCase() + suggestion.type.slice(1)}\n                        </p>\n                        <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                          \"{suggestion.text}\" → \"{suggestion.suggestion}\"\n                        </p>\n                        {suggestion.explanation && (\n                          <p className=\"text-xs mt-1 italic\" style={{ color: 'var(--text-secondary)' }}>\n                            {suggestion.explanation}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\" style={{ color: 'var(--text-secondary)' }}>\n                <Search className=\"mx-auto h-8 w-8 mb-2\" />\n                <p className=\"text-sm\">\n                  {t('startTypingForSuggestionsDescription')}\n                </p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n\n      {/* Tone Analysis Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <MessageCircle className=\"mr-2 h-5 w-5\" style={{ color: 'var(--info-color)' }} />\n            {t('aiToneAnalysisTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-3\">\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>Overall Tone:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-accent)' }}>\n                {toneAnalysis.overall}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('formalityLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.formality}\n              </span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span style={{ color: 'var(--text-secondary)' }}>{t('confidenceLabel')}:</span>\n              <span className=\"font-medium\" style={{ color: 'var(--text-primary)' }}>\n                {toneAnalysis.confidence}\n              </span>\n            </div>\n          </div>\n          <Button\n            onClick={onAdjustTone}\n            variant=\"secondary\"\n            size=\"sm\"\n            className=\"w-full linguaflow-button-secondary\"\n          >\n            <Sliders className=\"mr-1 h-4 w-4\" />\n            Adjust Tone ✨\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* Recent AI Prompts Card */}\n      <Card className=\"linguaflow-card\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center text-base\" style={{ color: 'var(--text-primary)' }}>\n            <History className=\"mr-2 h-5 w-5\" style={{ color: 'var(--primary-color)' }} />\n            {t('generationHistoryTitle')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ScrollArea className=\"h-40 linguaflow-scrollbar\">\n            {recentPrompts.length > 0 ? (\n              <div className=\"space-y-2\">\n                {recentPrompts.map((prompt) => (\n                  <div\n                    key={prompt.id}\n                    className=\"p-2 rounded border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                    style={{ \n                      borderColor: 'var(--border-color)',\n                      backgroundColor: 'var(--bg-primary)'\n                    }}\n                    onClick={() => onUsePrompt(prompt.prompt)}\n                  >\n                    <p className=\"text-xs font-medium truncate\" style={{ color: 'var(--text-primary)' }}>\n                      {prompt.prompt}\n                    </p>\n                    <p className=\"text-xs mt-1\" style={{ color: 'var(--text-secondary)' }}>\n                      {prompt.timestamp.toLocaleDateString()}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-4\" style={{ color: 'var(--text-secondary)' }}>\n                <p className=\"text-sm\">{t('noGenerationsYetPlaceholder')}</p>\n              </div>\n            )}\n          </ScrollArea>\n        </CardContent>\n      </Card>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;AA4CO,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,aAAa,EACb,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACU;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,uBAAuB;QACrC,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,iBAAiB;YAAM;;;;;;IAGtC;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCAC1E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,YAAY,MAAM,GAAG,kBACpB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,kBAAkB,WAAW,EAAE;kDAE9C,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,sBAAsB,WAAW,IAAI;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAsB;sEACtE,WAAW,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC;;;;;;sEAEnE,8OAAC;4DAAE,WAAU;4DAAe,OAAO;gEAAE,OAAO;4DAAwB;;gEAAG;gEACnE,WAAW,IAAI;gEAAC;gEAAM,WAAW,UAAU;gEAAC;;;;;;;wDAE/C,WAAW,WAAW,kBACrB,8OAAC;4DAAE,WAAU;4DAAsB,OAAO;gEAAE,OAAO;4DAAwB;sEACxE,WAAW,WAAW;;;;;;;;;;;;;;;;;;uCAnB1B,WAAW,EAAE;;;;;;;;;qDA4BxB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;;kDACxE,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAoB;;;;;;gCAC3E,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;0DACjD,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAqB;0DAChE,aAAa,OAAO;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAkB;;;;;;;0DACtE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,SAAS;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAwB;;oDAAI,EAAE;oDAAmB;;;;;;;0DACvE,8OAAC;gDAAK,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAsB;0DACjE,aAAa,UAAU;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;4BAA8B,OAAO;gCAAE,OAAO;4BAAsB;;8CACvF,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;oCAAe,OAAO;wCAAE,OAAO;oCAAuB;;;;;;gCACxE,EAAE;;;;;;;;;;;;kCAGP,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,cAAc,MAAM,GAAG,kBACtB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,aAAa;4CACb,iBAAiB;wCACnB;wCACA,SAAS,IAAM,YAAY,OAAO,MAAM;;0DAExC,8OAAC;gDAAE,WAAU;gDAA+B,OAAO;oDAAE,OAAO;gDAAsB;0DAC/E,OAAO,MAAM;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;gDAAe,OAAO;oDAAE,OAAO;gDAAwB;0DACjE,OAAO,SAAS,CAAC,kBAAkB;;;;;;;uCAZjC,OAAO,EAAE;;;;;;;;;qDAkBpB,8OAAC;gCAAI,WAAU;gCAAmB,OAAO;oCAAE,OAAO;gCAAwB;0CACxE,cAAA,8OAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/linguaflow-editor.tsx"], "sourcesContent": ["'use client';\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport {\n  Undo2,\n  Redo2,\n  Wand2,\n  Circle,\n  X,\n  Bold,\n  Italic,\n  Underline,\n  AlignLeft,\n  AlignCenter,\n  AlignRight,\n  Copy,\n  Sparkles\n} from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface LinguaFlowEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n  isAutoCorrectionEnabled: boolean;\n  onToggleAutoCorrection: () => void;\n  suggestions: any[];\n  onApplySuggestion: (suggestionId: string) => void;\n  direction: 'ltr' | 'rtl';\n  currentLanguage: string;\n  lastSaved?: Date;\n  cursorPosition?: { line: number; col: number };\n  selectionInfo?: string;\n}\n\nexport function LinguaFlowEditor({\n  value,\n  onChange,\n  onUndo,\n  onRedo,\n  canUndo,\n  canRedo,\n  isAutoCorrectionEnabled,\n  onToggleAutoCorrection,\n  suggestions,\n  onApplySuggestion,\n  direction,\n  currentLanguage,\n  lastSaved,\n  cursorPosition = { line: 1, col: 1 },\n  selectionInfo = ''\n}: LinguaFlowEditorProps) {\n  const { t } = useI18n();\n  const editorRef = useRef<HTMLDivElement>(null);\n  const [selectedText, setSelectedText] = useState('');\n  const [showAiActions, setShowAiActions] = useState(false);\n  const [aiActionPosition, setAiActionPosition] = useState({ x: 0, y: 0 });\n  const [isClient, setIsClient] = useState(false);\n  const [fontFamily, setFontFamily] = useState('inter');\n  const [fontSize, setFontSize] = useState('14');\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    if (selection && selection.toString().trim()) {\n      const selectedText = selection.toString().trim();\n      setSelectedText(selectedText);\n      \n      // Get selection position for AI actions panel\n      const range = selection.getRangeAt(0);\n      const rect = range.getBoundingClientRect();\n      setAiActionPosition({ x: rect.left, y: rect.bottom + 10 });\n      setShowAiActions(true);\n    } else {\n      setShowAiActions(false);\n      setSelectedText('');\n    }\n  }, []);\n\n  const handleEditorChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {\n    const newValue = e.currentTarget.textContent || '';\n    onChange(newValue);\n  }, [onChange]);\n\n  const aiActions = [\n    { id: 'rewrite-formal', label: 'Formalize', action: () => console.log('Formalize') },\n    { id: 'rewrite-casual', label: 'Casualize', action: () => console.log('Casualize') },\n    { id: 'rewrite-shorter', label: 'Shorten', action: () => console.log('Shorten') },\n    { id: 'rewrite-longer', label: 'Lengthen', action: () => console.log('Lengthen') },\n    { id: 'summarize', label: 'Summarize', action: () => console.log('Summarize') },\n    { id: 'explain', label: 'Explain', action: () => console.log('Explain') },\n  ];\n\n  return (\n    <section className=\"space-y-4\">\n      {/* Main Editor Card */}\n      <Card className=\"linguaflow-card shadow-lg\">\n        <CardHeader className=\"border-b px-4 py-3\" style={{ borderColor: 'var(--border-color)' }}>\n          {/* Top Row - Title and Controls */}\n          <div className=\"flex flex-row items-center justify-between mb-3\">\n            <h3 className=\"font-semibold\" style={{ color: 'var(--text-primary)' }}>\n              Document Editor\n            </h3>\n            <div className=\"flex items-center space-x-2\">\n              {/* Status Indicator */}\n              <div className=\"flex items-center space-x-1 text-sm\" style={{ color: 'var(--text-secondary)' }}>\n                <Circle\n                  className=\"h-3 w-3 fill-current\"\n                  style={{ color: 'var(--success-color)' }}\n                />\n                <span>Ready</span>\n              </div>\n\n              {/* Auto-correction Toggle */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onToggleAutoCorrection}\n                className=\"p-1 rounded\"\n                style={{\n                  color: isAutoCorrectionEnabled ? 'var(--primary-color)' : 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title={isAutoCorrectionEnabled ? 'Auto-correction enabled' : 'Auto-correction disabled'}\n              >\n                <Wand2 className=\"h-4 w-4\" />\n              </Button>\n\n              {/* Undo/Redo */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onUndo}\n                disabled={!canUndo}\n                className=\"p-1 rounded\"\n                style={{\n                  color: 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title=\"Undo (Ctrl+Z)\"\n              >\n                <Undo2 className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onRedo}\n                disabled={!canRedo}\n                className=\"p-1 rounded\"\n                style={{\n                  color: 'var(--text-secondary)',\n                  backgroundColor: 'var(--bg-alt)'\n                }}\n                title=\"Redo (Ctrl+Y)\"\n              >\n                <Redo2 className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n\n          {/* Formatting Toolbar */}\n          <div className=\"flex items-center space-x-1 border-t pt-3\" style={{ borderColor: 'var(--border-color)' }}>\n            {/* Font Formatting */}\n            <div className=\"flex items-center space-x-1 border-r pr-2\" style={{ borderColor: 'var(--border-color)' }}>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 font-bold\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Bold (Ctrl+B)\"\n              >\n                <Bold className=\"h-4 w-4 mr-1\" />\n                B\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 italic\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Italic (Ctrl+I)\"\n              >\n                <Italic className=\"h-4 w-4 mr-1\" />\n                I\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-8 px-2 underline\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Underline (Ctrl+U)\"\n              >\n                <Underline className=\"h-4 w-4 mr-1\" />\n                U\n              </Button>\n            </div>\n\n            {/* Text Alignment */}\n            <div className=\"flex items-center space-x-1 border-r pr-2\" style={{ borderColor: 'var(--border-color)' }}>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Left\"\n              >\n                <AlignLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Center\"\n              >\n                <AlignCenter className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Align Right\"\n              >\n                <AlignRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {/* Font Selection */}\n            <select\n              className=\"h-8 px-2 text-sm border rounded\"\n              style={{\n                backgroundColor: 'var(--bg-primary)',\n                borderColor: 'var(--border-color)',\n                color: 'var(--text-primary)'\n              }}\n              title=\"Font Family\"\n              value={fontFamily}\n              onChange={(e) => setFontFamily(e.target.value)}\n            >\n              <option value=\"inter\">Inter</option>\n              <option value=\"arial\">Arial</option>\n              <option value=\"helvetica\">Helvetica</option>\n              <option value=\"times\">Times New Roman</option>\n              <option value=\"georgia\">Georgia</option>\n              <option value=\"courier\">Courier New</option>\n            </select>\n\n            <select\n              className=\"h-8 px-2 text-sm border rounded\"\n              style={{\n                backgroundColor: 'var(--bg-primary)',\n                borderColor: 'var(--border-color)',\n                color: 'var(--text-primary)'\n              }}\n              title=\"Font Size\"\n              value={fontSize}\n              onChange={(e) => setFontSize(e.target.value)}\n            >\n              <option value=\"12\">12px</option>\n              <option value=\"14\">14px</option>\n              <option value=\"16\">16px</option>\n              <option value=\"18\">18px</option>\n              <option value=\"20\">20px</option>\n              <option value=\"24\">24px</option>\n            </select>\n          </div>\n        </CardHeader>\n\n        {/* Editor Content */}\n        <div className=\"linguaflow-editor linguaflow-scrollbar\">\n          <div\n            ref={editorRef}\n            contentEditable\n            suppressContentEditableWarning\n            onInput={handleEditorChange}\n            onMouseUp={handleTextSelection}\n            onKeyUp={handleTextSelection}\n            className=\"p-6 min-h-[380px] outline-none\"\n            style={{\n              color: 'var(--text-primary)',\n              lineHeight: '1.75',\n              direction: direction,\n              textAlign: direction === 'rtl' ? 'right' : 'left'\n            }}\n            data-placeholder={t('editorPlaceholder')}\n            dangerouslySetInnerHTML={{ __html: value }}\n          />\n        </div>\n\n        {/* Editor Footer */}\n        <div\n          className=\"border-t px-4 py-2 flex items-center justify-between text-xs\"\n          style={{\n            borderColor: 'var(--border-color)',\n            backgroundColor: 'var(--bg-alt)',\n            color: 'var(--text-secondary)'\n          }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <span>\n              Last saved: <span className=\"font-medium\">\n                {isClient && lastSaved ? lastSaved.toLocaleTimeString() : 'Never'}\n              </span>\n            </span>\n            <span>\n              Language: <span className=\"font-medium\">{currentLanguage}</span>\n            </span>\n\n            {/* Smart Synonyms and Copy Text Buttons */}\n            <div className=\"flex items-center space-x-2 ml-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 px-2 text-xs\"\n                style={{ color: 'var(--primary-color)' }}\n                title=\"Smart Synonyms\"\n              >\n                <Sparkles className=\"h-3 w-3 mr-1\" />\n                Smart Synonyms\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 px-2 text-xs\"\n                style={{ color: 'var(--text-secondary)' }}\n                title=\"Copy Text\"\n              >\n                <Copy className=\"h-3 w-3 mr-1\" />\n                Copy Text\n              </Button>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {selectionInfo && <span>{selectionInfo}</span>}\n            <span>Line {cursorPosition.line}, Col {cursorPosition.col}</span>\n          </div>\n        </div>\n      </Card>\n\n      {/* AI Context Panel */}\n      {showAiActions && selectedText && (\n        <Card \n          className=\"linguaflow-card animate-in fade-in-0 zoom-in-95\"\n          style={{\n            position: 'fixed',\n            top: aiActionPosition.y,\n            left: aiActionPosition.x,\n            zIndex: 1000,\n            maxWidth: '400px'\n          }}\n        >\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-semibold flex items-center text-sm\" style={{ color: 'var(--primary-color)' }}>\n                <Wand2 className=\"mr-2 h-4 w-4\" />\n                AI Actions for Selection ✨\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setShowAiActions(false)}\n                className=\"h-6 w-6 p-1\"\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <p \n              className=\"text-sm italic p-2 rounded\"\n              style={{ \n                color: 'var(--text-secondary)',\n                backgroundColor: 'var(--bg-alt)'\n              }}\n            >\n              \"{selectedText}\"\n            </p>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n              {aiActions.map((action) => (\n                <Button\n                  key={action.id}\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={action.action}\n                  className=\"linguaflow-button-secondary text-xs\"\n                >\n                  {action.label}\n                </Button>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AApBA;;;;;;;AAwCO,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,uBAAuB,EACvB,sBAAsB,EACtB,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,eAAe,EACf,SAAS,EACT,iBAAiB;IAAE,MAAM;IAAG,KAAK;AAAE,CAAC,EACpC,gBAAgB,EAAE,EACI;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,YAAY,OAAO,YAAY;QACrC,IAAI,aAAa,UAAU,QAAQ,GAAG,IAAI,IAAI;YAC5C,MAAM,eAAe,UAAU,QAAQ,GAAG,IAAI;YAC9C,gBAAgB;YAEhB,8CAA8C;YAC9C,MAAM,QAAQ,UAAU,UAAU,CAAC;YACnC,MAAM,OAAO,MAAM,qBAAqB;YACxC,oBAAoB;gBAAE,GAAG,KAAK,IAAI;gBAAE,GAAG,KAAK,MAAM,GAAG;YAAG;YACxD,iBAAiB;QACnB,OAAO;YACL,iBAAiB;YACjB,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI;QAChD,SAAS;IACX,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAkB,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QACnF;YAAE,IAAI;YAAmB,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;QAChF;YAAE,IAAI;YAAkB,OAAO;YAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAY;QACjF;YAAE,IAAI;YAAa,OAAO;YAAa,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAa;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAW,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAW;KACzE;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;wBAAqB,OAAO;4BAAE,aAAa;wBAAsB;;0CAErF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;wCAAgB,OAAO;4CAAE,OAAO;wCAAsB;kDAAG;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;gDAAsC,OAAO;oDAAE,OAAO;gDAAwB;;kEAC3F,8OAAC,sMAAA,CAAA,SAAM;wDACL,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAuB;;;;;;kEAEzC,8OAAC;kEAAK;;;;;;;;;;;;0DAIR,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;gDACV,OAAO;oDACL,OAAO,0BAA0B,yBAAyB;oDAC1D,iBAAiB;gDACnB;gDACA,OAAO,0BAA0B,4BAA4B;0DAE7D,cAAA,8OAAC,+MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAInB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,OAAM;0DAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,OAAM;0DAEN,cAAA,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC;gCAAI,WAAU;gCAA4C,OAAO;oCAAE,aAAa;gCAAsB;;kDAErG,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,aAAa;wCAAsB;;0DACrG,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAM1C,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,aAAa;wCAAsB;;0DACrG,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;0DAEN,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,OAAO;wCACT;wCACA,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;0DAE7C,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,OAAO;wCACT;wCACA,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;0DAE3C,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,eAAe;4BACf,8BAA8B;4BAC9B,SAAS;4BACT,WAAW;4BACX,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,YAAY;gCACZ,WAAW;gCACX,WAAW,cAAc,QAAQ,UAAU;4BAC7C;4BACA,oBAAkB,EAAE;4BACpB,yBAAyB;gCAAE,QAAQ;4BAAM;;;;;;;;;;;kCAK7C,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,aAAa;4BACb,iBAAiB;4BACjB,OAAO;wBACT;;0CAEA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;0DACQ,8OAAC;gDAAK,WAAU;0DACzB,YAAY,YAAY,UAAU,kBAAkB,KAAK;;;;;;;;;;;;kDAG9D,8OAAC;;4CAAK;0DACM,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAI3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAuB;gDACvC,OAAM;;kEAEN,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAAwB;gDACxC,OAAM;;kEAEN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;oCACZ,+BAAiB,8OAAC;kDAAM;;;;;;kDACzB,8OAAC;;4CAAK;4CAAM,eAAe,IAAI;4CAAC;4CAAO,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAM9D,iBAAiB,8BAChB,8OAAC,gIAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK,iBAAiB,CAAC;oBACvB,MAAM,iBAAiB,CAAC;oBACxB,QAAQ;oBACR,UAAU;gBACZ;;kCAEA,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAA0C,OAAO;wCAAE,OAAO;oCAAuB;;sDAC7F,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;;oCACD;oCACG;oCAAa;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,uBACd,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,OAAO,MAAM;wCACtB,WAAU;kDAET,OAAO,KAAK;uCANR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}, {"offset": {"line": 2909, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3041, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3077, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3162, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/modals/settings-modal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\n\ninterface SettingsModalProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function SettingsModal({ open, onOpenChange }: SettingsModalProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, fontSize, setFontSize, highContrastMode, setHighContrastMode } = useAppearance();\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>{t('settingsTitle')}</DialogTitle>\n        </DialogHeader>\n\n        <Tabs defaultValue=\"language\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"language\">{t('languageLabel')}</TabsTrigger>\n            <TabsTrigger value=\"appearance\">{t('appearanceLabel')}</TabsTrigger>\n            <TabsTrigger value=\"features\">{t('featuresLabel')}</TabsTrigger>\n            <TabsTrigger value=\"advanced\">{t('advancedSettingsLabel')}</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"language\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('languageLabel')}</CardTitle>\n                <CardDescription>{t('uiLanguageDescription')}</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"ui-language\">{t('uiLanguageLabel')}</Label>\n                  <Select value={uiLanguage} onValueChange={setUiLanguage}>\n                    <SelectTrigger>\n                      <SelectValue placeholder={t('selectUiLanguagePlaceholder')} />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                        <SelectItem key={lang.value} value={lang.value}>\n                          {t(lang.labelKey)}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"appearance\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('appearanceLabel')}</CardTitle>\n                <CardDescription>{t('themeDescription')}</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"theme\">{t('themeLabel')}</Label>\n                  <Select value={theme} onValueChange={setTheme}>\n                    <SelectTrigger>\n                      <SelectValue placeholder={t('selectThemePlaceholder')} />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"light\">{t('themeLight')}</SelectItem>\n                      <SelectItem value=\"dark\">{t('themeDark')}</SelectItem>\n                      <SelectItem value=\"system\">{t('themeSystem')}</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"font-size\">{t('fontSizeLabel')}</Label>\n                  <Select value={fontSize} onValueChange={setFontSize}>\n                    <SelectTrigger>\n                      <SelectValue placeholder={t('selectFontSizePlaceholder')} />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"small\">{t('fontSizeSmall')}</SelectItem>\n                      <SelectItem value=\"medium\">{t('fontSizeMedium')}</SelectItem>\n                      <SelectItem value=\"large\">{t('fontSizeLarge')}</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <Switch\n                    id=\"high-contrast\"\n                    checked={highContrastMode}\n                    onCheckedChange={setHighContrastMode}\n                  />\n                  <Label htmlFor=\"high-contrast\">{t('highContrastModeLabel')}</Label>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"features\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('featuresLabel')}</CardTitle>\n                <CardDescription>{t('featureSettingsDescription')}</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"auto-correction\" defaultChecked />\n                  <Label htmlFor=\"auto-correction\">{t('automaticTextCorrectionLabel')}</Label>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"real-time-grammar\" defaultChecked />\n                  <Label htmlFor=\"real-time-grammar\">{t('realTimeGrammarCheckingLabel')}</Label>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"real-time-spell\" defaultChecked />\n                  <Label htmlFor=\"real-time-spell\">{t('realTimeSpellCheckingLabel')}</Label>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"style-suggestions\" defaultChecked />\n                  <Label htmlFor=\"style-suggestions\">{t('styleSuggestionsLabel')}</Label>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"advanced\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('advancedSettingsLabel')}</CardTitle>\n                <CardDescription>{t('advancedSettingsDescription')}</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"offline-mode\" />\n                  <Label htmlFor=\"offline-mode\">{t('enableOfflineFunctionalityLabel')}</Label>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Switch id=\"auto-language-detection\" defaultChecked />\n                  <Label htmlFor=\"auto-language-detection\">{t('enableAutomaticLanguageDetectionLabel')}</Label>\n                </div>\n                <div className=\"pt-4\">\n                  <Button variant=\"destructive\" size=\"sm\">\n                    {t('resetAllSettingsLabel')}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n\n        <div className=\"flex justify-end space-x-2 pt-4\">\n          <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\n            {t('cancelButton')}\n          </Button>\n          <Button onClick={() => onOpenChange(false)}>\n            Save Changes\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAmBO,SAAS,cAAc,EAAE,IAAI,EAAE,YAAY,EAAsB;IACtE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAEtG,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAE,EAAE;;;;;;;;;;;8BAGlB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY,EAAE;;;;;;8CACjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAc,EAAE;;;;;;8CACnC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY,EAAE;;;;;;8CACjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY,EAAE;;;;;;;;;;;;sCAGnC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAE,EAAE;;;;;;0DACd,8OAAC,gIAAA,CAAA,kBAAe;0DAAE,EAAE;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe,EAAE;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAY,eAAe;;sEACxC,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAa,EAAE;;;;;;;;;;;sEAE9B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,KAAK,KAAK;8EAC3C,EAAE,KAAK,QAAQ;mEADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWzC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAE,EAAE;;;;;;0DACd,8OAAC,gIAAA,CAAA,kBAAe;0DAAE,EAAE;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS,EAAE;;;;;;kEAC1B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAO,eAAe;;0EACnC,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAa,EAAE;;;;;;;;;;;0EAE9B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS,EAAE;;;;;;kFAC7B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ,EAAE;;;;;;kFAC5B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0DAKpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa,EAAE;;;;;;kEAC9B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAU,eAAe;;0EACtC,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAa,EAAE;;;;;;;;;;;0EAE9B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS,EAAE;;;;;;kFAC7B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU,EAAE;;;;;;kFAC9B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB;;;;;;kEAEnB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM1C,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAE,EAAE;;;;;;0DACd,8OAAC,gIAAA,CAAA,kBAAe;0DAAE,EAAE;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;wDAAkB,cAAc;;;;;;kEAC3C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAmB,EAAE;;;;;;;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;wDAAoB,cAAc;;;;;;kEAC7C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAqB,EAAE;;;;;;;;;;;;0DAExC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;wDAAkB,cAAc;;;;;;kEAC3C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAmB,EAAE;;;;;;;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;wDAAoB,cAAc;;;;;;kEAC7C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM9C,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAE,EAAE;;;;;;0DACd,8OAAC,gIAAA,CAAA,kBAAe;0DAAE,EAAE;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;;;;;;kEACX,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAgB,EAAE;;;;;;;;;;;;0DAEnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,IAAG;wDAA0B,cAAc;;;;;;kEACnD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAA2B,EAAE;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAc,MAAK;8DAChC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,aAAa;sCACnD,EAAE;;;;;;sCAEL,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,aAAa;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}, {"offset": {"line": 3880, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/modals/help-modal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface HelpModalProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function HelpModal({ open, onOpenChange }: HelpModalProps) {\n  const { t } = useI18n();\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[80vh]\">\n        <DialogHeader>\n          <DialogTitle>{t('helpTitle')}</DialogTitle>\n        </DialogHeader>\n\n        <ScrollArea className=\"h-[60vh] pr-4\">\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpEditorTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpEditorDescription') }} />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpAiToolsTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpAiToolsDescription') }} />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpLanguageSettingsTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpLanguageSettingsDescription') }} />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpAppearanceSettingsTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpAppearanceSettingsDescription') }} />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpFeatureSettingsTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpFeatureSettingsDescription') }} />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>{t('helpAdvancedSettingsTitle')}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div dangerouslySetInnerHTML={{ __html: t('helpAdvancedSettingsDescription') }} />\n              </CardContent>\n            </Card>\n\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n              <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n                💡 {t('helpPanelTip')}\n              </p>\n            </div>\n          </div>\n        </ScrollArea>\n\n        <div className=\"flex justify-end pt-4\">\n          <Button onClick={() => onOpenChange(false)}>\n            Close\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAcO,SAAS,UAAU,EAAE,IAAI,EAAE,YAAY,EAAkB;IAC9D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAE,EAAE;;;;;;;;;;;8BAGlB,8OAAC,0IAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAAyB;;;;;;;;;;;;;;;;;0CAIvE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAA0B;;;;;;;;;;;;;;;;;0CAIxE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAAmC;;;;;;;;;;;;;;;;;0CAIjF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAAqC;;;;;;;;;;;;;;;;;0CAInF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAAkC;;;;;;;;;;;;;;;;;0CAIhF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;;;;;;kDAEhB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,yBAAyB;gDAAE,QAAQ,EAAE;4CAAmC;;;;;;;;;;;;;;;;;0CAIjF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAA2C;wCAClD,EAAE;;;;;;;;;;;;;;;;;;;;;;;8BAMd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,aAAa;kCAAQ;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}, {"offset": {"line": 4200, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/new-design.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect, useMemo } from 'react';\nimport { LinguaFlowHeader } from '@/components/layout/linguaflow-header';\nimport { WritingToolsSidebar } from '@/components/layout/writing-tools-sidebar';\nimport { AnalysisSidebar } from '@/components/layout/analysis-sidebar';\nimport { LinguaFlowEditor } from '@/components/editor/linguaflow-editor';\nimport { SettingsModal } from '@/components/modals/settings-modal';\nimport { HelpModal } from '@/components/modals/help-modal';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\n\ninterface Suggestion {\n  id: string;\n  type: 'grammar' | 'spelling' | 'style' | 'clarity' | 'tone';\n  text: string;\n  suggestion: string;\n  explanation?: string;\n}\n\ninterface ToneAnalysis {\n  overall: string;\n  formality: string;\n  confidence: string;\n}\n\ninterface RecentPrompt {\n  id: string;\n  prompt: string;\n  timestamp: Date;\n}\n\nfunction NewDesignPage() {\n  const { t, uiLanguage, writingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n  const { effectiveTheme } = useAppearance();\n  const isRTL = uiLanguage === 'ar';\n\n  // Editor state\n  const [editorValue, setEditorValue] = useState('');\n  const [writingMode, setWritingMode] = useState('formal');\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const [isAutoCorrectionEnabled, setIsAutoCorrectionEnabled] = useState(true);\n  const [aiPrompt, setAiPrompt] = useState('');\n\n  // Statistics\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [errorCount, setErrorCount] = useState(0);\n  const [qualityScore, setQualityScore] = useState(100);\n\n  // Analysis data\n  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);\n  const [toneAnalysis, setToneAnalysis] = useState<ToneAnalysis>({\n    overall: 'Neutral',\n    formality: 'Professional (75%)',\n    confidence: 'Confident (80%)'\n  });\n  const [recentPrompts, setRecentPrompts] = useState<RecentPrompt[]>([]);\n\n  // Modal states\n  const [showSettings, setShowSettings] = useState(false);\n  const [showHelp, setShowHelp] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | undefined>(undefined);\n\n  // Set lastSaved on client side only\n  useEffect(() => {\n    setLastSaved(new Date());\n  }, []);\n\n  // Update statistics when editor value changes\n  useEffect(() => {\n    const words = editorValue.trim().split(/\\s+/).filter(word => word.length > 0);\n    setWordCount(words.length);\n    setCharCount(editorValue.length);\n    \n    // Mock error count and quality score\n    const errors = Math.max(0, Math.floor(words.length * 0.05) - Math.floor(Math.random() * 3));\n    setErrorCount(errors);\n    setQualityScore(Math.max(60, 100 - errors * 5));\n  }, [editorValue]);\n\n  // Handlers\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n    setLastSaved(new Date());\n  }, []);\n\n  const handleUndo = useCallback(() => {\n    // Implement undo logic\n    console.log('Undo');\n  }, []);\n\n  const handleRedo = useCallback(() => {\n    // Implement redo logic\n    console.log('Redo');\n  }, []);\n\n  const handleFileImport = useCallback((file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      setEditorValue(content);\n      toast({\n        title: t('toastFileImportSuccessTitle'),\n        description: t('toastFileImportSuccessMessage'),\n      });\n    };\n    reader.readAsText(file);\n  }, [toast, t]);\n\n  const handleCheckText = useCallback(() => {\n    // Mock suggestions\n    const mockSuggestions: Suggestion[] = [\n      {\n        id: '1',\n        type: 'grammar',\n        text: 'are',\n        suggestion: 'is',\n        explanation: 'Subject-verb agreement error'\n      },\n      {\n        id: '2',\n        type: 'style',\n        text: 'very good',\n        suggestion: 'excellent',\n        explanation: 'More precise word choice'\n      }\n    ];\n    setSuggestions(mockSuggestions);\n    toast({\n      title: 'Text Analysis Complete',\n      description: `Found ${mockSuggestions.length} suggestions`,\n    });\n  }, [toast]);\n\n  const handleAiRewrite = useCallback(() => {\n    toast({\n      title: 'AI Rewrite',\n      description: 'AI rewrite feature activated',\n    });\n  }, [toast]);\n\n  const handleBrainstorm = useCallback(() => {\n    toast({\n      title: 'Brainstorm',\n      description: 'Brainstorm feature activated',\n    });\n  }, [toast]);\n\n  const handleExport = useCallback(() => {\n    const blob = new Blob([editorValue], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'document.txt';\n    a.click();\n    URL.revokeObjectURL(url);\n    toast({\n      title: 'Export Complete',\n      description: 'Document exported successfully',\n    });\n  }, [editorValue, toast]);\n\n  const handleClear = useCallback(() => {\n    setEditorValue('');\n    setSuggestions([]);\n    toast({\n      title: t('toastEditorClearedSuccess'),\n    });\n  }, [toast, t]);\n\n  const handleGenerateText = useCallback(() => {\n    if (!aiPrompt.trim()) {\n      toast({\n        title: t('toastInputRequiredTitle'),\n        description: t('toastPromptRequiredError'),\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    // Mock text generation\n    const generatedText = `Generated text based on: \"${aiPrompt}\"`;\n    setEditorValue(prev => prev + (prev ? '\\n\\n' : '') + generatedText);\n    \n    // Add to recent prompts\n    const newPrompt: RecentPrompt = {\n      id: Date.now().toString(),\n      prompt: aiPrompt,\n      timestamp: new Date()\n    };\n    setRecentPrompts(prev => [newPrompt, ...prev.slice(0, 4)]);\n    setAiPrompt('');\n    \n    toast({\n      title: t('toastTextGeneratedSuccess'),\n    });\n  }, [aiPrompt, toast, t]);\n\n  const handleApplySuggestion = useCallback((suggestionId: string) => {\n    const suggestion = suggestions.find(s => s.id === suggestionId);\n    if (suggestion) {\n      const newValue = editorValue.replace(suggestion.text, suggestion.suggestion);\n      setEditorValue(newValue);\n      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n      toast({\n        title: t('toastSuggestionAppliedSuccess'),\n      });\n    }\n  }, [suggestions, editorValue, toast, t]);\n\n  const handleAdjustTone = useCallback(() => {\n    toast({\n      title: 'Tone Adjustment',\n      description: 'Tone adjustment feature activated',\n    });\n  }, [toast]);\n\n  const handleUsePrompt = useCallback((prompt: string) => {\n    setAiPrompt(prompt);\n  }, []);\n\n  const handlePlagiarismCheck = useCallback(() => {\n    toast({\n      title: 'Plagiarism Detection',\n      description: 'Plagiarism detection feature activated',\n    });\n  }, [toast]);\n\n  const handleAiWritingDetection = useCallback(() => {\n    toast({\n      title: 'AI Writing Detection',\n      description: 'AI writing detection feature activated',\n    });\n  }, [toast]);\n\n  const handleHumanizeText = useCallback(() => {\n    toast({\n      title: 'Humanize Text',\n      description: 'Text humanization feature activated',\n    });\n  }, [toast]);\n\n  const writingDirection = writingLanguageDialect?.startsWith('ar') ? 'rtl' : 'ltr';\n  const currentLanguage = writingLanguageDialect || 'English';\n\n  return (\n    <div\n      className=\"min-h-screen transition-all duration-300\"\n      data-theme={effectiveTheme}\n      style={{\n        backgroundColor: 'var(--bg-primary)',\n        color: 'var(--text-primary)'\n      }}\n    >\n      {/* Header */}\n      <LinguaFlowHeader\n        onSettingsClick={() => setShowSettings(true)}\n        onHelpClick={() => setShowHelp(true)}\n      />\n\n      {/* Main Content */}\n      <main className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className={`grid grid-cols-1 lg:grid-cols-12 gap-6 ${isRTL ? 'direction-rtl' : ''}`}>\n          {/* Left Sidebar - Writing Tools */}\n          <div className=\"lg:col-span-3\">\n            <WritingToolsSidebar\n              writingMode={writingMode}\n              onWritingModeChange={setWritingMode}\n              onFileImport={handleFileImport}\n              onCheckText={handleCheckText}\n              onAiRewrite={handleAiRewrite}\n              onBrainstorm={handleBrainstorm}\n              onExport={handleExport}\n              onClear={handleClear}\n              onPlagiarismCheck={handlePlagiarismCheck}\n              onAiWritingDetection={handleAiWritingDetection}\n              onHumanizeText={handleHumanizeText}\n              aiPrompt={aiPrompt}\n              onAiPromptChange={setAiPrompt}\n              onGenerateText={handleGenerateText}\n              wordCount={wordCount}\n              charCount={charCount}\n              errorCount={errorCount}\n              qualityScore={qualityScore}\n            />\n          </div>\n\n          {/* Center - Editor */}\n          <div className=\"lg:col-span-6\">\n            <LinguaFlowEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n              isAutoCorrectionEnabled={isAutoCorrectionEnabled}\n              onToggleAutoCorrection={() => setIsAutoCorrectionEnabled(!isAutoCorrectionEnabled)}\n              suggestions={suggestions}\n              onApplySuggestion={handleApplySuggestion}\n              direction={writingDirection}\n              currentLanguage={currentLanguage}\n              lastSaved={lastSaved}\n            />\n          </div>\n\n          {/* Right Sidebar - Analysis */}\n          <div className=\"lg:col-span-3\">\n            <AnalysisSidebar\n              suggestions={suggestions}\n              toneAnalysis={toneAnalysis}\n              recentPrompts={recentPrompts}\n              onApplySuggestion={handleApplySuggestion}\n              onAdjustTone={handleAdjustTone}\n              onUsePrompt={handleUsePrompt}\n            />\n          </div>\n        </div>\n      </main>\n\n      {/* Modals */}\n      <SettingsModal open={showSettings} onOpenChange={setShowSettings} />\n      <HelpModal open={showHelp} onOpenChange={setShowHelp} />\n    </div>\n  );\n}\n\nexport default NewDesignPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAkCA,SAAS;IACP,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACtC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACvC,MAAM,QAAQ,eAAe;IAE7B,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,SAAS;QACT,WAAW;QACX,YAAY;IACd;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAE7D,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,IAAI;IACnB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAC3E,aAAa,MAAM,MAAM;QACzB,aAAa,YAAY,MAAM;QAE/B,qCAAqC;QACrC,MAAM,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACxF,cAAc;QACd,gBAAgB,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS;IAC9C,GAAG;QAAC;KAAY;IAEhB,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;QACf,aAAa,IAAI;IACnB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,uBAAuB;QACvB,QAAQ,GAAG,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,eAAe;YACf,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;YACjB;QACF;QACA,OAAO,UAAU,CAAC;IACpB,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,mBAAmB;QACnB,MAAM,kBAAgC;YACpC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;SACD;QACD,eAAe;QACf,MAAM;YACJ,OAAO;YACP,aAAa,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;QAC5D;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;QACpB,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,eAAe;QACf,eAAe,EAAE;QACjB,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAO;KAAE;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,uBAAuB;QACvB,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;QAC9D,eAAe,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;QAErD,wBAAwB;QACxB,MAAM,YAA0B;YAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ;YACR,WAAW,IAAI;QACjB;QACA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAc,KAAK,KAAK,CAAC,GAAG;aAAG;QACzD,YAAY;QAEZ,MAAM;YACJ,OAAO,EAAE;QACX;IACF,GAAG;QAAC;QAAU;QAAO;KAAE;IAEvB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,YAAY;YACd,MAAM,WAAW,YAAY,OAAO,CAAC,WAAW,IAAI,EAAE,WAAW,UAAU;YAC3E,eAAe;YACf,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACjD,MAAM;gBACJ,OAAO,EAAE;YACX;QACF;IACF,GAAG;QAAC;QAAa;QAAa;QAAO;KAAE;IAEvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,wBAAwB,WAAW,QAAQ,QAAQ;IAC5E,MAAM,kBAAkB,0BAA0B;IAElD,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;0BAGA,8OAAC,oJAAA,CAAA,mBAAgB;gBACf,iBAAiB,IAAM,gBAAgB;gBACvC,aAAa,IAAM,YAAY;;;;;;0BAIjC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,kBAAkB,IAAI;;sCAEtF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,sBAAmB;gCAClB,aAAa;gCACb,qBAAqB;gCACrB,cAAc;gCACd,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,UAAU;gCACV,SAAS;gCACT,mBAAmB;gCACnB,sBAAsB;gCACtB,gBAAgB;gCAChB,UAAU;gCACV,kBAAkB;gCAClB,gBAAgB;gCAChB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,cAAc;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oJAAA,CAAA,mBAAgB;gCACf,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,SAAS;gCACT,SAAS;gCACT,yBAAyB;gCACzB,wBAAwB,IAAM,2BAA2B,CAAC;gCAC1D,aAAa;gCACb,mBAAmB;gCACnB,WAAW;gCACX,iBAAiB;gCACjB,WAAW;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mJAAA,CAAA,kBAAe;gCACd,aAAa;gCACb,cAAc;gCACd,eAAe;gCACf,mBAAmB;gCACnB,cAAc;gCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;0BAOrB,8OAAC,iJAAA,CAAA,gBAAa;gBAAC,MAAM;gBAAc,cAAc;;;;;;0BACjD,8OAAC,6IAAA,CAAA,YAAS;gBAAC,MAAM;gBAAU,cAAc;;;;;;;;;;;;AAG/C;uCAEe", "debugId": null}}, {"offset": {"line": 4596, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport NewDesignPage from \"./new-design\";\n\nexport default function LinguaFlowPageContainer() {\n  return <NewDesignPage />;\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,4HAAA,CAAA,UAAa;;;;;AACvB", "debugId": null}}]}