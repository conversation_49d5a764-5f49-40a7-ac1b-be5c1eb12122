{"appName": "LinguaFlow", "appDescription": "Dilbilgisi Düzeltme ve Yazma Asistanı", "editorTitle": "D<PERSON><PERSON>leyici", "rephraseSelectionButton": "Seçimi Yeniden İfade Et", "writingStatsTitle": "<PERSON><PERSON><PERSON>", "wordCountLabel": "<PERSON><PERSON><PERSON>", "charCountLabel": "<PERSON><PERSON><PERSON>", "writingScoreLabel": "Yazma Puanı", "writingScoreUnit": "/ 100", "writingModeLabel": "<PERSON><PERSON><PERSON>", "selectWritingModePlaceholder": "<PERSON><PERSON><PERSON> modunu se<PERSON>", "formalWritingMode": "<PERSON><PERSON><PERSON>", "casualWritingMode": "Günlük", "professionalWritingMode": "Profesyonel", "creativeWritingMode": "Yaratıcı", "technicalWritingMode": "Teknik", "academicWritingMode": "Akademik", "businessWritingMode": "İş", "aiToneAnalysisAccordionTitle": "<PERSON><PERSON>", "aiToneAnalysisTitle": "<PERSON><PERSON>", "aiToneAnalysisDescription": "Yazınızın resmiyeti ve güveni hakkında geri bildirim alın.", "analyzeToneButton": "<PERSON><PERSON><PERSON>", "formalityLabel": "Re<PERSON><PERSON><PERSON><PERSON>", "confidenceLabel": "<PERSON><PERSON><PERSON>", "feedbackLabel": "<PERSON><PERSON>", "writeSomeTextToAnalyzePlaceholder": "Tonunu analiz etmek için düzenleyiciye biraz metin yazın.", "aiTextGenerationAccordionTitle": "Yapay Zeka İçerik Üretimi", "aiTextGenerationTitle": "Yapay Zeka İçerik Üretici", "aiTextGenerationDescription": "İsteminize göre içerik üretin.", "yourPromptLabel": "İsteminiz", "promptPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> keşfeden bir robot hakkında kısa bir hikaye yazın", "generatedTextLabel": "<PERSON><PERSON><PERSON><PERSON>", "generateTextButton": "<PERSON><PERSON>", "settingsAccordionTitle": "<PERSON><PERSON><PERSON>", "settingsTitle": "<PERSON><PERSON><PERSON>", "settingsDescription": "LinguaFlow deneyiminizi özelleştirin.", "themeLabel": "<PERSON><PERSON>", "switchToLightMode": "Açık Moda Geç", "switchToDarkMode": "Koyu Moda Geç", "languageLabel": "Dil", "selectLanguagePlaceholder": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "englishUSLanguage": "İngilizce (ABD)", "englishUKLanguage": "İngilizce (BK)", "arabicLanguage": "العربية (Arabic)", "turkishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Turkish)", "spanishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Spanish)", "germanLanguage": "<PERSON><PERSON><PERSON> (German)", "frenchLanguage": "<PERSON><PERSON><PERSON> (French)", "dutchLanguage": "<PERSON><PERSON><PERSON> (Dutch)", "italianLanguage": "<PERSON><PERSON> (Italian)", "startWritingPlaceholder": "<PERSON><PERSON>ya yazmaya ba<PERSON>n...", "rephrasePopoverTitle": "Metni Yeniden İfade Et", "rephrasePopoverDescription": "Seçili metniniz için ö<PERSON>iyi inceleyin.", "originalTextLabel": "Orijinal", "suggestionTextLabel": "<PERSON><PERSON><PERSON>", "rephraseWaitMessage": "\"Yeniden İfade Et\"e tıklayın veya öneriyi bekleyin.", "applyButton": "<PERSON><PERSON><PERSON><PERSON>", "cancelButton": "İptal", "siteHeaderTitle": "LinguaFlow", "footerText": "Müh AZA7 tarafından oluşturuldu©2025. Tüm hakları saklıdır®. Desteğiniz ve takdiriniz büyük takdir görmektedir.", "toastInputRequiredTitle": "<PERSON><PERSON><PERSON>", "toastEditorEmptyError": "Düzenleyici boş. Lütfen analiz etmek için biraz metin yazın.", "toastPromptRequiredError": "Lütfen metin üretmek için bir istem girin.", "toastSuccessTitle": "Başarılı", "toastErrorTitle": "<PERSON><PERSON>", "toastInfoTitle": "<PERSON><PERSON><PERSON>", "toastTextGeneratedSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> üretildi.", "toastTextGenerationError": "Metin üretilemedi. Lütfen tekrar deneyin.", "toastToneAnalysisSuccess": "Ton analizi tamamlandı.", "toastToneAnalysisError": "Ton analizi yapılamadı. Lütfen tekrar deneyin.", "toastNothingToRephraseError": "Yeniden ifade edilecek bir şey yok", "toastSelectTextToRephraseError": "Lütfen düzenleyicide biraz metin seçin.", "toastSuggestionReady": "<PERSON><PERSON>i hazır", "toastRephraseError": "<PERSON>in yeniden ifade edilemedi. Lütfen tekrar deneyin.", "toastFileUploadedSuccess": "Dosya içeriği düzenleyiciye yüklendi.", "toastFileTypeNotSupportedError": "Dosya türü desteklenmiyor. Lütfen bir {{fileType}} dosyası yükleyin.", "plagiarismDetectionAccordionTitle": "İntihal Tespiti", "plagiarismDetectionTitle": "İntihal Tespiti", "plagiarismDetectionSettingsTitle": "İntihal Tespiti", "plagiarismDetectionDescription": "İçeriğinizi mevcut literatürle kasıtsız benzerlikler açısından titizlikle taramak üzere tasarlanmış intihal tespit aracımızı kullanarak bütünlüğünüzü koruyun ve yazınızda özgünlüğü sürdürmenize yardımcı olun.", "detectPlagiarismButton": "Metninde İntihal Tespiti", "originalityScoreLabel": "Özgünlük Puanı", "plagiarismReportLabel": "<PERSON><PERSON><PERSON>", "potentialSourcesFoundLabel": "Potansi<PERSON>l Kaynaklar Bulundu", "originalSourceLabel": "Orijinal Kaynak", "similarityScoreLabel": "Benzerlik Puanı", "toastPlagiarismDetectionSuccess": "İntihal kontrolü tamamlandı.", "toastPlagiarismDetectionError": "İntihal kontrolü başarısız oldu. Lütfen tekrar deneyin.", "writeSomeTextToDetectPlagiarismPlaceholder": "İntihal kontrolü için dü<PERSON>leyiciye biraz metin yazın.", "aiWritingDetectionAccordionTitle": "Yapay Zeka Yazı Tespiti", "aiWritingDetectionTitle": "Yapay Zeka Yazı Tespiti", "aiWritingDetectionDescription": "Metninizin yapay zeka tarafından üretilmiş olma <PERSON>ığını tahmin edin.", "detectAiWritingButton": "Metninde Yazısını Tespit Et", "probabilityAIWrittenLabel": "Yapay Zeka Tarafından Yazılma <PERSON>ığı", "aiWritingDetectionSummaryLabel": "<PERSON><PERSON><PERSON>", "toastAiWritingDetectionSuccess": "Yapay zeka yazı tespiti tamamlandı.", "toastAiWritingDetectionError": "Yapay zeka yazısı tespit edilemedi. Lütfen tekrar deneyin.", "writeSomeTextToDetectAiWritingPlaceholder": "Yapay zeka yazarlığını kontrol etmek için düzenleyiciye biraz metin yazın.", "writingSuggestionsTitle": "<PERSON><PERSON><PERSON><PERSON>", "analyzingTextDescription": "Yapay zeka metninizi öneriler için analiz ediyor...", "suggestionsFoundDescription": "{{count}} öneri bulundu. Aşağıda inceleyebilirsiniz.", "noSuggestionsFoundDescription": "<PERSON>men bir öneri bulunamadı. Yazmaya devam edin veya yeniden ifade etmeyi deneyin.", "startTypingForSuggestionsDescription": "Daha iyi yazma önerileri için yazmaya başlayın.", "suggestionTypeSpelling": "Yazım", "suggestionTypeGrammar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestionTypeRewrite": "Yeniden Yaz", "suggestionTypeStyle": "Stil", "suggestionTypeUnknown": "<PERSON><PERSON><PERSON>", "suggestionLabel": "Öneriyor", "applySuggestionButton": "<PERSON><PERSON><PERSON><PERSON>", "suggestionExplanationTooltip": "Açıklamayı gör", "toastTextAnalysisError": "Yazım önerileri alınamadı. Lütfen tekrar deneyin.", "toastSuggestionAppliedSuccess": "Öneri uygulandı.", "toastSuggestionApplyError": "Öneri uygulanamadı. Orijinal metin değişmiş olabilir.", "humanizeAiTextAccordionTitle": "Yapay Zeka Metnini İnsanileştir", "humanizeAiTextTitle": "Yapay Zeka Metnini İnsanileştir", "humanizeAiTextDescription": "Yapay zeka tarafından üretilen metni daha insansı bir şekilde yeniden yazın.", "humanizeTextButton": "<PERSON><PERSON>", "humanizedTextLabel": "İnsanileştirilmiş Metin", "toastHumanizeTextSuccess": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> insanileştirildi.", "toastHumanizeTextError": "<PERSON>in insanileştirilemedi. Lütfen tekrar deneyin.", "writeSomeTextToHumanizePlaceholder": "İnsanileştirmek için düzenleyiciye yapay zeka tarafından üretilmiş metin yazın veya yapıştırın.", "clearEditorButton": "<PERSON><PERSON><PERSON>", "clearEditorButtonAriaLabel": "Düzenleyicideki tüm metni temizle", "toastEditorClearedSuccess": "Düzenleyici içeriği temizlendi.", "generationHistoryTitle": "<PERSON><PERSON><PERSON>i", "noGenerationsYetPlaceholder": "Henüz üretim yok. Burada görmek için biraz metin üretin.", "promptLabel": "İstem", "outputLabel": "Çıktı", "useThisPromptButton": "<PERSON><PERSON><PERSON>", "copyOutputButton": "Çıktıyı Kopyala", "toastPromptRestoredSuccess": "İstem giriş alanına geri yü<PERSON>.", "toastTextCopiedSuccess": "<PERSON><PERSON> panoya kopyalandı.", "toastTextCopyError": "<PERSON><PERSON> panoya kopyalanamadı.", "insertIntoEditorButton": "Düzenleyiciye Ekle", "insertIntoEditorButtonTooltip": "Üretilen metni düzenleyiciye ekle", "toastTextInsertedSuccess": "Üretilen metin düzenleyiciye eklendi.", "copyEditorButton": "<PERSON><PERSON>", "copyEditorButtonAriaLabel": "Düzenleyicideki tüm metni k<PERSON>ala", "toastEditorContentCopiedSuccess": "Düzenleyici içeriği panoya kopyalandı.", "toastEditorContentCopyError": "Düzenleyici içeriği panoya kopyalanamadı.", "toastEditorEmptyForCopyError": "Düzenleyici boş. Kopyalanacak bir şey yok.", "recordVoiceButtonStart": "<PERSON><PERSON>", "recordVoiceButtonStop": "Kaydı Durdur", "recordVoiceButtonAriaLabelStart": "<PERSON>ne dönüştürmek için ses kaydını başlat", "recordVoiceButtonAriaLabelStop": "<PERSON><PERSON> kaydını durdur", "toastRecordingStarted": "<PERSON><PERSON>t başladı. Mikrofonunuza konuşun.", "toastRecordingStoppedNoTranscript": "<PERSON><PERSON>t durdu. Hiçbir konuşma yazıya dökülmedi.", "toastSpeechTranscribedAndAppended": "Konuşma yazıya döküldü ve düzenleyiciye eklendi.", "toastSpeechRecognitionNotSupported": "Tarayıcınız konuşma tanımayı desteklemiyor.", "toastMicrophonePermissionDenied": "Mikrofon izni reddedildi. Lütfen tarayıcı ayarlarınızdan etkinleştirin ve sayfayı yenileyin.", "toastSpeechNoSpeechDetected": "Hiçbir konuşma algılanmadı. Lütfen tekrar deneyin.", "toastSpeechAudioCaptureError": "Ses yakalama hatası. Lütfen mikrofonunuzu kontrol edin.", "toastSpeechNetworkError": "Konuşma tanıma sırasında ağ hatası. Lütfen bağlantınızı kontrol edin.", "toastSpeechRecognitionError": "Konuşma tanıma hatası: {{error}}", "toastSpeechServiceNotAllowed": "Konuşma tanıma hizmetine izin verilmiyor veya kullanılamıyor. Lütfen daha sonra tekrar deneyin.", "toastSpeechLanguageNotSupportedError": "Seçilen dil, tarayı<PERSON>ı<PERSON><PERSON>z tarafından konuşma tanıma için desteklenmiyor.", "helpTitle": "Yardım", "helpPanelTitle": "LinguaFlow Nasıl Kullanılır", "helpPanelDescription": "LinguaFlow özellikleriyle başlayın.", "helpPanelIntro": "LinguaFlow'a hoş geldiniz! <PERSON><PERSON>ı<PERSON>, uygulamanın güçlü yazma yardım araçlarında gezinmenize ve bunları kullanmanıza yardımcı olacaktır.", "helpEditorTitle": "D<PERSON><PERSON>leyici", "helpAiToolsTitle": "Yapay Zeka Araçları Paneli", "helpLanguageSettingsTitle": "<PERSON><PERSON>ı", "helpAppearanceSettingsTitle": "G<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "helpDictionarySettingsTitle": "Sözlük Ayarları", "helpFeatureSettingsTitle": "Özellik Ayarları", "helpWritingAidSettingsTitle": "Yazım Yardımı Ayarları", "helpAdvancedSettingsTitle": "Gelişmiş <PERSON>", "helpEditorDescription": "Düzenleyici ana çalışma alanınızdır.<br/><br/><b>- G<PERSON>ç<PERSON> zamanlı Öneriler:</b> <PERSON><PERSON>, uygulama metninizi otomatik olarak kontrol eder ve potansiyel sorunların altını çizer. Bir düzeltme açılır penceresi görmek için vurgulanan bir bölüme tıklayın.<br/><b>- Seçimde Yapay Zeka Araçları:</b> <b>Akıllı Eş Anlamlılar</b> düğmesini getirmek için herhangi bir metin parçasını seçin. Tek bir kelime seçerseniz, eş anlamlılar ve bir telaffuz kılavuzu alırsınız. Daha uzun bir ifade seçerseniz, yapay zeka destekli bir yeniden ifade etme önerisi alırsınız.<br/><b>- Biçimlendirme Araç Çubuğu:</b> Düzenleyicinin üst kısmında, <PERSON><PERSON>/<PERSON>, yazı tiplerini değiştirme ve Kalın, <PERSON><PERSON>ik, listeler ve metin hizalama gibi biçimlendirmeleri uygulama araçlarını bulacaksınız.", "helpAiToolsDescription": "Sol ve sağdaki paneller g<PERSON><PERSON><PERSON><PERSON> yapay zeka yetenekleri sağlar.<br/><br/><b>- <PERSON><PERSON><PERSON> (Sol):</b> Burada yapay zekanın stilini etkilemek için <b><PERSON><PERSON><PERSON></b>'nu değiştirebilir, bir <b>Belge İçe Aktarabilir</b> veya tüm metninizi yeniden tasarlamak için <b>Yapay Zeka Yeniden Yazıcı</b>'yı kullanabilirsiniz. Ayrıca <b>Yapay Zeka Metnini İnsanileştirme</b>, <b>Yapay Zeka Yazısını</b> kontrol etme ve <b>İntihal</b> tespit etme araçlarını da kullanabilirsiniz.<br/><b>- <PERSON><PERSON><PERSON> (Sağ):</b> Bu sütun gerçek zamanlı <b><PERSON><PERSON><PERSON>sta<PERSON></b> gösterir, met<PERSON><PERSON> i<PERSON> bir <b><PERSON><PERSON></b> sağlar ve bir istemden yeni metin oluşturmak için <b>Yapay Zeka İçerik Üreticisini</b> içerir. Üretim geçmişiniz kolay yeniden kullanım için burada kaydedilir.", "helpLanguageSettingsDescription": "Uygulama arayüzü için <b>Kullanıcı Arayüzü Dili</b>'ni ve analiz için <b>Birinc<PERSON></b>'ni yapılandırın. Bazı diller için bir <b>B<PERSON>lgesel <PERSON>çe</b> de seçebilirsiniz. Uygulamanın siz yazarken yazma dillerini değiştirmesi için <b>Otomatik Dil Algılama</b>'yı etkinleştirin.", "helpAppearanceSettingsDescription": "Uygulamanın görünümünü ve hissini özelleştirin. Bir <b><PERSON><PERSON></b> (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> veya Sistem) se<PERSON><PERSON>, genel <b>Yazı Tipi Boyutu</b>'nu ayarlayın ve daha iyi okunabilirlik için <b>Yüksek Kontrast Modu</b>'nu açıp kapatın.", "helpDictionarySettingsDescription": "Farklı diller için kişisel sözlüklerinizi yönetin. Sık kullandığınız ancak yazım hatası olarak işaretlenebilecek kelimeleri (isimler veya teknik jargon gibi) <b>ekleyin</b>. Sözlük listenizi bir JSON dosyası olarak <b>içe aktarabilir</b> veya <b>dışa aktarabilir</b> veya belirli bir dil için sözlüğü <b>temizleyebilirsiniz</b>.", "helpFeatureSettingsDescription": "Belirli yapay zeka ve otomatik düzeltme özelliklerinin davranışını ince ayar yapın. Burada çeşitli üretken yapay zeka araçlarını, otomatik düzeltme davranışlarını ve iş akışınıza uyacak şekilde temel gerçek zamanlı kontrol işlevlerini açıp kapatabilirsiniz.", "helpWritingAidSettingsDescription": "Yapay zekanın size nasıl yardımcı olacağını özelleştirin. Beceri seviyenize uygun öneriler almak için <b><PERSON><PERSON> Yeterliliğinizi</b> ayarlayın. Ayrıca <b>Ton Tespiti</b>, <b>İntihal Tespiti</b> gibi özellikleri ve <b>Anadili Olmayan Konuşmacılar</b> için özel desteği etkinleştirebilir veya devre dışı bırakabilirsiniz.", "helpAdvancedSettingsDescription": "Temel operasyonel davranışları kontrol edin. İnternet bağlantısı olmadan temel özellikleri kullanmak için <b>Çevrimdışı İşlevselliği</b> etkinleştirin. Yeni bir başlangıç yapmak isterseniz, uygulamayı orijinal varsayılanlarına geri yüklemek için <b>Tüm Ayarları Sıfırla</b>'yabilirsiniz (bu işlem geri alınamaz).", "helpPanelTip": "Yazma stilinize ve ihtiyaçlarınıza en uygun olanı bulmak için farklı araçlar ve ayarlarla denemeler yapın!", "Write Tools": "<PERSON><PERSON><PERSON>", "Import Document": "Belge İçe Aktar", "Quick Action": "Hızlı İşlem", "Tone Analyzer": "<PERSON><PERSON>", "aiRewriteAccordionTitle": "Yapay Zeka Yeniden Yazıcı", "aiRewriteTitle": "Yapay Zeka Yeniden Yazıcı", "aiRewriteDescription": "Netliği ve stili geliştirmek için tüm düzenleyici içeriğini yeniden yazın.", "rewriteEditorContentButton": "Düzenleyici İçeriğini Yeniden Yaz", "rewrittenTextLabel": "Yeniden Yazılmış Metin", "applyToEditorButton": "Düzenleyiciye Uygula", "toastRewriteSuccess": "Düzenleyici içeriği başarıyla yeniden yazıldı.", "toastRewriteError": "Düzenleyici içeriği yeniden yazılamadı. Lütfen tekrar deneyin.", "writeSomeTextToRewritePlaceholder": "Yeniden yazmak için dü<PERSON>leyiciye biraz metin yazın.", "Click the button to rewrite the editor content.": "Düzenleyici içeriğini yeniden yazmak için düğmeye tıklayın.", "dropzoneInstruction": "Dosyaları buraya bırakın veya göz atın", "toastFileImportSuccessTitle": "Dosya İçe Aktarıldı", "toastFileImportSuccessMessage": "Belge içeriği yüklendi.", "toastFileImportErrorTitle": "İçe Aktarma Hatası", "toastFileImportErrorMessage": "Dosya içeriği okunamadı. Lütfen geçerli bir .txt dosyası olduğundan emin olun.", "toastInvalidFileTypeMessage": "Geçersiz dosya türü. Yalnızca .txt dosyaları kabul edilir.", "dropzoneAriaLabel": "Belge içe aktarma bırakma alanı: Yüklemek için bir .txt dosyasına tıklayın veya sürükleyip bırakın.", "featuresLabel": "<PERSON><PERSON><PERSON><PERSON>", "featureSettingsDescription": "Belirli yazma yardım özelliklerinin işlevselliğini özelleştirin.", "appearanceLabel": "G<PERSON>rü<PERSON><PERSON><PERSON>", "writingAidLabel": "Yaz<PERSON>m Yardımı", "dictionaryLabel": "Sözlük", "dictionarySettingsDescription": "<PERSON><PERSON> kelimeler ekleyin veya kişisel sözlükleri yö<PERSON>in. (<PERSON><PERSON> tutuc<PERSON>)", "advancedSettingsLabel": "Gelişmiş", "advancedSettingsDescription": "Gelişmiş yapılandırma seçeneklerine eri<PERSON>. <PERSON>kka<PERSON><PERSON> kull<PERSON>ı<PERSON>. (<PERSON><PERSON> tutucu)", "uiLanguageLabel": "Arayüz <PERSON>", "selectUiLanguagePlaceholder": "Arayüz dili seçin...", "uiLanguageDescription": "Uygulama arayüzü<PERSON><PERSON><PERSON>ştirir.", "writingLanguageLabel": "<PERSON><PERSON><PERSON><PERSON>", "selectWritingLanguagePlaceholder": "<PERSON><PERSON><PERSON> di<PERSON> se<PERSON>...", "writingLanguageDescription": "Yapay zeka analizi ve üretimi için birincil dili a<PERSON>.", "regionalDialectLabel": "B<PERSON>lgesel Le<PERSON>ç<PERSON>", "selectRegionalDialectPlaceholder": "<PERSON><PERSON><PERSON><PERSON> seç<PERSON>...", "regionalDialectDescription": "Seçilen yazma dili için bölge<PERSON> varyasyonu belirtir.", "languageProficiencyLabel": "<PERSON><PERSON>", "selectProficiencyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> seçin...", "languageProficiencyDescription": "Yapay zekanın önerileri dil beceri seviyenize göre uyarlamasına yardımcı olur.", "proficiencyNative": "<PERSON><PERSON><PERSON>", "proficiencyAdvanced": "İleri (C1/C2)", "proficiencyIntermediate": "Orta (B1/B2)", "proficiencyBeginner": "Başlangıç (A1/A2)", "languageEnglishGeneral": "İngilizce", "languageSpanishGeneral": "İspanyolca", "languageFrenchGeneral": "Fransızca", "languageGermanGeneral": "Almanca", "languageItalianGeneral": "İtalyanca", "languageDutchGeneral": "Felemenkçe", "languageArabicGeneral": "<PERSON><PERSON><PERSON>", "arabicSyriaLanguage": "Arap<PERSON> (Suriye)", "arabicSaudiArabiaLanguage": "Arapça (Suudi Arabistan)", "arabicEgyptLanguage": "Arapça (Mısır)", "languageTurkishGeneral": "Türkçe", "spanishSpainLanguage": "İspanyolca (İspanya)", "spanishMexicoLanguage": "İspanyolca (Meksika)", "themeLight": "Açık", "themeDark": "<PERSON><PERSON>", "themeSystem": "Sistem Varsayılanı", "selectThemePlaceholder": "<PERSON><PERSON>...", "themeDescription": "Uygulamanın görsel temasını seçin.", "fontSizeLabel": "Yazı Tipi Boyutu", "selectFontSizePlaceholder": "Yazı tipi boyutu seçin...", "fontSizeSmall": "Küçük", "fontSizeMedium": "Orta", "fontSizeLarge": "Büyük", "fontSizeDescription": "<PERSON>y<PERSON><PERSON>a genelinde metin boyutunu a<PERSON>.", "highContrastModeLabel": "Yüksek Kontrast Modu", "highContrastModeDescription": "Daha iyi okunabilirlik için metin/arka plan kontrastını artırır.", "enabledLabel": "<PERSON><PERSON><PERSON>", "disabledLabel": "Devre Dışı", "personalDictionaryLabel": "Kişisel Sözlük", "personalDictionaryDescription": "Sık kullandığınız ve hata olarak işaretlenebilecek kelimeleri ekleyin.", "addWordPlaceholder": "Bir kelime girin...", "addWordButton": "<PERSON><PERSON><PERSON>", "deleteWordButtonAria": "{{word}} k<PERSON><PERSON><PERSON> sil", "dictionaryEmptyPlaceholder": "Sözlüğünüz boş. Biraz kelime e<PERSON>!", "dictionaryImportExportLabel": "Sözlüğü İçe / Dışa Aktar", "importDictionaryButton": "İçe Aktar", "exportDictionaryButton": "Dışa Aktar", "dictionaryImportExportDescription": "Kişisel sözlüğünüzü JSON dosyası olarak yedekleyin veya paylaşın.", "clearDictionaryForLanguageButton": "{{language}} Sözlüğünü Temizle", "clearDictionaryConfirmTitle": "Emin misiniz?", "clearDictionaryForLanguageConfirmDescription": "<PERSON>u, ki<PERSON><PERSON><PERSON> sözlüğünüzdeki tüm kelimeleri {{language}} dili için kalıcı olarak silecektir. Bu işlem geri alınamaz.", "confirmClearButton": "<PERSON><PERSON>, Sözlüğü Temizle", "clearDictionaryWarning": "Bu işlem geri alınamaz.", "toastDictionaryWordAdded": "'{{word}}' k<PERSON><PERSON><PERSON> s<PERSON>lü<PERSON>e eklendi.", "toastDictionaryWordExists": "'{{word}}' keli<PERSON>i zaten sözlükte mevcut.", "toastDictionaryWordEmpty": "Boş kelime eklenemez.", "toastDictionaryWordDeleted": "'{{word}}' keli<PERSON><PERSON> sözlükten silindi.", "toastDictionaryImportOverwriteSuccess": "{{count}} k<PERSON><PERSON> i<PERSON> aktarıldı, sözlük üzerine yazıldı.", "toastDictionaryImportMergeSuccess": "{{count}} yeni kelime içe aktarıldı ve birleştirildi.", "toastDictionaryImportInvalidFormat": "Geçersiz sözlük dosyası formatı. Bir JSON dizesi dizisi olmalıdır.", "toastDictionaryImportError": "Sözlük dosyası içe aktarılırken hata oluştu.", "toastDictionaryExportSuccess": "Sözlük başarıyla dışa aktarıldı.", "toastDictionaryCleared": "Kişisel sözlük temizlendi.", "generativeAiFeaturesLabel": "Üretken YZ Özellikleri", "showAiSuggestionsOnTextSelectionLabel": "<PERSON>in Seçiminde YZ Önerilerini Göster", "showAiSuggestionsOnTextSelectionDescription": "<PERSON>in seçtiğinizde YZ önerilerini etkinleştirin.", "quickAiActionsForSelectedTextLabel": "Seçilen Metin için Hızlı YZ İşlemleri", "quickAiActionsForSelectedTextDescription": "Vurgulanan metin için hızlı YZ eylemlerine erişin.", "aiQuickReplySuggestionsLabel": "YZ Tabanlı Hızlı Yanıt Önerileri", "aiQuickReplySuggestionsDescription": "Yanıtlar için fi<PERSON> alın. (Taklit özellik)", "viewAndReuseRecentPromptHistoryLabel": "Son Komut İstemi Geçmişini Görüntüleme ve Yeniden Kullanma", "viewAndReuseRecentPromptHistoryDescription": "Son YZ komutlarınıza kolayca erişin.", "autoCorrectionFeaturesLabel": "Otomatik Düzeltme Özellikleri", "automaticTextCorrectionLabel": "Otomatik Met<PERSON>", "automaticTextCorrectionDescription": "Yaygın yazım hatalarını ve gramer hatalarını otomatik olarak düzeltin.", "realTimeCorrectionLabel": "Gerçek Zamanlı Düzeltme", "realTimeCorrectionDescription": "Yazarken düzeltmeleri uygulayın.", "sentenceEnhancementLabel": "<PERSON><PERSON><PERSON><PERSON> Güçlendirme", "sentenceEnhancementDescription": "Netlik ve stilinizi iyileştirmek için öneriler alın.", "showCorrectionFeedbackLabel": "Düzeltme Geri Bildirimi Göster", "showCorrectionFeedbackDescription": "Düzeltmeler uygulandığında bildirim alın.", "coreCheckingFeaturesLabel": "Temel Kontrol <PERSON>", "realTimeGrammarCheckingLabel": "Gerçek Zamanlı Gramer Denetimi", "realTimeGrammarCheckingDescription": "Yazarken gramer konusunda anında geri bildirim alın.", "realTimeSpellCheckingLabel": "Gerçek Zamanlı Yazım Denetimi", "realTimeSpellCheckingDescription": "Ya<PERSON><PERSON>m hatalarını gerçek zamanlı olarak kontrol edin.", "styleSuggestionsLabel": "<PERSON><PERSON>", "styleSuggestionsDescription": "Ya<PERSON><PERSON>m tarzınızı geliştirmek için öneriler alın.", "toastLanguageSwitched": "<PERSON><PERSON><PERSON> dili otomatik olarak {{language}} di<PERSON>.", "writingAssistanceTitle": "Yazma Yardımı", "writingAssistanceDescription": "LinguaFlow ile yazma den<PERSON>zi, size nasıl yardımcı olacağını özelleştirerek geliştirin.", "yourLanguageProficiencyTitle": "<PERSON><PERSON> Yeterliliğiniz (Birincil Dil için)", "yourLanguageProficiencyDescription": "Dil konusundaki sofistike anlayışınıza uygun, titizlikle hazırlanmış öneriler alarak yazınıza daha incelikli bir yaklaşım sağlayın.", "toneDetectionTitle": "Ton <PERSON>", "toneDetectionDescription": "Metninizin duygusal alt tonlarını ton tespit özelliğimizle inceleyin; bu özellik yazınızı analiz eder ve tonunuzu iyileştirip yükseltmek için anlayışlı öneriler sunarak hedef kitlenizle daha etkili bir şekilde rezonans kurmanızı sağlar.", "nonNativeSupportTitle": "<PERSON><PERSON><PERSON>ı<PERSON> için <PERSON>", "nonNativeSupportDescription": "Anadili olmayan konuşmacılara yönelik özel yardımdan yararlanın; akıcılığınızı ve yazma konusundaki güveninizi artırmak için düşünceli rehberlik ve pratik ipuçları sunar.", "advancedSettingsTitle": "Gelişmiş <PERSON>", "enableOfflineFunctionalityLabel": "Çevrimdışı İşlevselliği Etkinleştir", "enableOfflineFunctionalityDescription": "Ayarlar ve sözlük dahil olmak üzere temel özellikler çevrimdışı kullanılabilir. Programın sorunsuz çalışmasını sağlamak için anahtar bileşenler dahil edilecektir.", "enableAutomaticLanguageDetectionLabel": "Otomatik Dil Algılamayı Etkinleştir", "enableAutomaticLanguageDetectionDescription": "Yazarken yazma dilini otomatik olarak algıla ve değiştir.", "dataManagementLabel": "<PERSON><PERSON>", "resetAllSettingsLabel": "Tüm Ayarları Sıfırla", "resetAllSettingsDescription": "<PERSON><PERSON>, <PERSON><PERSON>, dil ve özellik ayarları dahil tüm özelleştirmeleri varsayılanlarına sıfırlayacaktır. Bu işlem geri alınamaz.", "resetButtonLabel": "Sıfırla", "resetAllSettingsConfirmTitle": "Tüm ayarları sıfırlamak istediğinizden emin misiniz?", "resetAllSettingsConfirmDescription": "<PERSON>üm ki<PERSON><PERSON>l <PERSON>, sözlük kelimeleriniz ve tercihleriniz kalıcı olarak silinecek ve uygulama varsayılanlarına sıfırlanacaktır. Bu işlem geri alınamaz.", "confirmResetButton": "<PERSON><PERSON>, Her Şeyi Sıfırla", "toastResetSuccess": "<PERSON><PERSON>m a<PERSON> var<PERSON>ılana sı<PERSON>ırlandı. Uygulama şimdi yeniden yüklenecek.", "dictionaryLanguageLabel": "Sözlük Dili", "selectDictionaryLanguagePlaceholder": "Görüntülenecek dili seçin...", "dictionaryLanguageDescription": "Belirli bir dil i<PERSON>in sözlüğü görüntüleyin ve yönetin.", "toastSuggestionDismissed": "<PERSON><PERSON><PERSON> reddedil<PERSON>.", "dismissButton": "<PERSON><PERSON>", "correctButton": "<PERSON><PERSON><PERSON><PERSON>", "undoButton": "<PERSON><PERSON>", "redoButton": "İleri Al", "aiToolsButton": "Akıllı Eş Anlamlılar", "wordToolkitTitle": "Eş Anlamlı Önerisi", "wordToolkitDescription": "Eş anlamlılar almak ve telaffuzunu duymak için düzenleyicide tek bir kelime seçin.", "wordToolkitPlaceholder": "Akıllı eş anlamlılar almak için ana düzenleyicide tek bir kelime seçin.", "selectedWordLabel": "Seç<PERSON>", "synonymsLabel": "<PERSON><PERSON>ı<PERSON>", "noSynonymsFound": "Eş anlamlı bulunamadı.", "applySynonymTooltip": "<PERSON><PERSON><PERSON><PERSON> '{{synonym}}' il<PERSON>", "toastWordToolkitError": "<PERSON><PERSON><PERSON> kelime için <PERSON>iler alınamadı.", "toastWordReplacedSuccess": "<PERSON><PERSON><PERSON> '{{word}}' il<PERSON>.", "wordToolkitPopoverDescription": "<PERSON>ş anlamlılar alın veya kelimenin telaffuzunu dinleyin.", "spellingAndPronunciationLabel": "Telaffuz", "pronounceButton": "Kelimeyi Telaffuz Et", "toastPronunciationError": "Sesli telaffuz üretilemedi.", "toastEmptyText": "Lütfen önce biraz metin girin."}